'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface FloatingActionButtonProps {
  onClick: () => void;
  icon: React.ReactNode;
  label?: string;
  className?: string;
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  size?: 'sm' | 'md' | 'lg';
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({
  onClick,
  icon,
  label,
  className = '',
  variant = 'primary',
  size = 'md'
}) => {
  const variantClasses = {
    primary: 'from-blue-500 via-blue-600 to-teal-500',
    secondary: 'from-gray-500 via-gray-600 to-gray-700',
    success: 'from-emerald-500 via-emerald-600 to-green-500',
    warning: 'from-amber-500 via-amber-600 to-orange-500',
    danger: 'from-red-500 via-red-600 to-pink-500'
  };

  const sizeClasses = {
    sm: 'w-12 h-12',
    md: 'w-16 h-16',
    lg: 'w-20 h-20'
  };

  const iconSizeClasses = {
    sm: 'w-5 h-5',
    md: 'w-6 h-6',
    lg: 'w-8 h-8'
  };

  return (
    <motion.div
      className="fixed bottom-8 right-8 z-50"
      initial={{ scale: 0, rotate: -180 }}
      animate={{ scale: 1, rotate: 0 }}
      transition={{ 
        type: "spring", 
        stiffness: 200, 
        damping: 15,
        delay: 0.5 
      }}
    >
      <motion.button
        className={`${sizeClasses[size]} bg-gradient-to-br ${variantClasses[variant]} text-white rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden group ${className}`}
        onClick={onClick}
        whileHover={{ 
          scale: 1.1, 
          rotate: 5,
          boxShadow: '0 25px 50px rgba(0, 102, 204, 0.4)'
        }}
        whileTap={{ scale: 0.9 }}
        style={{
          filter: 'drop-shadow(0 10px 20px rgba(0, 102, 204, 0.3))'
        }}
      >
        {/* Background Glow Effect */}
        <motion.div
          className="absolute inset-0 bg-white/20 rounded-full"
          initial={{ scale: 0 }}
          whileHover={{ scale: 1.2 }}
          transition={{ duration: 0.3 }}
        />
        
        {/* Ripple Effect */}
        <motion.div
          className="absolute inset-0 bg-white/10 rounded-full"
          animate={{
            scale: [1, 1.5, 1],
            opacity: [0.5, 0, 0.5],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        
        {/* Icon */}
        <motion.div
          className={`relative z-10 ${iconSizeClasses[size]}`}
          whileHover={{ rotate: 15 }}
          transition={{ duration: 0.2 }}
        >
          {icon}
        </motion.div>
        
        {/* Shine Effect */}
        <motion.div
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full"
          initial={{ x: '-100%' }}
          whileHover={{ x: '100%' }}
          transition={{ duration: 0.6 }}
        />
      </motion.button>
      
      {/* Tooltip */}
      {label && (
        <motion.div
          className="absolute right-full top-1/2 transform -translate-y-1/2 mr-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
          initial={{ x: 20, opacity: 0 }}
          whileHover={{ x: 0, opacity: 1 }}
        >
          <div className="bg-gray-900 text-white text-sm font-medium px-3 py-2 rounded-lg shadow-lg whitespace-nowrap">
            {label}
            <div className="absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-l-gray-900 border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default FloatingActionButton;
