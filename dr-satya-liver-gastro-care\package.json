{"name": "dr-satya-liver-gastro-care", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-sdk/client-s3": "^3.859.0", "@aws-sdk/s3-request-presigner": "^3.859.0", "@headlessui/react": "^2.2.7", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@supabase/supabase-js": "^2.53.0", "@types/pg": "^8.15.5", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "date-fns": "^4.1.0", "dotenv": "^17.2.1", "framer-motion": "^12.23.12", "lucide-react": "^0.536.0", "next": "15.4.5", "pg": "^8.16.3", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.5.2", "recharts": "^3.1.1", "uuid": "^11.1.0", "zod": "^4.0.14", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/uuid": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.4.5", "tailwindcss": "^4", "typescript": "^5"}}