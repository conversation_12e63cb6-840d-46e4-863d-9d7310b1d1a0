'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  UserIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  MapPinIcon,
  ScaleIcon,
  HeartIcon,
  DocumentTextIcon,
  CameraIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { InfoCard } from '@/components/ui/Card';
import { ButtonLoading } from '@/components/ui/Loading';

// Validation schema
const patientSchema = z.object({
  // Basic Information
  fullName: z.string().min(2, 'Full name must be at least 2 characters'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  gender: z.enum(['Male', 'Female', 'Other'], { required_error: 'Gender is required' }),
  aadharNumber: z.string().regex(/^\d{12}$/, 'Aadhar number must be exactly 12 digits'),
  mobileNumber: z.string().regex(/^\d{10}$/, 'Mobile number must be exactly 10 digits'),
  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  
  // Physical Information
  heightCm: z.number().min(50, 'Height must be at least 50 cm').max(250, 'Height must be less than 250 cm'),
  weightKg: z.number().min(10, 'Weight must be at least 10 kg').max(300, 'Weight must be less than 300 kg'),
  bloodGroup: z.enum(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'], { required_error: 'Blood group is required' }),
  
  // Medical Scores
  charlsonIndex: z.number().min(0).max(37),
  asaGrade: z.number().min(1).max(6),
  ecogGrade: z.number().min(0).max(5),
  
  // Comorbidities
  comorbidities: z.array(z.string()).optional(),
});

type PatientFormData = z.infer<typeof patientSchema>;

const NewPatientPage: React.FC = () => {
  const [currentSection, setCurrentSection] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedComorbidities, setSelectedComorbidities] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      charlsonIndex: 0,
      asaGrade: 1,
      ecogGrade: 0,
      comorbidities: [],
    },
  });

  const watchHeight = watch('heightCm');
  const watchWeight = watch('weightKg');

  // Calculate BMI
  const calculateBMI = () => {
    if (watchHeight && watchWeight) {
      const heightInMeters = watchHeight / 100;
      const bmi = watchWeight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return null;
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const mockComorbidities = [
    { id: '1', name: 'Diabetes Mellitus', category: 'Endocrine' },
    { id: '2', name: 'Hypertension', category: 'Cardiovascular' },
    { id: '3', name: 'Heart Disease', category: 'Cardiovascular' },
    { id: '4', name: 'Chronic Kidney Disease', category: 'Renal' },
    { id: '5', name: 'Liver Disease', category: 'Hepatic' },
    { id: '6', name: 'COPD', category: 'Respiratory' },
    { id: '7', name: 'Asthma', category: 'Respiratory' },
    { id: '8', name: 'Obesity', category: 'Metabolic' },
  ];

  const sections = [
    {
      title: 'Basic Information',
      icon: <UserIcon className="w-5 h-5" />,
      description: 'Personal details and contact information',
    },
    {
      title: 'Physical Information',
      icon: <ScaleIcon className="w-5 h-5" />,
      description: 'Height, weight, and blood group',
    },
    {
      title: 'Medical Information',
      icon: <HeartIcon className="w-5 h-5" />,
      description: 'Medical scores and comorbidities',
    },
    {
      title: 'Review & Submit',
      icon: <DocumentTextIcon className="w-5 h-5" />,
      description: 'Review all information before submitting',
    },
  ];

  const onSubmit = async (data: PatientFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Patient data:', { ...data, comorbidities: selectedComorbidities });
      toast.success('Patient registered successfully!');
      
      // Reset form or redirect
    } catch (error) {
      toast.error('Failed to register patient. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const nextSection = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const prevSection = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="max-w-4xl mx-auto space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Add New Patient
        </h1>
        <p className="text-gray-600">
          Register a new patient in the system with comprehensive medical information.
        </p>
      </motion.div>

      {/* Progress Steps */}
      <motion.div className="medical-card p-6" variants={itemVariants}>
        <div className="flex items-center justify-between">
          {sections.map((section, index) => (
            <div
              key={index}
              className={`flex items-center ${index < sections.length - 1 ? 'flex-1' : ''}`}
            >
              <div className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    index <= currentSection
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}
                >
                  {index < currentSection ? (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    section.icon
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    index <= currentSection ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {section.title}
                  </p>
                  <p className="text-xs text-gray-500">{section.description}</p>
                </div>
              </div>
              {index < sections.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 ${
                  index < currentSection ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
      </motion.div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <motion.div variants={itemVariants}>
          {/* Section 0: Basic Information */}
          {currentSection === 0 && (
            <InfoCard title="Basic Information" icon={<UserIcon className="w-5 h-5" />}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="form-label">Full Name *</label>
                  <input
                    {...register('fullName')}
                    className={`form-input ${errors.fullName ? 'error' : ''}`}
                    placeholder="Enter full name"
                  />
                  {errors.fullName && (
                    <p className="mt-1 text-sm text-red-600">{errors.fullName.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Date of Birth *</label>
                  <input
                    {...register('dateOfBirth')}
                    type="date"
                    className={`form-input ${errors.dateOfBirth ? 'error' : ''}`}
                  />
                  {errors.dateOfBirth && (
                    <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth.message}</p>
                  )}
                  {watch('dateOfBirth') && (
                    <p className="mt-1 text-sm text-blue-600">
                      Age: {calculateAge(watch('dateOfBirth'))} years
                    </p>
                  )}
                </div>

                <div>
                  <label className="form-label">Gender *</label>
                  <select
                    {...register('gender')}
                    className={`form-input ${errors.gender ? 'error' : ''}`}
                  >
                    <option value="">Select gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                  {errors.gender && (
                    <p className="mt-1 text-sm text-red-600">{errors.gender.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Aadhar Number *</label>
                  <input
                    {...register('aadharNumber')}
                    className={`form-input ${errors.aadharNumber ? 'error' : ''}`}
                    placeholder="Enter 12-digit Aadhar number"
                    maxLength={12}
                  />
                  {errors.aadharNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.aadharNumber.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Mobile Number *</label>
                  <input
                    {...register('mobileNumber')}
                    className={`form-input ${errors.mobileNumber ? 'error' : ''}`}
                    placeholder="Enter 10-digit mobile number"
                    maxLength={10}
                  />
                  {errors.mobileNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.mobileNumber.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Email Address</label>
                  <input
                    {...register('email')}
                    type="email"
                    className={`form-input ${errors.email ? 'error' : ''}`}
                    placeholder="Enter email address (optional)"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <div className="md:col-span-2">
                  <label className="form-label">Address *</label>
                  <textarea
                    {...register('address')}
                    className={`form-input ${errors.address ? 'error' : ''}`}
                    placeholder="Enter complete address"
                    rows={3}
                  />
                  {errors.address && (
                    <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                  )}
                </div>
              </div>
            </InfoCard>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-6">
            <button
              type="button"
              onClick={prevSection}
              disabled={currentSection === 0}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            {currentSection < sections.length - 1 ? (
              <button
                type="button"
                onClick={nextSection}
                className="btn-primary"
              >
                Next
              </button>
            ) : (
              <ButtonLoading
                loading={isLoading}
                className="btn-primary"
                disabled={isLoading}
              >
                {isLoading ? 'Registering...' : 'Register Patient'}
              </ButtonLoading>
            )}
          </div>
        </motion.div>
      </form>
    </motion.div>
  );
};

export default NewPatientPage;
