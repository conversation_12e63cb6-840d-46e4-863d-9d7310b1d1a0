# Database Configuration (NeonDB)
DATABASE_URL="postgresql://username:<EMAIL>/dbname?sslmode=require"

# AWS S3 Configuration
AWS_ACCESS_KEY_ID="your-access-key-id"
AWS_SECRET_ACCESS_KEY="your-secret-access-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET_NAME="dr-satya-medical-files"

# NextAuth Configuration
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# Application Configuration
APP_NAME="Dr <PERSON>'s Liver & Gastro Care"
APP_VERSION="1.0.0"
