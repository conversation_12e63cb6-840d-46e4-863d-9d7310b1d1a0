{"name": "pg", "version": "8.16.3", "description": "PostgreSQL client - pure javascript & libpq with the same API", "keywords": ["database", "libpq", "pg", "postgre", "postgres", "postgresql", "rdbms"], "homepage": "https://github.com/brianc/node-postgres", "repository": {"type": "git", "url": "git://github.com/brianc/node-postgres.git", "directory": "packages/pg"}, "author": "<PERSON> <brian.m.car<PERSON>@gmail.com>", "main": "./lib", "exports": {".": {"import": "./esm/index.mjs", "require": "./lib/index.js", "default": "./lib/index.js"}, "./package.json": {"default": "./package.json"}, "./lib/*": "./lib/*.js", "./lib/*.js": "./lib/*.js"}, "dependencies": {"pg-connection-string": "^2.9.1", "pg-pool": "^3.10.1", "pg-protocol": "^1.10.3", "pg-types": "2.2.0", "pgpass": "1.0.5"}, "devDependencies": {"@cloudflare/vitest-pool-workers": "0.8.23", "@cloudflare/workers-types": "^4.20230404.0", "async": "2.6.4", "bluebird": "3.7.2", "co": "4.6.0", "pg-copy-streams": "0.3.0", "typescript": "^4.0.3", "vitest": "~3.0.9", "wrangler": "^3.x"}, "optionalDependencies": {"pg-cloudflare": "^1.2.7"}, "peerDependencies": {"pg-native": ">=3.0.1"}, "peerDependenciesMeta": {"pg-native": {"optional": true}}, "scripts": {"test": "make test-all"}, "files": ["lib", "esm", "SPONSORS.md"], "license": "MIT", "engines": {"node": ">= 16.0.0"}, "gitHead": "8f8e7315e8f7c1bb01e98fdb41c8c92585510782"}