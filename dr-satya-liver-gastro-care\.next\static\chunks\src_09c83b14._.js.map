{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: {\n      gradient: 'from-blue-500 via-blue-600 to-blue-700',\n      bg: 'from-blue-50 to-blue-100',\n      text: 'text-blue-700',\n      border: 'border-blue-200'\n    },\n    green: {\n      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',\n      bg: 'from-emerald-50 to-emerald-100',\n      text: 'text-emerald-700',\n      border: 'border-emerald-200'\n    },\n    amber: {\n      gradient: 'from-amber-500 via-amber-600 to-amber-700',\n      bg: 'from-amber-50 to-amber-100',\n      text: 'text-amber-700',\n      border: 'border-amber-200'\n    },\n    red: {\n      gradient: 'from-red-500 via-red-600 to-red-700',\n      bg: 'from-red-50 to-red-100',\n      text: 'text-red-700',\n      border: 'border-red-200'\n    },\n    teal: {\n      gradient: 'from-teal-500 via-teal-600 to-teal-700',\n      bg: 'from-teal-50 to-teal-100',\n      text: 'text-teal-700',\n      border: 'border-teal-200'\n    }\n  };\n\n  const currentColor = colorClasses[color];\n\n  return (\n    <motion.div\n      className={`medical-card-glass relative overflow-hidden border ${currentColor.border} ${className}`}\n      whileHover={{\n        y: -8,\n        scale: 1.02,\n        transition: { duration: 0.3 }\n      }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className={`absolute inset-0 bg-gradient-to-br ${currentColor.bg} opacity-50`} />\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/20\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-white/10\"\n          animate={{\n            scale: [1, 1.3, 1],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <motion.p\n              className=\"text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              {title}\n            </motion.p>\n\n            <motion.p\n              className=\"text-4xl font-bold text-gray-900 mb-2\"\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            >\n              {value}\n            </motion.p>\n\n            {subtitle && (\n              <motion.p\n                className=\"text-sm text-gray-600 font-medium\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                {subtitle}\n              </motion.p>\n            )}\n\n            {trend && (\n              <motion.div\n                className=\"flex items-center mt-3\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n              >\n                <motion.span\n                  className={`inline-flex items-center gap-1 text-sm font-bold px-2 py-1 rounded-full ${\n                    trend.isPositive\n                      ? 'text-emerald-700 bg-emerald-100'\n                      : 'text-red-700 bg-red-100'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                >\n                  <motion.span\n                    animate={{ rotate: trend.isPositive ? 0 : 180 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    ↗\n                  </motion.span>\n                  {Math.abs(trend.value)}%\n                </motion.span>\n                <span className=\"text-xs text-gray-500 ml-2 font-medium\">vs last month</span>\n              </motion.div>\n            )}\n          </div>\n\n          {icon && (\n            <motion.div\n              className={`p-4 rounded-2xl bg-gradient-to-br ${currentColor.gradient} text-white shadow-lg`}\n              whileHover={{\n                scale: 1.1,\n                rotate: 5,\n                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)'\n              }}\n              initial={{ opacity: 0, scale: 0, rotate: -90 }}\n              animate={{ opacity: 1, scale: 1, rotate: 0 }}\n              transition={{ delay: 0.3, type: \"spring\", stiffness: 200 }}\n            >\n              {icon}\n            </motion.div>\n          )}\n        </div>\n      </div>\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className={`absolute inset-0 bg-gradient-to-r ${currentColor.gradient} opacity-0 rounded-2xl`}\n        whileHover={{ opacity: 0.1 }}\n        transition={{ duration: 0.3 }}\n      />\n    </motion.div>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <motion.div\n      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}\n      onClick={() => onClick?.(patient)}\n      whileHover={{\n        y: -6,\n        scale: 1.02,\n        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',\n        transition: { duration: 0.3 }\n      }}\n      whileTap={{ scale: 0.98 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50\" />\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n      />\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Premium Avatar */}\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ duration: 0.2 }}\n          >\n            {patient.profileImageUrl ? (\n              <img\n                src={patient.profileImageUrl}\n                alt={patient.fullName}\n                className=\"w-16 h-16 rounded-2xl object-cover shadow-lg\"\n              />\n            ) : (\n              <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                {getInitials(patient.fullName)}\n              </div>\n            )}\n            {/* Status Indicator */}\n            <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n              <div className=\"w-full h-full bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n          </motion.div>\n\n          {/* Patient Info */}\n          <div className=\"flex-1 min-w-0\">\n            <motion.div\n              className=\"flex items-center justify-between mb-2\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h3 className=\"font-bold text-gray-900 text-lg truncate\">\n                {patient.fullName}\n              </h3>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200\">\n                {patient.patientId}\n              </span>\n            </motion.div>\n\n            <motion.div\n              className=\"flex items-center space-x-3 text-sm\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  patient.gender === 'Male' ? 'bg-blue-500' :\n                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'\n                }`}></div>\n                <span className=\"text-gray-600 font-medium\">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n              </div>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <span className=\"text-gray-600 font-medium\">{patient.gender}</span>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <div className=\"flex items-center gap-1\">\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span className=\"text-gray-600 font-medium\">{patient.mobileNumber}</span>\n              </div>\n            </motion.div>\n\n            {patient.lastVisit && (\n              <motion.div\n                className=\"flex items-center gap-1 mt-2\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-xs text-gray-500 font-medium\">\n                  Last visit: {patient.lastVisit}\n                </p>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Arrow Indicator */}\n          <motion.div\n            className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-200\"\n            whileHover={{ x: 5 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.div>\n        </div>\n\n        {/* Bottom Border Animation */}\n        <motion.div\n          className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full\"\n          initial={{ width: 0 }}\n          whileHover={{ width: '100%' }}\n          transition={{ duration: 0.3 }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,AAAC,4BAGjB,OADA,cAAc,CAAC,QAAQ,EAAC,WAExB,OADA,aAAa,CAAC,OAAO,EAAC,UAEtB,OADA,WAAW,4CAA4C,YAAW,UAElE,OADA,UAAU,mBAAmB,IAAG,UACtB,OAAV,WAAU;IAGd,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;KApDM;AAqEC,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,KAAK;YACH,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,MAAM;IAExC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,sDAA4E,OAAvB,aAAa,MAAM,EAAC,KAAa,OAAV;QACxF,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAW,AAAC,sCAAqD,OAAhB,aAAa,EAAE,EAAC;;;;;;0BAGtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG;gCAAK;6BAAI;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAK;gCAAK;6BAAE;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;8CAGH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;8CAExD;;;;;;gCAGF,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;gCAIJ,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;;sDAEzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAW,AAAC,2EAIX,OAHC,MAAM,UAAU,GACZ,oCACA;4CAEN,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,QAAQ,MAAM,UAAU,GAAG,IAAI;oDAAI;oDAC9C,YAAY;wDAAE,UAAU;oDAAI;8DAC7B;;;;;;gDAGA,KAAK,GAAG,CAAC,MAAM,KAAK;gDAAE;;;;;;;sDAEzB,6LAAC;4CAAK,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;wBAK9D,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,AAAC,qCAA0D,OAAtB,aAAa,QAAQ,EAAC;4BACtE,YAAY;gCACV,OAAO;gCACP,QAAQ;gCACR,WAAW;4BACb;4BACA,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ,CAAC;4BAAG;4BAC7C,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ;4BAAE;4BAC3C,YAAY;gCAAE,OAAO;gCAAK,MAAM;gCAAU,WAAW;4BAAI;sCAExD;;;;;;;;;;;;;;;;;0BAOT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,qCAA0D,OAAtB,aAAa,QAAQ,EAAC;gBACtE,YAAY;oBAAE,SAAS;gBAAI;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;MA7Ka;AA8LN,MAAM,cAA0C;QAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,2FAAoG,OAAV;QACtG,SAAS,IAAM,oBAAA,8BAAA,QAAU;QACzB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,UAAU;gCAAI;;oCAE3B,QAAQ,eAAe,iBACtB,6LAAC;wCACC,KAAK,QAAQ,eAAe;wCAC5B,KAAK,QAAQ,QAAQ;wCACrB,WAAU;;;;;iGAGZ,6LAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,QAAQ;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ;;;;;;0DAEnB,6LAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS;;;;;;;;;;;;kDAItB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAGhB,OAFC,QAAQ,MAAM,KAAK,SAAS,gBAC5B,QAAQ,MAAM,KAAK,WAAW,gBAAgB;;;;;;kEAEhD,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,GAAG,GAAG,AAAC,GAAc,OAAZ,QAAQ,GAAG,EAAC,YAAU;;;;;;;;;;;;0DAGtF,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAK,WAAU;0DAA6B,QAAQ,MAAM;;;;;;0DAE3D,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,YAAY;;;;;;;;;;;;;;;;;;oCAIpE,QAAQ,SAAS,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAE,WAAU;;oDAAoC;oDAClC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0CAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG;gCAAE;0CAEnB,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAO;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;AAKtC;MAhJa;AA2JN,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAK,WAAW;;0BACf,6LAAC;gBACC,WAAW,AAAC,qCAAwE,OAApC,cAAc,mBAAmB;gBACjF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GAnDa;MAAA;uCAqDE", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n  fullScreen?: boolean;\n  className?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  size = 'md', \n  text = 'Loading...', \n  fullScreen = false,\n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-10 h-10',\n    lg: 'w-16 h-16'\n  };\n\n  const LoadingSpinner = () => (\n    <div className=\"relative\">\n      {/* Outer ring */}\n      <motion.div\n        className={`${sizeClasses[size]} border-4 border-blue-100 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Inner spinning ring */}\n      <motion.div\n        className={`absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-blue-600 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Medical cross in center */}\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <div className=\"w-3 h-3 relative\">\n          <div className=\"absolute inset-x-1/2 inset-y-0 w-0.5 bg-blue-600 transform -translate-x-1/2\" />\n          <div className=\"absolute inset-y-1/2 inset-x-0 h-0.5 bg-blue-600 transform -translate-y-1/2\" />\n        </div>\n      </div>\n    </div>\n  );\n\n  const LoadingDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-2 h-2 bg-blue-600 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const content = (\n    <motion.div\n      className={`flex flex-col items-center justify-center gap-4 ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <LoadingSpinner />\n      \n      {text && (\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <p className=\"text-gray-600 font-medium\">{text}</p>\n          <LoadingDots />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n\n  if (fullScreen) {\n    return (\n      <motion.div\n        className=\"fixed inset-0 bg-white bg-opacity-90 backdrop-blur-sm z-50 flex items-center justify-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n      >\n        {content}\n      </motion.div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton Loading Component\ninterface SkeletonProps {\n  className?: string;\n  lines?: number;\n  avatar?: boolean;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({ \n  className = '', \n  lines = 3, \n  avatar = false \n}) => {\n  return (\n    <div className={`animate-pulse ${className}`}>\n      <div className=\"flex items-start space-x-4\">\n        {avatar && (\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n        )}\n        <div className=\"flex-1 space-y-2\">\n          {Array.from({ length: lines }).map((_, index) => (\n            <div\n              key={index}\n              className={`h-4 bg-gray-200 rounded ${\n                index === lines - 1 ? 'w-3/4' : 'w-full'\n              }`}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Card Skeleton\nexport const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {\n  return (\n    <div className={`medical-card p-6 ${className}`}>\n      <div className=\"animate-pulse\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n          <div className=\"flex-1\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\" />\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"h-3 bg-gray-200 rounded\" />\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n          <div className=\"h-3 bg-gray-200 rounded w-4/6\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Button Loading State\ninterface ButtonLoadingProps {\n  loading?: boolean;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n}\n\nexport const ButtonLoading: React.FC<ButtonLoadingProps> = ({\n  loading = false,\n  children,\n  className = '',\n  onClick,\n  disabled = false\n}) => {\n  return (\n    <button\n      className={`btn-primary relative ${className} ${\n        loading || disabled ? 'opacity-70 cursor-not-allowed' : ''\n      }`}\n      onClick={onClick}\n      disabled={loading || disabled}\n    >\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      <span className={loading ? 'opacity-0' : 'opacity-100'}>\n        {children}\n      </span>\n    </button>\n  );\n};\n\nexport default Loading;\n"], "names": [], "mappings": ";;;;;;;AAGA;AAHA;;;AAYA,MAAM,UAAkC;QAAC,EACvC,OAAO,IAAI,EACX,OAAO,YAAY,EACnB,aAAa,KAAK,EAClB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,GAAoB,OAAlB,WAAW,CAAC,KAAK,EAAC;oBAChC,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,oBAAqC,OAAlB,WAAW,CAAC,KAAK,EAAC;oBACjD,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,cAAc,kBAClB,6LAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,QAAQ;oBACjB;mBAVK;;;;;;;;;;IAgBb,MAAM,wBACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,mDAA4D,OAAV;QAC9D,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;;;;;YAEA,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;;;;;;;;;;;;;;;;;IAMT,IAAI,YAAY;QACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;sBAElB;;;;;;IAGP;IAEA,OAAO;AACT;KA/FM;AAwGC,MAAM,WAAoC;QAAC,EAChD,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,SAAS,KAAK,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,iBAA0B,OAAV;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;gBACZ,wBACC,6LAAC;oBAAI,WAAU;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;4BAEC,WAAW,AAAC,2BAEX,OADC,UAAU,QAAQ,IAAI,UAAU;2BAF7B;;;;;;;;;;;;;;;;;;;;;AAUnB;MAxBa;AA2BN,MAAM,eAAiD;QAAC,EAAE,YAAY,EAAE,EAAE;IAC/E,qBACE,6LAAC;QAAI,WAAW,AAAC,oBAA6B,OAAV;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAGnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;MAnBa;AA8BN,MAAM,gBAA8C;QAAC,EAC1D,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EACjB;IACC,qBACE,6LAAC;QACC,WAAW,AAAC,wBACV,OADiC,WAAU,KAE5C,OADC,WAAW,WAAW,kCAAkC;QAE1D,SAAS;QACT,UAAU,WAAW;;YAEpB,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGnB,6LAAC;gBAAK,WAAW,UAAU,cAAc;0BACtC;;;;;;;;;;;;AAIT;MAzBa;uCA2BE", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/patients/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { \n  UserIcon, \n  PhoneIcon, \n  EnvelopeIcon,\n  MapPinIcon,\n  ScaleIcon,\n  HeartIcon,\n  DocumentTextIcon,\n  CameraIcon\n} from '@heroicons/react/24/outline';\nimport { toast } from 'react-hot-toast';\nimport { InfoCard } from '@/components/ui/Card';\nimport { ButtonLoading } from '@/components/ui/Loading';\n\n// Validation schema\nconst patientSchema = z.object({\n  // Basic Information\n  firstName: z.string().min(2, 'First name must be at least 2 characters'),\n  lastName: z.string().min(2, 'Last name must be at least 2 characters'),\n  dateOfBirth: z.string().min(1, 'Date of birth is required'),\n  gender: z.enum(['Male', 'Female', 'Other'], { required_error: 'Gender is required' }),\n  aadharNumber: z.string().regex(/^\\d{12}$/, 'Aadhar number must be exactly 12 digits'),\n  mobileNumber: z.string().regex(/^\\d{10}$/, 'Mobile number must be exactly 10 digits'),\n  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),\n  address: z.string().min(10, 'Address must be at least 10 characters'),\n  occupation: z.string().optional(),\n\n  // Physical Information\n  heightCm: z.number().min(50, 'Height must be at least 50 cm').max(250, 'Height must be less than 250 cm'),\n  weightKg: z.number().min(10, 'Weight must be at least 10 kg').max(300, 'Weight must be less than 300 kg'),\n  bloodGroup: z.enum(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'], { required_error: 'Blood group is required' }),\n\n  // Medical Scores\n  charlsonIndex: z.number().min(0).max(37).optional(),\n  asaGrade: z.number().min(1).max(6).optional(),\n  ecogGrade: z.number().min(0).max(5).optional(),\n\n  // Comorbidities\n  comorbidities: z.array(z.string()).optional(),\n});\n\ntype PatientFormData = z.infer<typeof patientSchema>;\n\nconst NewPatientPage: React.FC = () => {\n  const [currentSection, setCurrentSection] = useState(0);\n  const [isLoading, setIsLoading] = useState(false);\n  const [selectedComorbidities, setSelectedComorbidities] = useState<string[]>([]);\n\n  const {\n    register,\n    handleSubmit,\n    watch,\n    formState: { errors },\n    setValue,\n  } = useForm<PatientFormData>({\n    resolver: zodResolver(patientSchema),\n    defaultValues: {\n      charlsonIndex: 0,\n      asaGrade: 1,\n      ecogGrade: 0,\n      comorbidities: [],\n    },\n  });\n\n  const watchHeight = watch('heightCm');\n  const watchWeight = watch('weightKg');\n\n  // Calculate BMI\n  const calculateBMI = () => {\n    if (watchHeight && watchWeight) {\n      const heightInMeters = watchHeight / 100;\n      const bmi = watchWeight / (heightInMeters * heightInMeters);\n      return bmi.toFixed(1);\n    }\n    return null;\n  };\n\n  // Calculate age from date of birth\n  const calculateAge = (dateOfBirth: string) => {\n    if (!dateOfBirth) return null;\n    const today = new Date();\n    const birthDate = new Date(dateOfBirth);\n    let age = today.getFullYear() - birthDate.getFullYear();\n    const monthDiff = today.getMonth() - birthDate.getMonth();\n    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {\n      age--;\n    }\n    return age;\n  };\n\n  const mockComorbidities = [\n    { id: '1', name: 'Diabetes Mellitus', category: 'Endocrine' },\n    { id: '2', name: 'Hypertension', category: 'Cardiovascular' },\n    { id: '3', name: 'Heart Disease', category: 'Cardiovascular' },\n    { id: '4', name: 'Chronic Kidney Disease', category: 'Renal' },\n    { id: '5', name: 'Liver Disease', category: 'Hepatic' },\n    { id: '6', name: 'COPD', category: 'Respiratory' },\n    { id: '7', name: 'Asthma', category: 'Respiratory' },\n    { id: '8', name: 'Obesity', category: 'Metabolic' },\n  ];\n\n  const sections = [\n    {\n      title: 'Basic Information',\n      icon: <UserIcon className=\"w-5 h-5\" />,\n      description: 'Personal details and contact information',\n    },\n    {\n      title: 'Physical Information',\n      icon: <ScaleIcon className=\"w-5 h-5\" />,\n      description: 'Height, weight, and blood group',\n    },\n    {\n      title: 'Medical Information',\n      icon: <HeartIcon className=\"w-5 h-5\" />,\n      description: 'Medical scores and comorbidities',\n    },\n    {\n      title: 'Review & Submit',\n      icon: <DocumentTextIcon className=\"w-5 h-5\" />,\n      description: 'Review all information before submitting',\n    },\n  ];\n\n  const onSubmit = async (data: PatientFormData) => {\n    setIsLoading(true);\n    try {\n      // Calculate BMI\n      let bmi = null;\n      if (data.heightCm && data.weightKg) {\n        const heightInMeters = data.heightCm / 100;\n        bmi = data.weightKg / (heightInMeters * heightInMeters);\n      }\n\n      const patientData = {\n        firstName: data.firstName,\n        lastName: data.lastName,\n        dateOfBirth: data.dateOfBirth,\n        gender: data.gender,\n        aadharNumber: data.aadharNumber,\n        mobileNumber: data.mobileNumber,\n        email: data.email || null,\n        address: data.address,\n        occupation: data.occupation || null,\n        height: data.heightCm,\n        weight: data.weightKg,\n        bmi: bmi ? parseFloat(bmi.toFixed(2)) : null,\n        bloodGroup: data.bloodGroup,\n        charlsonIndex: data.charlsonIndex || null,\n        asaGrade: data.asaGrade || null,\n        ecogGrade: data.ecogGrade || null,\n        comorbidities: selectedComorbidities,\n        createdBy: 'current-user-id' // This should come from auth context\n      };\n\n      const response = await fetch('/api/patients', {\n        method: 'POST',\n        headers: { 'Content-Type': 'application/json' },\n        body: JSON.stringify(patientData),\n      });\n\n      const result = await response.json();\n\n      if (result.success) {\n        toast.success('Patient registered successfully!');\n        // Redirect to patient detail page\n        window.location.href = `/patients/${result.data.id}`;\n      } else {\n        toast.error(result.error || 'Failed to register patient');\n      }\n    } catch (error) {\n      console.error('Error registering patient:', error);\n      toast.error('Failed to register patient. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const nextSection = () => {\n    if (currentSection < sections.length - 1) {\n      setCurrentSection(currentSection + 1);\n    }\n  };\n\n  const prevSection = () => {\n    if (currentSection > 0) {\n      setCurrentSection(currentSection - 1);\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <motion.div\n      className=\"max-w-4xl mx-auto space-y-6\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Header */}\n      <motion.div variants={itemVariants}>\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Add New Patient\n        </h1>\n        <p className=\"text-gray-600\">\n          Register a new patient in the system with comprehensive medical information.\n        </p>\n      </motion.div>\n\n      {/* Progress Steps */}\n      <motion.div className=\"medical-card p-6\" variants={itemVariants}>\n        <div className=\"flex items-center justify-between\">\n          {sections.map((section, index) => (\n            <div\n              key={index}\n              className={`flex items-center ${index < sections.length - 1 ? 'flex-1' : ''}`}\n            >\n              <div className=\"flex items-center\">\n                <div\n                  className={`w-10 h-10 rounded-full flex items-center justify-center ${\n                    index <= currentSection\n                      ? 'bg-blue-600 text-white'\n                      : 'bg-gray-200 text-gray-500'\n                  }`}\n                >\n                  {index < currentSection ? (\n                    <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                    </svg>\n                  ) : (\n                    section.icon\n                  )}\n                </div>\n                <div className=\"ml-3 hidden sm:block\">\n                  <p className={`text-sm font-medium ${\n                    index <= currentSection ? 'text-blue-600' : 'text-gray-500'\n                  }`}>\n                    {section.title}\n                  </p>\n                  <p className=\"text-xs text-gray-500\">{section.description}</p>\n                </div>\n              </div>\n              {index < sections.length - 1 && (\n                <div className={`flex-1 h-0.5 mx-4 ${\n                  index < currentSection ? 'bg-blue-600' : 'bg-gray-200'\n                }`} />\n              )}\n            </div>\n          ))}\n        </div>\n      </motion.div>\n\n      {/* Form */}\n      <form onSubmit={handleSubmit(onSubmit)}>\n        <motion.div variants={itemVariants}>\n          {/* Section 0: Basic Information */}\n          {currentSection === 0 && (\n            <InfoCard title=\"Basic Information\" icon={<UserIcon className=\"w-5 h-5\" />}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"form-label\">First Name *</label>\n                  <input\n                    {...register('firstName')}\n                    className={`form-input ${errors.firstName ? 'error' : ''}`}\n                    placeholder=\"Enter first name\"\n                  />\n                  {errors.firstName && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.firstName.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Last Name *</label>\n                  <input\n                    {...register('lastName')}\n                    className={`form-input ${errors.lastName ? 'error' : ''}`}\n                    placeholder=\"Enter last name\"\n                  />\n                  {errors.lastName && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.lastName.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Date of Birth *</label>\n                  <input\n                    {...register('dateOfBirth')}\n                    type=\"date\"\n                    className={`form-input ${errors.dateOfBirth ? 'error' : ''}`}\n                  />\n                  {errors.dateOfBirth && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.dateOfBirth.message}</p>\n                  )}\n                  {watch('dateOfBirth') && (\n                    <p className=\"mt-1 text-sm text-blue-600\">\n                      Age: {calculateAge(watch('dateOfBirth'))} years\n                    </p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Gender *</label>\n                  <select\n                    {...register('gender')}\n                    className={`form-input ${errors.gender ? 'error' : ''}`}\n                  >\n                    <option value=\"\">Select gender</option>\n                    <option value=\"Male\">Male</option>\n                    <option value=\"Female\">Female</option>\n                    <option value=\"Other\">Other</option>\n                  </select>\n                  {errors.gender && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.gender.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Aadhar Number *</label>\n                  <input\n                    {...register('aadharNumber')}\n                    className={`form-input ${errors.aadharNumber ? 'error' : ''}`}\n                    placeholder=\"Enter 12-digit Aadhar number\"\n                    maxLength={12}\n                  />\n                  {errors.aadharNumber && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.aadharNumber.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Mobile Number *</label>\n                  <input\n                    {...register('mobileNumber')}\n                    className={`form-input ${errors.mobileNumber ? 'error' : ''}`}\n                    placeholder=\"Enter 10-digit mobile number\"\n                    maxLength={10}\n                  />\n                  {errors.mobileNumber && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.mobileNumber.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Email Address</label>\n                  <input\n                    {...register('email')}\n                    type=\"email\"\n                    className={`form-input ${errors.email ? 'error' : ''}`}\n                    placeholder=\"Enter email address (optional)\"\n                  />\n                  {errors.email && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.email.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Occupation</label>\n                  <input\n                    {...register('occupation')}\n                    className=\"form-input\"\n                    placeholder=\"Enter occupation\"\n                  />\n                </div>\n\n                <div className=\"md:col-span-2\">\n                  <label className=\"form-label\">Address *</label>\n                  <textarea\n                    {...register('address')}\n                    className={`form-input ${errors.address ? 'error' : ''}`}\n                    placeholder=\"Enter complete address\"\n                    rows={3}\n                  />\n                  {errors.address && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.address.message}</p>\n                  )}\n                </div>\n              </div>\n            </InfoCard>\n          )}\n\n          {/* Section 1: Physical Information */}\n          {currentSection === 1 && (\n            <InfoCard title=\"Physical Information\" icon={<ScaleIcon className=\"w-5 h-5\" />}>\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"form-label\">Height (cm) *</label>\n                  <input\n                    {...register('heightCm', { valueAsNumber: true })}\n                    type=\"number\"\n                    className={`form-input ${errors.heightCm ? 'error' : ''}`}\n                    placeholder=\"Enter height in centimeters\"\n                    min=\"50\"\n                    max=\"250\"\n                  />\n                  {errors.heightCm && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.heightCm.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Weight (kg) *</label>\n                  <input\n                    {...register('weightKg', { valueAsNumber: true })}\n                    type=\"number\"\n                    className={`form-input ${errors.weightKg ? 'error' : ''}`}\n                    placeholder=\"Enter weight in kilograms\"\n                    min=\"10\"\n                    max=\"300\"\n                    step=\"0.1\"\n                  />\n                  {errors.weightKg && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.weightKg.message}</p>\n                  )}\n                </div>\n\n                <div>\n                  <label className=\"form-label\">Blood Group *</label>\n                  <select\n                    {...register('bloodGroup')}\n                    className={`form-input ${errors.bloodGroup ? 'error' : ''}`}\n                  >\n                    <option value=\"\">Select blood group</option>\n                    <option value=\"A+\">A+</option>\n                    <option value=\"A-\">A-</option>\n                    <option value=\"B+\">B+</option>\n                    <option value=\"B-\">B-</option>\n                    <option value=\"AB+\">AB+</option>\n                    <option value=\"AB-\">AB-</option>\n                    <option value=\"O+\">O+</option>\n                    <option value=\"O-\">O-</option>\n                  </select>\n                  {errors.bloodGroup && (\n                    <p className=\"mt-1 text-sm text-red-600\">{errors.bloodGroup.message}</p>\n                  )}\n                </div>\n\n                {/* BMI Display */}\n                {watchHeight && watchWeight && (\n                  <div>\n                    <label className=\"form-label\">BMI (Calculated)</label>\n                    <div className=\"form-input bg-gray-50 text-gray-700\">\n                      {calculateBMI()}\n                    </div>\n                    <p className=\"mt-1 text-sm text-gray-600\">\n                      Automatically calculated from height and weight\n                    </p>\n                  </div>\n                )}\n              </div>\n            </InfoCard>\n          )}\n\n          {/* Section 2: Medical Information */}\n          {currentSection === 2 && (\n            <InfoCard title=\"Medical Information\" icon={<HeartIcon className=\"w-5 h-5\" />}>\n              <div className=\"space-y-6\">\n                {/* Medical Scores */}\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Medical Scores</h4>\n                  <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n                    <div>\n                      <label className=\"form-label\">Charlson Index (0-37)</label>\n                      <input\n                        {...register('charlsonIndex', { valueAsNumber: true })}\n                        type=\"number\"\n                        className=\"form-input\"\n                        placeholder=\"0\"\n                        min=\"0\"\n                        max=\"37\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"form-label\">ASA Grade (1-6)</label>\n                      <select\n                        {...register('asaGrade', { valueAsNumber: true })}\n                        className=\"form-input\"\n                      >\n                        <option value=\"\">Select ASA Grade</option>\n                        <option value={1}>1 - Normal healthy patient</option>\n                        <option value={2}>2 - Mild systemic disease</option>\n                        <option value={3}>3 - Severe systemic disease</option>\n                        <option value={4}>4 - Life-threatening disease</option>\n                        <option value={5}>5 - Moribund patient</option>\n                        <option value={6}>6 - Brain-dead patient</option>\n                      </select>\n                    </div>\n\n                    <div>\n                      <label className=\"form-label\">ECOG Grade (0-5)</label>\n                      <select\n                        {...register('ecogGrade', { valueAsNumber: true })}\n                        className=\"form-input\"\n                      >\n                        <option value=\"\">Select ECOG Grade</option>\n                        <option value={0}>0 - Fully active</option>\n                        <option value={1}>1 - Restricted in strenuous activity</option>\n                        <option value={2}>2 - Ambulatory, up &gt;50% of time</option>\n                        <option value={3}>3 - Confined to bed/chair &gt;50% of time</option>\n                        <option value={4}>4 - Completely disabled</option>\n                        <option value={5}>5 - Dead</option>\n                      </select>\n                    </div>\n                  </div>\n                </div>\n\n                {/* Comorbidities */}\n                <div>\n                  <h4 className=\"font-semibold text-gray-900 mb-4\">Comorbidities</h4>\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3\">\n                    {mockComorbidities.map((comorbidity) => (\n                      <label key={comorbidity.id} className=\"flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer\">\n                        <input\n                          type=\"checkbox\"\n                          className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                          checked={selectedComorbidities.includes(comorbidity.id)}\n                          onChange={(e) => {\n                            if (e.target.checked) {\n                              setSelectedComorbidities([...selectedComorbidities, comorbidity.id]);\n                            } else {\n                              setSelectedComorbidities(selectedComorbidities.filter(id => id !== comorbidity.id));\n                            }\n                          }}\n                        />\n                        <div>\n                          <p className=\"text-sm font-medium text-gray-900\">{comorbidity.name}</p>\n                          <p className=\"text-xs text-gray-500\">{comorbidity.category}</p>\n                        </div>\n                      </label>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </InfoCard>\n          )}\n\n          {/* Section 3: Review & Submit */}\n          {currentSection === 3 && (\n            <InfoCard title=\"Review & Submit\" icon={<DocumentTextIcon className=\"w-5 h-5\" />}>\n              <div className=\"space-y-6\">\n                <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n                  <h4 className=\"font-semibold text-blue-900 mb-2\">Review Patient Information</h4>\n                  <p className=\"text-sm text-blue-700\">\n                    Please review all the information below before submitting the patient registration.\n                  </p>\n                </div>\n\n                {/* Summary sections would go here */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                  <div>\n                    <h5 className=\"font-medium text-gray-900 mb-2\">Basic Information</h5>\n                    <div className=\"text-sm space-y-1\">\n                      <p><span className=\"text-gray-600\">Name:</span> {watch('firstName')} {watch('lastName')}</p>\n                      <p><span className=\"text-gray-600\">DOB:</span> {watch('dateOfBirth')}</p>\n                      <p><span className=\"text-gray-600\">Gender:</span> {watch('gender')}</p>\n                      <p><span className=\"text-gray-600\">Mobile:</span> {watch('mobileNumber')}</p>\n                      <p><span className=\"text-gray-600\">Email:</span> {watch('email') || 'N/A'}</p>\n                    </div>\n                  </div>\n\n                  <div>\n                    <h5 className=\"font-medium text-gray-900 mb-2\">Physical Information</h5>\n                    <div className=\"text-sm space-y-1\">\n                      <p><span className=\"text-gray-600\">Height:</span> {watch('heightCm')} cm</p>\n                      <p><span className=\"text-gray-600\">Weight:</span> {watch('weightKg')} kg</p>\n                      <p><span className=\"text-gray-600\">BMI:</span> {calculateBMI()}</p>\n                      <p><span className=\"text-gray-600\">Blood Group:</span> {watch('bloodGroup')}</p>\n                    </div>\n                  </div>\n                </div>\n\n                {selectedComorbidities.length > 0 && (\n                  <div>\n                    <h5 className=\"font-medium text-gray-900 mb-2\">Selected Comorbidities</h5>\n                    <div className=\"flex flex-wrap gap-2\">\n                      {selectedComorbidities.map(id => {\n                        const comorbidity = mockComorbidities.find(c => c.id === id);\n                        return comorbidity ? (\n                          <span key={id} className=\"px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full\">\n                            {comorbidity.name}\n                          </span>\n                        ) : null;\n                      })}\n                    </div>\n                  </div>\n                )}\n              </div>\n            </InfoCard>\n          )}\n\n          {/* Navigation Buttons */}\n          <div className=\"flex justify-between mt-6\">\n            <button\n              type=\"button\"\n              onClick={prevSection}\n              disabled={currentSection === 0}\n              className=\"btn-secondary disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              Previous\n            </button>\n            \n            {currentSection < sections.length - 1 ? (\n              <button\n                type=\"button\"\n                onClick={nextSection}\n                className=\"btn-primary\"\n              >\n                Next\n              </button>\n            ) : (\n              <ButtonLoading\n                loading={isLoading}\n                className=\"btn-primary\"\n                disabled={isLoading}\n              >\n                {isLoading ? 'Registering...' : 'Register Patient'}\n              </ButtonLoading>\n            )}\n          </div>\n        </motion.div>\n      </form>\n    </motion.div>\n  );\n};\n\nexport default NewPatientPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAUA;AACA;AACA;;;AAnBA;;;;;;;;;;AAqBA,oBAAoB;AACpB,MAAM,gBAAgB,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,oBAAoB;IACpB,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC/B,QAAQ,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;KAAQ,EAAE;QAAE,gBAAgB;IAAqB;IACnF,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY;IAC3C,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,YAAY;IAC3C,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,sCAAsC,QAAQ,GAAG,EAAE,CAAC,gLAAA,CAAA,IAAC,CAAC,OAAO,CAAC;IACtF,SAAS,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC5B,YAAY,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAE/B,uBAAuB;IACvB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,iCAAiC,GAAG,CAAC,KAAK;IACvE,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,iCAAiC,GAAG,CAAC,KAAK;IACvE,YAAY,gLAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAM;QAAM;QAAM;QAAM;QAAO;QAAO;QAAM;KAAK,EAAE;QAAE,gBAAgB;IAA0B;IAEnH,iBAAiB;IACjB,eAAe,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,IAAI,QAAQ;IACjD,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IAC3C,WAAW,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC,GAAG,QAAQ;IAE5C,gBAAgB;IAChB,eAAe,gLAAA,CAAA,IAAC,CAAC,KAAK,CAAC,gLAAA,CAAA,IAAC,CAAC,MAAM,IAAI,QAAQ;AAC7C;AAIA,MAAM,iBAA2B;;IAC/B,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,uBAAuB,yBAAyB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/E,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,KAAK,EACL,WAAW,EAAE,MAAM,EAAE,EACrB,QAAQ,EACT,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,eAAe;YACf,UAAU;YACV,WAAW;YACX,eAAe,EAAE;QACnB;IACF;IAEA,MAAM,cAAc,MAAM;IAC1B,MAAM,cAAc,MAAM;IAE1B,gBAAgB;IAChB,MAAM,eAAe;QACnB,IAAI,eAAe,aAAa;YAC9B,MAAM,iBAAiB,cAAc;YACrC,MAAM,MAAM,cAAc,CAAC,iBAAiB,cAAc;YAC1D,OAAO,IAAI,OAAO,CAAC;QACrB;QACA,OAAO;IACT;IAEA,mCAAmC;IACnC,MAAM,eAAe,CAAC;QACpB,IAAI,CAAC,aAAa,OAAO;QACzB,MAAM,QAAQ,IAAI;QAClB,MAAM,YAAY,IAAI,KAAK;QAC3B,IAAI,MAAM,MAAM,WAAW,KAAK,UAAU,WAAW;QACrD,MAAM,YAAY,MAAM,QAAQ,KAAK,UAAU,QAAQ;QACvD,IAAI,YAAY,KAAM,cAAc,KAAK,MAAM,OAAO,KAAK,UAAU,OAAO,IAAK;YAC/E;QACF;QACA,OAAO;IACT;IAEA,MAAM,oBAAoB;QACxB;YAAE,IAAI;YAAK,MAAM;YAAqB,UAAU;QAAY;QAC5D;YAAE,IAAI;YAAK,MAAM;YAAgB,UAAU;QAAiB;QAC5D;YAAE,IAAI;YAAK,MAAM;YAAiB,UAAU;QAAiB;QAC7D;YAAE,IAAI;YAAK,MAAM;YAA0B,UAAU;QAAQ;QAC7D;YAAE,IAAI;YAAK,MAAM;YAAiB,UAAU;QAAU;QACtD;YAAE,IAAI;YAAK,MAAM;YAAQ,UAAU;QAAc;QACjD;YAAE,IAAI;YAAK,MAAM;YAAU,UAAU;QAAc;QACnD;YAAE,IAAI;YAAK,MAAM;YAAW,UAAU;QAAY;KACnD;IAED,MAAM,WAAW;QACf;YACE,OAAO;YACP,oBAAM,6LAAC,kNAAA,CAAA,WAAQ;gBAAC,WAAU;;;;;;YAC1B,aAAa;QACf;QACA;YACE,OAAO;YACP,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,aAAa;QACf;QACA;YACE,OAAO;YACP,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,aAAa;QACf;QACA;YACE,OAAO;YACP,oBAAM,6LAAC,kOAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;YAClC,aAAa;QACf;KACD;IAED,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,gBAAgB;YAChB,IAAI,MAAM;YACV,IAAI,KAAK,QAAQ,IAAI,KAAK,QAAQ,EAAE;gBAClC,MAAM,iBAAiB,KAAK,QAAQ,GAAG;gBACvC,MAAM,KAAK,QAAQ,GAAG,CAAC,iBAAiB,cAAc;YACxD;YAEA,MAAM,cAAc;gBAClB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,aAAa,KAAK,WAAW;gBAC7B,QAAQ,KAAK,MAAM;gBACnB,cAAc,KAAK,YAAY;gBAC/B,cAAc,KAAK,YAAY;gBAC/B,OAAO,KAAK,KAAK,IAAI;gBACrB,SAAS,KAAK,OAAO;gBACrB,YAAY,KAAK,UAAU,IAAI;gBAC/B,QAAQ,KAAK,QAAQ;gBACrB,QAAQ,KAAK,QAAQ;gBACrB,KAAK,MAAM,WAAW,IAAI,OAAO,CAAC,MAAM;gBACxC,YAAY,KAAK,UAAU;gBAC3B,eAAe,KAAK,aAAa,IAAI;gBACrC,UAAU,KAAK,QAAQ,IAAI;gBAC3B,WAAW,KAAK,SAAS,IAAI;gBAC7B,eAAe;gBACf,WAAW,kBAAkB,qCAAqC;YACpE;YAEA,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,OAAO,OAAO,EAAE;gBAClB,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd,kCAAkC;gBAClC,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,aAA2B,OAAf,OAAO,IAAI,CAAC,EAAE;YACpD,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC,OAAO,KAAK,IAAI;YAC9B;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,8BAA8B;YAC5C,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,iBAAiB,SAAS,MAAM,GAAG,GAAG;YACxC,kBAAkB,iBAAiB;QACrC;IACF;IAEA,MAAM,cAAc;QAClB,IAAI,iBAAiB,GAAG;YACtB,kBAAkB,iBAAiB;QACrC;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,WAAU;gBAAmB,UAAU;0BACjD,cAAA,6LAAC;oBAAI,WAAU;8BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC;4BAEC,WAAW,AAAC,qBAAgE,OAA5C,QAAQ,SAAS,MAAM,GAAG,IAAI,WAAW;;8CAEzE,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,WAAW,AAAC,2DAIX,OAHC,SAAS,iBACL,2BACA;sDAGL,QAAQ,+BACP,6LAAC;gDAAI,WAAU;gDAAU,MAAK;gDAAe,SAAQ;0DACnD,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAqH,UAAS;;;;;;;;;;2FAG3J,QAAQ,IAAI;;;;;;sDAGhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAW,AAAC,uBAEd,OADC,SAAS,iBAAiB,kBAAkB;8DAE3C,QAAQ,KAAK;;;;;;8DAEhB,6LAAC;oDAAE,WAAU;8DAAyB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;gCAG5D,QAAQ,SAAS,MAAM,GAAG,mBACzB,6LAAC;oCAAI,WAAW,AAAC,qBAEhB,OADC,QAAQ,iBAAiB,gBAAgB;;;;;;;2BA9BxC;;;;;;;;;;;;;;;0BAuCb,6LAAC;gBAAK,UAAU,aAAa;0BAC3B,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAAC,UAAU;;wBAEnB,mBAAmB,mBAClB,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAoB,oBAAM,6LAAC,kNAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;sCAC5D,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,YAAY;gDACzB,WAAW,AAAC,cAA6C,OAAhC,OAAO,SAAS,GAAG,UAAU;gDACtD,aAAY;;;;;;4CAEb,OAAO,SAAS,kBACf,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;kDAItE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,WAAW;gDACxB,WAAW,AAAC,cAA4C,OAA/B,OAAO,QAAQ,GAAG,UAAU;gDACrD,aAAY;;;;;;4CAEb,OAAO,QAAQ,kBACd,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kDAIrE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,cAAc;gDAC3B,MAAK;gDACL,WAAW,AAAC,cAA+C,OAAlC,OAAO,WAAW,GAAG,UAAU;;;;;;4CAEzD,OAAO,WAAW,kBACjB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,WAAW,CAAC,OAAO;;;;;;4CAErE,MAAM,gCACL,6LAAC;gDAAE,WAAU;;oDAA6B;oDAClC,aAAa,MAAM;oDAAgB;;;;;;;;;;;;;kDAK/C,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,SAAS;gDACtB,WAAW,AAAC,cAA0C,OAA7B,OAAO,MAAM,GAAG,UAAU;;kEAEnD,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;4CAEvB,OAAO,MAAM,kBACZ,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,MAAM,CAAC,OAAO;;;;;;;;;;;;kDAInE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,eAAe;gDAC5B,WAAW,AAAC,cAAgD,OAAnC,OAAO,YAAY,GAAG,UAAU;gDACzD,aAAY;gDACZ,WAAW;;;;;;4CAEZ,OAAO,YAAY,kBAClB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;kDAIzE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,eAAe;gDAC5B,WAAW,AAAC,cAAgD,OAAnC,OAAO,YAAY,GAAG,UAAU;gDACzD,aAAY;gDACZ,WAAW;;;;;;4CAEZ,OAAO,YAAY,kBAClB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,YAAY,CAAC,OAAO;;;;;;;;;;;;kDAIzE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,QAAQ;gDACrB,MAAK;gDACL,WAAW,AAAC,cAAyC,OAA5B,OAAO,KAAK,GAAG,UAAU;gDAClD,aAAY;;;;;;4CAEb,OAAO,KAAK,kBACX,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;kDAIlE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,aAAa;gDAC1B,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAIhB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,UAAU;gDACvB,WAAW,AAAC,cAA2C,OAA9B,OAAO,OAAO,GAAG,UAAU;gDACpD,aAAY;gDACZ,MAAM;;;;;;4CAEP,OAAO,OAAO,kBACb,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;wBAQzE,mBAAmB,mBAClB,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAuB,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCAChE,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,YAAY;oDAAE,eAAe;gDAAK,EAAE;gDACjD,MAAK;gDACL,WAAW,AAAC,cAA4C,OAA/B,OAAO,QAAQ,GAAG,UAAU;gDACrD,aAAY;gDACZ,KAAI;gDACJ,KAAI;;;;;;4CAEL,OAAO,QAAQ,kBACd,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kDAIrE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,YAAY;oDAAE,eAAe;gDAAK,EAAE;gDACjD,MAAK;gDACL,WAAW,AAAC,cAA4C,OAA/B,OAAO,QAAQ,GAAG,UAAU;gDACrD,aAAY;gDACZ,KAAI;gDACJ,KAAI;gDACJ,MAAK;;;;;;4CAEN,OAAO,QAAQ,kBACd,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;kDAIrE,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDACE,GAAG,SAAS,aAAa;gDAC1B,WAAW,AAAC,cAA8C,OAAjC,OAAO,UAAU,GAAG,UAAU;;kEAEvD,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;;;;;;;4CAEpB,OAAO,UAAU,kBAChB,6LAAC;gDAAE,WAAU;0DAA6B,OAAO,UAAU,CAAC,OAAO;;;;;;;;;;;;oCAKtE,eAAe,6BACd,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDAAI,WAAU;0DACZ;;;;;;0DAEH,6LAAC;gDAAE,WAAU;0DAA6B;;;;;;;;;;;;;;;;;;;;;;;wBAUnD,mBAAmB,mBAClB,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAsB,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCAC/D,cAAA,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAa;;;;;;0EAC9B,6LAAC;gEACE,GAAG,SAAS,iBAAiB;oEAAE,eAAe;gEAAK,EAAE;gEACtD,MAAK;gEACL,WAAU;gEACV,aAAY;gEACZ,KAAI;gEACJ,KAAI;;;;;;;;;;;;kEAIR,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAa;;;;;;0EAC9B,6LAAC;gEACE,GAAG,SAAS,YAAY;oEAAE,eAAe;gEAAK,EAAE;gEACjD,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;;;;;;;;;;;;;kEAItB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAa;;;;;;0EAC9B,6LAAC;gEACE,GAAG,SAAS,aAAa;oEAAE,eAAe;gEAAK,EAAE;gEAClD,WAAU;;kFAEV,6LAAC;wEAAO,OAAM;kFAAG;;;;;;kFACjB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;kFAClB,6LAAC;wEAAO,OAAO;kFAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAO1B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAI,WAAU;0DACZ,kBAAkB,GAAG,CAAC,CAAC,4BACtB,6LAAC;wDAA2B,WAAU;;0EACpC,6LAAC;gEACC,MAAK;gEACL,WAAU;gEACV,SAAS,sBAAsB,QAAQ,CAAC,YAAY,EAAE;gEACtD,UAAU,CAAC;oEACT,IAAI,EAAE,MAAM,CAAC,OAAO,EAAE;wEACpB,yBAAyB;+EAAI;4EAAuB,YAAY,EAAE;yEAAC;oEACrE,OAAO;wEACL,yBAAyB,sBAAsB,MAAM,CAAC,CAAA,KAAM,OAAO,YAAY,EAAE;oEACnF;gEACF;;;;;;0EAEF,6LAAC;;kFACC,6LAAC;wEAAE,WAAU;kFAAqC,YAAY,IAAI;;;;;;kFAClE,6LAAC;wEAAE,WAAU;kFAAyB,YAAY,QAAQ;;;;;;;;;;;;;uDAflD,YAAY,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;wBA0BrC,mBAAmB,mBAClB,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAkB,oBAAM,6LAAC,kOAAA,CAAA,mBAAgB;gCAAC,WAAU;;;;;;sCAClE,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6LAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAMvC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAY;oEAAE,MAAM;oEAAa;oEAAE,MAAM;;;;;;;0EAC5E,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAW;oEAAE,MAAM;;;;;;;0EACtD,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAc;oEAAE,MAAM;;;;;;;0EACzD,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAc;oEAAE,MAAM;;;;;;;0EACzD,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAa;oEAAE,MAAM,YAAY;;;;;;;;;;;;;;;;;;;0DAIxE,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAiC;;;;;;kEAC/C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAc;oEAAE,MAAM;oEAAY;;;;;;;0EACrE,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAc;oEAAE,MAAM;oEAAY;;;;;;;0EACrE,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAW;oEAAE;;;;;;;0EAChD,6LAAC;;kFAAE,6LAAC;wEAAK,WAAU;kFAAgB;;;;;;oEAAmB;oEAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;oCAKnE,sBAAsB,MAAM,GAAG,mBAC9B,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAiC;;;;;;0DAC/C,6LAAC;gDAAI,WAAU;0DACZ,sBAAsB,GAAG,CAAC,CAAA;oDACzB,MAAM,cAAc,kBAAkB,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oDACzD,OAAO,4BACL,6LAAC;wDAAc,WAAU;kEACtB,YAAY,IAAI;uDADR;;;;mGAGT;gDACN;;;;;;;;;;;;;;;;;;;;;;;sCASZ,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,UAAU,mBAAmB;oCAC7B,WAAU;8CACX;;;;;;gCAIA,iBAAiB,SAAS,MAAM,GAAG,kBAClC,6LAAC;oCACC,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;6FAID,6LAAC,sIAAA,CAAA,gBAAa;oCACZ,SAAS;oCACT,WAAU;oCACV,UAAU;8CAET,YAAY,mBAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhD;GAjlBM;;QAWA,iKAAA,CAAA,UAAO;;;KAXP;uCAmlBS", "debugId": null}}]}