{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: {\n      gradient: 'from-blue-500 to-blue-600',\n      iconBg: 'bg-blue-500',\n      text: 'text-blue-600'\n    },\n    green: {\n      gradient: 'from-emerald-500 to-emerald-600',\n      iconBg: 'bg-emerald-500',\n      text: 'text-emerald-600'\n    },\n    amber: {\n      gradient: 'from-amber-500 to-amber-600',\n      iconBg: 'bg-amber-500',\n      text: 'text-amber-600'\n    },\n    red: {\n      gradient: 'from-red-500 to-red-600',\n      iconBg: 'bg-red-500',\n      text: 'text-red-600'\n    },\n    teal: {\n      gradient: 'from-teal-500 to-teal-600',\n      iconBg: 'bg-teal-500',\n      text: 'text-teal-600'\n    }\n  };\n\n  const currentColor = colorClasses[color];\n\n  return (\n    <motion.div\n      className={`bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 ${className}`}\n      whileHover={{ y: -2 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1 uppercase tracking-wide\">\n            {title}\n          </p>\n\n          <p className=\"text-3xl font-bold text-gray-900 mb-2\">\n            {value}\n          </p>\n\n          {subtitle && (\n            <p className=\"text-sm text-gray-600\">\n              {subtitle}\n            </p>\n          )}\n\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              <span className={`inline-flex items-center text-sm font-medium ${\n                trend.isPositive ? 'text-emerald-600' : 'text-red-600'\n              }`}>\n                <span className=\"mr-1\">\n                  {trend.isPositive ? '↗' : '↘'}\n                </span>\n                {Math.abs(trend.value)}% vs last month\n              </span>\n            </div>\n          )}\n        </div>\n\n        {icon && (\n          <div className={`p-3 rounded-xl ${currentColor.iconBg}`}>\n            <div className=\"w-6 h-6 text-white\">\n              {icon}\n            </div>\n          </div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <motion.div\n      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}\n      onClick={() => onClick?.(patient)}\n      whileHover={{\n        y: -6,\n        scale: 1.02,\n        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',\n        transition: { duration: 0.3 }\n      }}\n      whileTap={{ scale: 0.98 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50\" />\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n      />\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Premium Avatar */}\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ duration: 0.2 }}\n          >\n            {patient.profileImageUrl ? (\n              <img\n                src={patient.profileImageUrl}\n                alt={patient.fullName}\n                className=\"w-16 h-16 rounded-2xl object-cover shadow-lg\"\n              />\n            ) : (\n              <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                {getInitials(patient.fullName)}\n              </div>\n            )}\n            {/* Status Indicator */}\n            <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n              <div className=\"w-full h-full bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n          </motion.div>\n\n          {/* Patient Info */}\n          <div className=\"flex-1 min-w-0\">\n            <motion.div\n              className=\"flex items-center justify-between mb-2\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h3 className=\"font-bold text-gray-900 text-lg truncate\">\n                {patient.fullName}\n              </h3>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200\">\n                {patient.patientId}\n              </span>\n            </motion.div>\n\n            <motion.div\n              className=\"flex items-center space-x-3 text-sm\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  patient.gender === 'Male' ? 'bg-blue-500' :\n                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'\n                }`}></div>\n                <span className=\"text-gray-600 font-medium\">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n              </div>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <span className=\"text-gray-600 font-medium\">{patient.gender}</span>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <div className=\"flex items-center gap-1\">\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span className=\"text-gray-600 font-medium\">{patient.mobileNumber}</span>\n              </div>\n            </motion.div>\n\n            {patient.lastVisit && (\n              <motion.div\n                className=\"flex items-center gap-1 mt-2\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-xs text-gray-500 font-medium\">\n                  Last visit: {patient.lastVisit}\n                </p>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Arrow Indicator */}\n          <motion.div\n            className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-200\"\n            whileHover={{ x: 5 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.div>\n        </div>\n\n        {/* Bottom Border Animation */}\n        <motion.div\n          className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full\"\n          initial={{ width: 0 }}\n          whileHover={{ width: '100%' }}\n          transition={{ duration: 0.3 }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;;IAEnB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,aAAa,CAAC,OAAO,CAAC;IACxB,EAAE,WAAW,4CAA4C,WAAW;IACpE,EAAE,UAAU,mBAAmB,GAAG;IAClC,EAAE,UAAU;EACd,CAAC;IAED,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;AAiBO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;YACJ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,OAAO;YACL,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,OAAO;YACL,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,KAAK;YACH,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,MAAM;YACJ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,MAAM;IAExC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,qGAAqG,EAAE,WAAW;QAC9H,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCACV;;;;;;sCAGH,8OAAC;4BAAE,WAAU;sCACV;;;;;;wBAGF,0BACC,8OAAC;4BAAE,WAAU;sCACV;;;;;;wBAIJ,uBACC,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAW,CAAC,6CAA6C,EAC7D,MAAM,UAAU,GAAG,qBAAqB,gBACxC;;kDACA,8OAAC;wCAAK,WAAU;kDACb,MAAM,UAAU,GAAG,MAAM;;;;;;oCAE3B,KAAK,GAAG,CAAC,MAAM,KAAK;oCAAE;;;;;;;;;;;;;;;;;;gBAM9B,sBACC,8OAAC;oBAAI,WAAW,CAAC,eAAe,EAAE,aAAa,MAAM,EAAE;8BACrD,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAOf;AAiBO,MAAM,cAA0C,CAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,wFAAwF,EAAE,WAAW;QACjH,SAAS,IAAM,UAAU;QACzB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,UAAU;gCAAI;;oCAE3B,QAAQ,eAAe,iBACtB,8OAAC;wCACC,KAAK,QAAQ,eAAe;wCAC5B,KAAK,QAAQ,QAAQ;wCACrB,WAAU;;;;;iGAGZ,8OAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,QAAQ;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ;;;;;;0DAEnB,8OAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS;;;;;;;;;;;;kDAItB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,QAAQ,MAAM,KAAK,SAAS,gBAC5B,QAAQ,MAAM,KAAK,WAAW,gBAAgB,iBAC9C;;;;;;kEACF,8OAAC;wDAAK,WAAU;kEAA6B,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,MAAM,CAAC,GAAG;;;;;;;;;;;;0DAGtF,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,8OAAC;gDAAK,WAAU;0DAA6B,QAAQ,MAAM;;;;;;0DAE3D,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,8OAAC;wDAAK,WAAU;kEAA6B,QAAQ,YAAY;;;;;;;;;;;;;;;;;;oCAIpE,QAAQ,SAAS,kBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAE,WAAU;;oDAAoC;oDAClC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0CAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG;gCAAE;0CAEnB,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAO;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;AAKtC;AAWO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,8OAAC;QAAK,WAAW;;0BACf,8OAAC;gBACC,WAAW,CAAC,kCAAkC,EAAE,cAAc,mBAAmB,IAAI;gBACrF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 674, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n  fullScreen?: boolean;\n  className?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  size = 'md', \n  text = 'Loading...', \n  fullScreen = false,\n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-10 h-10',\n    lg: 'w-16 h-16'\n  };\n\n  const LoadingSpinner = () => (\n    <div className=\"relative\">\n      {/* Outer ring */}\n      <motion.div\n        className={`${sizeClasses[size]} border-4 border-blue-100 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Inner spinning ring */}\n      <motion.div\n        className={`absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-blue-600 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Medical cross in center */}\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <div className=\"w-3 h-3 relative\">\n          <div className=\"absolute inset-x-1/2 inset-y-0 w-0.5 bg-blue-600 transform -translate-x-1/2\" />\n          <div className=\"absolute inset-y-1/2 inset-x-0 h-0.5 bg-blue-600 transform -translate-y-1/2\" />\n        </div>\n      </div>\n    </div>\n  );\n\n  const LoadingDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-2 h-2 bg-blue-600 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const content = (\n    <motion.div\n      className={`flex flex-col items-center justify-center gap-4 ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <LoadingSpinner />\n      \n      {text && (\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <p className=\"text-gray-600 font-medium\">{text}</p>\n          <LoadingDots />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n\n  if (fullScreen) {\n    return (\n      <motion.div\n        className=\"fixed inset-0 bg-white bg-opacity-90 backdrop-blur-sm z-50 flex items-center justify-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n      >\n        {content}\n      </motion.div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton Loading Component\ninterface SkeletonProps {\n  className?: string;\n  lines?: number;\n  avatar?: boolean;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({ \n  className = '', \n  lines = 3, \n  avatar = false \n}) => {\n  return (\n    <div className={`animate-pulse ${className}`}>\n      <div className=\"flex items-start space-x-4\">\n        {avatar && (\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n        )}\n        <div className=\"flex-1 space-y-2\">\n          {Array.from({ length: lines }).map((_, index) => (\n            <div\n              key={index}\n              className={`h-4 bg-gray-200 rounded ${\n                index === lines - 1 ? 'w-3/4' : 'w-full'\n              }`}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Card Skeleton\nexport const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {\n  return (\n    <div className={`medical-card p-6 ${className}`}>\n      <div className=\"animate-pulse\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n          <div className=\"flex-1\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\" />\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"h-3 bg-gray-200 rounded\" />\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n          <div className=\"h-3 bg-gray-200 rounded w-4/6\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Button Loading State\ninterface ButtonLoadingProps {\n  loading?: boolean;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n}\n\nexport const ButtonLoading: React.FC<ButtonLoadingProps> = ({\n  loading = false,\n  children,\n  className = '',\n  onClick,\n  disabled = false\n}) => {\n  return (\n    <button\n      className={`btn-primary relative ${className} ${\n        loading || disabled ? 'opacity-70 cursor-not-allowed' : ''\n      }`}\n      onClick={onClick}\n      disabled={loading || disabled}\n    >\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      <span className={loading ? 'opacity-0' : 'opacity-100'}>\n        {children}\n      </span>\n    </button>\n  );\n};\n\nexport default Loading;\n"], "names": [], "mappings": ";;;;;;;AAGA;AAHA;;;AAYA,MAAM,UAAkC,CAAC,EACvC,OAAO,IAAI,EACX,OAAO,YAAY,EACnB,aAAa,KAAK,EAClB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,sCAAsC,CAAC;oBACvE,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,KAAK,CAAC,2DAA2D,CAAC;oBAC7G,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,QAAQ;oBACjB;mBAVK;;;;;;;;;;IAgBb,MAAM,wBACJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gDAAgD,EAAE,WAAW;QACzE,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;;;;;YAEA,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;;;;;;;;;;;;;;;;;IAMT,IAAI,YAAY;QACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;sBAElB;;;;;;IAGP;IAEA,OAAO;AACT;AASO,MAAM,WAAoC,CAAC,EAChD,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,SAAS,KAAK,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;gBACZ,wBACC,8OAAC;oBAAI,WAAU;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;4BAEC,WAAW,CAAC,wBAAwB,EAClC,UAAU,QAAQ,IAAI,UAAU,UAChC;2BAHG;;;;;;;;;;;;;;;;;;;;;AAUnB;AAGO,MAAM,eAAiD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC/E,qBACE,8OAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;kBAC7C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAGnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;AAWO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EACjB;IACC,qBACE,8OAAC;QACC,WAAW,CAAC,qBAAqB,EAAE,UAAU,CAAC,EAC5C,WAAW,WAAW,kCAAkC,IACxD;QACF,SAAS;QACT,UAAU,WAAW;;YAEpB,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGnB,8OAAC;gBAAK,WAAW,UAAU,cAAc;0BACtC;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 1042, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  ChartBarIcon, \n  UserGroupIcon, \n  HeartIcon,\n  CalendarIcon \n} from '@heroicons/react/24/outline';\nimport { InfoCard } from '@/components/ui/Card';\nimport Loading from '@/components/ui/Loading';\nimport { toast } from 'react-hot-toast';\n\nconst AnalyticsPage: React.FC = () => {\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/dashboard/stats');\n        const data = await response.json();\n        \n        if (data.success) {\n          setStats(data.data);\n        } else {\n          toast.error('Failed to fetch analytics data');\n        }\n      } catch (error) {\n        console.error('Error fetching analytics:', error);\n        toast.error('Failed to fetch analytics data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  if (loading) {\n    return <Loading text=\"Loading analytics...\" fullScreen />;\n  }\n\n  return (\n    <motion.div\n      className=\"space-y-8\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Header */}\n      <motion.div variants={itemVariants}>\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Medical Analytics\n        </h1>\n        <p className=\"text-gray-600\">\n          Comprehensive insights and reports for patient care management.\n        </p>\n      </motion.div>\n\n      {/* Overview Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <UserGroupIcon className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.totalPatients || 0}</h3>\n            <p className=\"text-gray-600\">Total Patients</p>\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <CalendarIcon className=\"w-12 h-12 text-green-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.newPatientsThisMonth || 0}</h3>\n            <p className=\"text-gray-600\">New This Month</p>\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <ChartBarIcon className=\"w-12 h-12 text-purple-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.averageAge || 0}</h3>\n            <p className=\"text-gray-600\">Average Age</p>\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <HeartIcon className=\"w-12 h-12 text-red-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.criticalCases || 0}</h3>\n            <p className=\"text-gray-600\">Critical Cases</p>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Detailed Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Gender Distribution */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"Gender Distribution\" icon={<UserGroupIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-4\">\n              {stats?.genderDistribution && Object.entries(stats.genderDistribution).map(([gender, count]) => (\n                <div key={gender} className=\"flex items-center justify-between\">\n                  <span className=\"capitalize text-gray-700\">{gender}</span>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-600 h-2 rounded-full\" \n                        style={{ \n                          width: `${(count as number / stats.totalPatients) * 100}%` \n                        }}\n                      />\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 w-8\">{count as number}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </InfoCard>\n        </motion.div>\n\n        {/* Blood Group Distribution */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"Blood Group Distribution\" icon={<HeartIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-3\">\n              {stats?.bloodGroupDistribution && Object.entries(stats.bloodGroupDistribution)\n                .sort(([,a], [,b]) => (b as number) - (a as number))\n                .slice(0, 6)\n                .map(([bloodGroup, count]) => (\n                <div key={bloodGroup} className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-700 font-medium\">{bloodGroup}</span>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-24 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-red-500 h-2 rounded-full\" \n                        style={{ \n                          width: `${(count as number / Math.max(...Object.values(stats.bloodGroupDistribution))) * 100}%` \n                        }}\n                      />\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 w-6\">{count as number}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </InfoCard>\n        </motion.div>\n\n        {/* BMI Distribution */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"BMI Distribution\" icon={<ChartBarIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-4\">\n              {stats?.bmiDistribution && Object.entries(stats.bmiDistribution).map(([category, count]) => {\n                const colors = {\n                  underweight: 'bg-blue-500',\n                  normal: 'bg-green-500',\n                  overweight: 'bg-yellow-500',\n                  obese: 'bg-red-500'\n                };\n                const total = Object.values(stats.bmiDistribution).reduce((a: number, b: number) => a + b, 0);\n                \n                return (\n                  <div key={category} className=\"flex items-center justify-between\">\n                    <span className=\"capitalize text-gray-700\">{category}</span>\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                        <div \n                          className={`h-2 rounded-full ${colors[category as keyof typeof colors]}`}\n                          style={{ \n                            width: total > 0 ? `${((count as number) / total) * 100}%` : '0%'\n                          }}\n                        />\n                      </div>\n                      <span className=\"text-sm font-medium text-gray-900 w-8\">{count as number}</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </InfoCard>\n        </motion.div>\n\n        {/* Recent Activity */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"Recent Activity\" icon={<CalendarIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-4\">\n              {stats?.recentActivity?.length > 0 ? (\n                stats.recentActivity.map((activity: any, index: number) => (\n                  <div key={index} className=\"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2\" />\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium text-gray-900\">{activity.action}</p>\n                      <p className=\"text-xs text-gray-600\">Patient: {activity.patient}</p>\n                      <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <p className=\"text-gray-500 text-center py-4\">No recent activity</p>\n              )}\n            </div>\n          </InfoCard>\n        </motion.div>\n      </div>\n\n      {/* Summary */}\n      <motion.div variants={itemVariants}>\n        <div className=\"medical-card p-6 bg-gradient-to-r from-blue-50 to-teal-50 border border-blue-200\">\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">Analytics Summary</h3>\n          <p className=\"text-blue-700 text-sm\">\n            Your medical practice is serving <strong>{stats?.totalPatients || 0} patients</strong> with \n            an average age of <strong>{stats?.averageAge || 0} years</strong>. \n            This month, you've registered <strong>{stats?.newPatientsThisMonth || 0} new patients</strong>.\n            {stats?.criticalCases > 0 && (\n              <span className=\"text-red-700 font-medium\">\n                {' '}Please note: {stats.criticalCases} patients require immediate attention.\n              </span>\n            )}\n          </p>\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default AnalyticsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAZA;;;;;;;;AAcA,MAAM,gBAA0B;IAC9B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB,OAAO;oBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,6BAA6B;gBAC3C,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,IAAI,SAAS;QACX,qBAAO,8OAAC,mIAAA,CAAA,UAAO;YAAC,MAAK;YAAuB,UAAU;;;;;;IACxD;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,8OAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,yNAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,8OAAC;oCAAG,WAAU;8CAAoC,OAAO,iBAAiB;;;;;;8CAC1E,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAAoC,OAAO,wBAAwB;;;;;;8CACjF,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,uNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,8OAAC;oCAAG,WAAU;8CAAoC,OAAO,cAAc;;;;;;8CACvE,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAG,WAAU;8CAAoC,OAAO,iBAAiB;;;;;;8CAC1E,8OAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAMnC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAsB,oBAAM,8OAAC,yNAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;sCACnE,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,sBAAsB,OAAO,OAAO,CAAC,MAAM,kBAAkB,EAAE,GAAG,CAAC,CAAC,CAAC,QAAQ,MAAM,iBACzF,8OAAC;wCAAiB,WAAU;;0DAC1B,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,GAAG,AAAC,QAAkB,MAAM,aAAa,GAAI,IAAI,CAAC,CAAC;4DAC5D;;;;;;;;;;;kEAGJ,8OAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;;uCAXnD;;;;;;;;;;;;;;;;;;;;kCAoBlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAA2B,oBAAM,8OAAC,iNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCACpE,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,0BAA0B,OAAO,OAAO,CAAC,MAAM,sBAAsB,EAC1E,IAAI,CAAC,CAAC,GAAE,EAAE,EAAE,GAAE,EAAE,GAAK,AAAC,IAAgB,GACtC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,CAAC,YAAY,MAAM,iBACzB,8OAAC;wCAAqB,WAAU;;0DAC9B,8OAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,GAAG,AAAC,QAAkB,KAAK,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,sBAAsB,KAAM,IAAI,CAAC,CAAC;4DACjG;;;;;;;;;;;kEAGJ,8OAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;;uCAXnD;;;;;;;;;;;;;;;;;;;;kCAoBlB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAmB,oBAAM,8OAAC,uNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;sCAC/D,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,mBAAmB,OAAO,OAAO,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,MAAM;oCACrF,MAAM,SAAS;wCACb,aAAa;wCACb,QAAQ;wCACR,YAAY;wCACZ,OAAO;oCACT;oCACA,MAAM,QAAQ,OAAO,MAAM,CAAC,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG;oCAE3F,qBACE,8OAAC;wCAAmB,WAAU;;0DAC5B,8OAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DACC,WAAW,CAAC,iBAAiB,EAAE,MAAM,CAAC,SAAgC,EAAE;4DACxE,OAAO;gEACL,OAAO,QAAQ,IAAI,GAAG,AAAE,QAAmB,QAAS,IAAI,CAAC,CAAC,GAAG;4DAC/D;;;;;;;;;;;kEAGJ,8OAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;;uCAXnD;;;;;gCAed;;;;;;;;;;;;;;;;kCAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,8OAAC,gIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAkB,oBAAM,8OAAC,uNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;sCAC9D,cAAA,8OAAC;gCAAI,WAAU;0CACZ,OAAO,gBAAgB,SAAS,IAC/B,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,UAAe,sBACvC,8OAAC;wCAAgB,WAAU;;0DACzB,8OAAC;gDAAI,WAAU;;;;;;0DACf,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEAAqC,SAAS,MAAM;;;;;;kEACjE,8OAAC;wDAAE,WAAU;;4DAAwB;4DAAU,SAAS,OAAO;;;;;;;kEAC/D,8OAAC;wDAAE,WAAU;kEAAyB,SAAS,IAAI;;;;;;;;;;;;;uCAL7C;;;;kGAUZ,8OAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,8OAAC;4BAAE,WAAU;;gCAAwB;8CACF,8OAAC;;wCAAQ,OAAO,iBAAiB;wCAAE;;;;;;;gCAAkB;8CACpE,8OAAC;;wCAAQ,OAAO,cAAc;wCAAE;;;;;;;gCAAe;8CACnC,8OAAC;;wCAAQ,OAAO,wBAAwB;wCAAE;;;;;;;gCAAsB;gCAC7F,OAAO,gBAAgB,mBACtB,8OAAC;oCAAK,WAAU;;wCACb;wCAAI;wCAAc,MAAM,aAAa;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;uCAEe", "debugId": null}}]}