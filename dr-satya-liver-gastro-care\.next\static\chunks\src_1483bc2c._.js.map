{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: {\n      gradient: 'from-blue-500 to-blue-600',\n      iconBg: 'bg-blue-500',\n      text: 'text-blue-600'\n    },\n    green: {\n      gradient: 'from-emerald-500 to-emerald-600',\n      iconBg: 'bg-emerald-500',\n      text: 'text-emerald-600'\n    },\n    amber: {\n      gradient: 'from-amber-500 to-amber-600',\n      iconBg: 'bg-amber-500',\n      text: 'text-amber-600'\n    },\n    red: {\n      gradient: 'from-red-500 to-red-600',\n      iconBg: 'bg-red-500',\n      text: 'text-red-600'\n    },\n    teal: {\n      gradient: 'from-teal-500 to-teal-600',\n      iconBg: 'bg-teal-500',\n      text: 'text-teal-600'\n    }\n  };\n\n  const currentColor = colorClasses[color];\n\n  return (\n    <motion.div\n      className={`bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-md transition-all duration-200 ${className}`}\n      whileHover={{ y: -2 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1 uppercase tracking-wide\">\n            {title}\n          </p>\n\n          <p className=\"text-3xl font-bold text-gray-900 mb-2\">\n            {value}\n          </p>\n\n          {subtitle && (\n            <p className=\"text-sm text-gray-600\">\n              {subtitle}\n            </p>\n          )}\n\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              <span className={`inline-flex items-center text-sm font-medium ${\n                trend.isPositive ? 'text-emerald-600' : 'text-red-600'\n              }`}>\n                <span className=\"mr-1\">\n                  {trend.isPositive ? '↗' : '↘'}\n                </span>\n                {Math.abs(trend.value)}% vs last month\n              </span>\n            </div>\n          )}\n        </div>\n\n        {icon && (\n          <div className={`p-3 rounded-xl ${currentColor.iconBg}`}>\n            <div className=\"w-6 h-6 text-white\">\n              {icon}\n            </div>\n          </div>\n        )}\n      </div>\n    </motion.div>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <motion.div\n      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}\n      onClick={() => onClick?.(patient)}\n      whileHover={{\n        y: -6,\n        scale: 1.02,\n        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',\n        transition: { duration: 0.3 }\n      }}\n      whileTap={{ scale: 0.98 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50\" />\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n      />\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Premium Avatar */}\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ duration: 0.2 }}\n          >\n            {patient.profileImageUrl ? (\n              <img\n                src={patient.profileImageUrl}\n                alt={patient.fullName}\n                className=\"w-16 h-16 rounded-2xl object-cover shadow-lg\"\n              />\n            ) : (\n              <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                {getInitials(patient.fullName)}\n              </div>\n            )}\n            {/* Status Indicator */}\n            <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n              <div className=\"w-full h-full bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n          </motion.div>\n\n          {/* Patient Info */}\n          <div className=\"flex-1 min-w-0\">\n            <motion.div\n              className=\"flex items-center justify-between mb-2\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h3 className=\"font-bold text-gray-900 text-lg truncate\">\n                {patient.fullName}\n              </h3>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200\">\n                {patient.patientId}\n              </span>\n            </motion.div>\n\n            <motion.div\n              className=\"flex items-center space-x-3 text-sm\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  patient.gender === 'Male' ? 'bg-blue-500' :\n                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'\n                }`}></div>\n                <span className=\"text-gray-600 font-medium\">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n              </div>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <span className=\"text-gray-600 font-medium\">{patient.gender}</span>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <div className=\"flex items-center gap-1\">\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span className=\"text-gray-600 font-medium\">{patient.mobileNumber}</span>\n              </div>\n            </motion.div>\n\n            {patient.lastVisit && (\n              <motion.div\n                className=\"flex items-center gap-1 mt-2\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-xs text-gray-500 font-medium\">\n                  Last visit: {patient.lastVisit}\n                </p>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Arrow Indicator */}\n          <motion.div\n            className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-200\"\n            whileHover={{ x: 5 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.div>\n        </div>\n\n        {/* Bottom Border Animation */}\n        <motion.div\n          className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full\"\n          initial={{ width: 0 }}\n          whileHover={{ width: '100%' }}\n          transition={{ duration: 0.3 }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,AAAC,4BAGjB,OADA,cAAc,CAAC,QAAQ,EAAC,WAExB,OADA,aAAa,CAAC,OAAO,EAAC,UAEtB,OADA,WAAW,4CAA4C,YAAW,UAElE,OADA,UAAU,mBAAmB,IAAG,UACtB,OAAV,WAAU;IAGd,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;KApDM;AAqEC,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;YACJ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,OAAO;YACL,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,OAAO;YACL,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,KAAK;YACH,UAAU;YACV,QAAQ;YACR,MAAM;QACR;QACA,MAAM;YACJ,UAAU;YACV,QAAQ;YACR,MAAM;QACR;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,MAAM;IAExC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,wGAAiH,OAAV;QACnH,YAAY;YAAE,GAAG,CAAC;QAAE;QACpB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCACV;;;;;;sCAGH,6LAAC;4BAAE,WAAU;sCACV;;;;;;wBAGF,0BACC,6LAAC;4BAAE,WAAU;sCACV;;;;;;wBAIJ,uBACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAW,AAAC,gDAEjB,OADC,MAAM,UAAU,GAAG,qBAAqB;;kDAExC,6LAAC;wCAAK,WAAU;kDACb,MAAM,UAAU,GAAG,MAAM;;;;;;oCAE3B,KAAK,GAAG,CAAC,MAAM,KAAK;oCAAE;;;;;;;;;;;;;;;;;;gBAM9B,sBACC,6LAAC;oBAAI,WAAW,AAAC,kBAAqC,OAApB,aAAa,MAAM;8BACnD,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAOf;MAvFa;AAwGN,MAAM,cAA0C;QAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,2FAAoG,OAAV;QACtG,SAAS,IAAM,oBAAA,8BAAA,QAAU;QACzB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,UAAU;gCAAI;;oCAE3B,QAAQ,eAAe,iBACtB,6LAAC;wCACC,KAAK,QAAQ,eAAe;wCAC5B,KAAK,QAAQ,QAAQ;wCACrB,WAAU;;;;;iGAGZ,6LAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,QAAQ;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ;;;;;;0DAEnB,6LAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS;;;;;;;;;;;;kDAItB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAGhB,OAFC,QAAQ,MAAM,KAAK,SAAS,gBAC5B,QAAQ,MAAM,KAAK,WAAW,gBAAgB;;;;;;kEAEhD,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,GAAG,GAAG,AAAC,GAAc,OAAZ,QAAQ,GAAG,EAAC,YAAU;;;;;;;;;;;;0DAGtF,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAK,WAAU;0DAA6B,QAAQ,MAAM;;;;;;0DAE3D,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,YAAY;;;;;;;;;;;;;;;;;;oCAIpE,QAAQ,SAAS,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAE,WAAU;;oDAAoC;oDAClC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0CAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG;gCAAE;0CAEnB,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAO;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;AAKtC;MAhJa;AA2JN,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAK,WAAW;;0BACf,6LAAC;gBACC,WAAW,AAAC,qCAAwE,OAApC,cAAc,mBAAmB;gBACjF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GAnDa;MAAA;uCAqDE", "debugId": null}}, {"offset": {"line": 690, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserGroupIcon,\n  UserPlusIcon,\n  ChartBarIcon,\n  HeartIcon\n} from '@heroicons/react/24/outline';\nimport { StatCard } from '@/components/ui/Card';\nimport { useAuthStore } from '@/store';\nimport { toast } from 'react-hot-toast';\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuthStore();\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/dashboard/stats');\n        const data = await response.json();\n\n        if (data.success) {\n          setStats(data.data);\n        } else {\n          toast.error('Failed to fetch dashboard statistics');\n        }\n      } catch (error) {\n        console.error('Error fetching stats:', error);\n        toast.error('Failed to fetch dashboard statistics');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  const statCards = stats ? [\n    {\n      title: 'Total Patients',\n      value: stats.totalPatients.toLocaleString(),\n      subtitle: 'Active patients',\n      icon: <UserGroupIcon className=\"w-6 h-6\" />,\n      trend: { value: 12, isPositive: true },\n      color: 'blue' as const,\n    },\n    {\n      title: 'New Patients',\n      value: stats.newPatientsThisMonth.toString(),\n      subtitle: 'This month',\n      icon: <UserPlusIcon className=\"w-6 h-6\" />,\n      trend: { value: 8, isPositive: true },\n      color: 'green' as const,\n    },\n    {\n      title: 'Appointments',\n      value: stats.appointments.toString(),\n      subtitle: 'This week',\n      icon: <ChartBarIcon className=\"w-6 h-6\" />,\n      trend: { value: 3, isPositive: false },\n      color: 'amber' as const,\n    },\n    {\n      title: 'Critical Cases',\n      value: stats.criticalCases.toString(),\n      subtitle: 'Require attention',\n      icon: <HeartIcon className=\"w-6 h-6\" />,\n      trend: { value: 2, isPositive: false },\n      color: 'red' as const,\n    },\n  ] : [];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <motion.div\n      className=\"space-y-6\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Clean Header */}\n      <motion.div variants={itemVariants} className=\"mb-8\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-2\">\n              Welcome back, {user ? `${user.firstName} ${user.lastName}` : 'Doctor'}\n            </h1>\n            <p className=\"text-gray-600 text-lg\">\n              Here's what's happening with your patients today.\n            </p>\n          </div>\n\n          {/* Time Display */}\n          <div className=\"text-right\">\n            <div className=\"text-2xl font-bold text-gray-800\">\n              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n            </div>\n            <div className=\"text-sm text-gray-500\">\n              {new Date().toLocaleDateString('en-US', {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric'\n              })}\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Stats Grid - Clean Layout */}\n      <motion.div\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n        variants={containerVariants}\n      >\n        {loading ? (\n          // Loading skeletons\n          Array.from({ length: 4 }).map((_, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              <div className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 animate-pulse\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"h-4 bg-gray-200 rounded mb-3\"></div>\n                    <div className=\"h-8 bg-gray-200 rounded mb-2\"></div>\n                    <div className=\"h-3 bg-gray-200 rounded w-2/3\"></div>\n                  </div>\n                  <div className=\"w-12 h-12 bg-gray-200 rounded-lg\"></div>\n                </div>\n              </div>\n            </motion.div>\n          ))\n        ) : (\n          statCards.map((stat, index) => (\n            <motion.div\n              key={stat.title}\n              variants={itemVariants}\n              whileHover={{\n                y: -4,\n                transition: { duration: 0.2 }\n              }}\n            >\n              <StatCard {...stat} />\n            </motion.div>\n          ))\n        )}\n      </motion.div>\n\n      {/* Quick Actions - Clean Layout */}\n      <motion.div variants={itemVariants}>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Quick Actions</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <motion.div\n            className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 cursor-pointer group hover:shadow-md transition-all duration-200\"\n            whileHover={{ y: -2 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={() => window.location.href = '/patients/new'}\n          >\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors\">\n                <UserPlusIcon className=\"w-6 h-6 text-blue-600\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">Add New Patient</h3>\n                <p className=\"text-sm text-gray-600\">Register a new patient in the system</p>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 cursor-pointer group hover:shadow-md transition-all duration-200\"\n            whileHover={{ y: -2 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={() => window.location.href = '/patients'}\n          >\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"p-3 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors\">\n                <UserGroupIcon className=\"w-6 h-6 text-emerald-600\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">View All Patients</h3>\n                <p className=\"text-sm text-gray-600\">Browse and search patient records</p>\n              </div>\n            </div>\n          </motion.div>\n\n          <motion.div\n            className=\"bg-white rounded-xl p-6 shadow-sm border border-gray-100 cursor-pointer group hover:shadow-md transition-all duration-200\"\n            whileHover={{ y: -2 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={() => window.location.href = '/analytics'}\n          >\n            <div className=\"flex items-center space-x-4\">\n              <div className=\"p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors\">\n                <ChartBarIcon className=\"w-6 h-6 text-purple-600\" />\n              </div>\n              <div>\n                <h3 className=\"font-semibold text-gray-900\">View Analytics</h3>\n                <p className=\"text-sm text-gray-600\">Check medical reports and insights</p>\n              </div>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Recent Activity - Clean Layout */}\n      <motion.div variants={itemVariants}>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Recent Activity</h2>\n        <div className=\"bg-white rounded-xl shadow-sm border border-gray-100 p-6\">\n          <div className=\"space-y-4\">\n            {loading ? (\n              // Loading skeleton\n              Array.from({ length: 5 }).map((_, index) => (\n                <div key={index} className=\"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0 animate-pulse\">\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-gray-200 rounded-full\"></div>\n                    <div className=\"flex-1\">\n                      <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\"></div>\n                      <div className=\"h-3 bg-gray-200 rounded w-1/2\"></div>\n                    </div>\n                  </div>\n                  <div className=\"h-3 bg-gray-200 rounded w-16\"></div>\n                </div>\n              ))\n            ) : (\n              stats?.recentActivity?.map((activity: any, index: number) => (\n                <motion.div\n                  key={index}\n                  className=\"flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded-lg px-2 transition-colors\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center\">\n                      <UserPlusIcon className=\"w-5 h-5 text-blue-600\" />\n                    </div>\n                    <div>\n                      <p className=\"font-medium text-gray-900\">{activity.action}</p>\n                      <p className=\"text-sm text-gray-600\">Patient: {activity.patient}</p>\n                    </div>\n                  </div>\n                  <span className=\"text-sm text-gray-500\">{activity.time}</span>\n                </motion.div>\n              )) || (\n                <div className=\"text-center py-8\">\n                  <div className=\"w-12 h-12 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center\">\n                    <svg className=\"w-6 h-6 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-gray-500\">No recent activity</p>\n                </div>\n              )\n            )}\n          </div>\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;;;AAZA;;;;;;;AAcA,MAAM,gBAA0B;QAkOlB;;IAjOZ,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;sDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,KAAK,OAAO,EAAE;4BAChB,SAAS,KAAK,IAAI;wBACpB,OAAO;4BACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,YAAY,QAAQ;QACxB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,cAAc;YACzC,UAAU;YACV,oBAAM,6LAAC,4NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;gBAAE,OAAO;gBAAI,YAAY;YAAK;YACrC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,oBAAoB,CAAC,QAAQ;YAC1C,UAAU;YACV,oBAAM,6LAAC,0NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAK;YACpC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,YAAY,CAAC,QAAQ;YAClC,UAAU;YACV,oBAAM,6LAAC,0NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAM;YACrC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,UAAU;YACV,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAM;YACrC,OAAO;QACT;KACD,GAAG,EAAE;IAEN,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;gBAAc,WAAU;0BAC5C,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;;wCAAkH;wCAC/G,OAAO,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ,IAAK;;;;;;;8CAE/D,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAMvC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACZ,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;wCAAE,MAAM;wCAAW,QAAQ;oCAAU;;;;;;8CAE1E,6LAAC;oCAAI,WAAU;8CACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;wCACtC,SAAS;wCACT,OAAO;wCACP,KAAK;oCACP;;;;;;;;;;;;;;;;;;;;;;;0BAOR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAET,UACC,oBAAoB;gBACpB,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAa,UAAU;kCAChC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;uBARJ;;;;oEAcnB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,YAAY;4BACV,GAAG,CAAC;4BACJ,YAAY;gCAAE,UAAU;4BAAI;wBAC9B;kCAEA,cAAA,6LAAC,mIAAA,CAAA,WAAQ;4BAAE,GAAG,IAAI;;;;;;uBAPb,KAAK,KAAK;;;;;;;;;;0BAcvB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCACpB,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0CAEtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCACpB,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0CAEtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4NAAA,CAAA,gBAAa;gDAAC,WAAU;;;;;;;;;;;sDAE3B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;0CAK3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG,CAAC;gCAAE;gCACpB,UAAU;oCAAE,OAAO;gCAAK;gCACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;0CAEtC,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;sDAE1B,6LAAC;;8DACC,6LAAC;oDAAG,WAAU;8DAA8B;;;;;;8DAC5C,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ/C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UACC,mBAAmB;4BACnB,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;;;;;sEACf,6LAAC;4DAAI,WAAU;;;;;;;;;;;;;;;;;;sDAGnB,6LAAC;4CAAI,WAAU;;;;;;;mCARP;;;;gFAYZ,CAAA,kBAAA,6BAAA,wBAAA,MAAO,cAAc,cAArB,4CAAA,sBAAuB,GAAG,CAAC,CAAC,UAAe,sBACzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;;sDAEjC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,0NAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;8DAE1B,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA6B,SAAS,MAAM;;;;;;sEACzD,6LAAC;4DAAE,WAAU;;gEAAwB;gEAAU,SAAS,OAAO;;;;;;;;;;;;;;;;;;;sDAGnE,6LAAC;4CAAK,WAAU;sDAAyB,SAAS,IAAI;;;;;;;mCAfjD;;;;gGAkBP,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAAwB,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAC/E,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS/C;GArQM;;QACa,wHAAA,CAAA,eAAY;;;KADzB;uCAuQS", "debugId": null}}]}