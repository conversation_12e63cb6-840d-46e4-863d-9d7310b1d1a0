{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showText?: boolean;\n  className?: string;\n  variant?: 'default' | 'minimal' | 'premium';\n}\n\nconst Logo: React.FC<LogoProps> = ({\n  size = 'md',\n  showText = true,\n  className = '',\n  variant = 'premium'\n}) => {\n  const sizeClasses = {\n    sm: 'w-10 h-10',\n    md: 'w-14 h-14',\n    lg: 'w-20 h-20',\n    xl: 'w-28 h-28'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-xl',\n    lg: 'text-2xl',\n    xl: 'text-4xl'\n  };\n\n  const logoVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 0.5,\n      rotate: -180\n    },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      rotate: 0,\n      transition: {\n        duration: 1.2,\n        ease: [0.6, -0.05, 0.01, 0.99],\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const iconVariants = {\n    hidden: { scale: 0, rotate: -90 },\n    visible: {\n      scale: 1,\n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 200,\n        damping: 15\n      }\n    }\n  };\n\n  const textVariants = {\n    hidden: { opacity: 0, x: -30 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const pulseVariants = {\n    pulse: {\n      scale: [1, 1.05, 1],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`flex items-center gap-4 ${className}`}\n      variants={logoVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"pulse\"\n    >\n      {/* Premium Medical Logo Icon */}\n      <motion.div\n        className={`${sizeClasses[size]} relative`}\n        variants={iconVariants}\n        whileHover={{\n          scale: 1.1,\n          rotate: 5,\n          transition: { duration: 0.3 }\n        }}\n      >\n        <svg\n          viewBox=\"0 0 120 120\"\n          className=\"w-full h-full drop-shadow-lg\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <defs>\n            {/* Premium Gradients */}\n            <linearGradient id=\"premiumGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#0066cc\" />\n              <stop offset=\"30%\" stopColor=\"#1a7ae8\" />\n              <stop offset=\"70%\" stopColor=\"#00a693\" />\n              <stop offset=\"100%\" stopColor=\"#1ab8a6\" />\n            </linearGradient>\n\n            <linearGradient id=\"crossGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#ffffff\" />\n              <stop offset=\"100%\" stopColor=\"#f0f9ff\" />\n            </linearGradient>\n\n            <linearGradient id=\"accentGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#10b981\" />\n              <stop offset=\"100%\" stopColor=\"#34d399\" />\n            </linearGradient>\n\n            {/* Glow Filter */}\n            <filter id=\"glow\">\n              <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n              <feMerge>\n                <feMergeNode in=\"coloredBlur\"/>\n                <feMergeNode in=\"SourceGraphic\"/>\n              </feMerge>\n            </filter>\n\n            {/* Shadow Filter */}\n            <filter id=\"dropshadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n              <feDropShadow dx=\"0\" dy=\"4\" stdDeviation=\"4\" floodColor=\"#0066cc\" floodOpacity=\"0.3\"/>\n            </filter>\n          </defs>\n\n          {/* Outer Ring with Animation */}\n          <motion.circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"55\"\n            fill=\"none\"\n            stroke=\"url(#premiumGradient)\"\n            strokeWidth=\"2\"\n            strokeDasharray=\"10 5\"\n            initial={{ rotate: 0 }}\n            animate={{ rotate: 360 }}\n            transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n            style={{ transformOrigin: \"60px 60px\" }}\n          />\n\n          {/* Main Circle Background */}\n          <circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"48\"\n            fill=\"url(#premiumGradient)\"\n            filter=\"url(#dropshadow)\"\n          />\n\n          {/* Inner Glow Circle */}\n          <circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"42\"\n            fill=\"none\"\n            stroke=\"rgba(255, 255, 255, 0.3)\"\n            strokeWidth=\"1\"\n          />\n\n          {/* Medical Cross with 3D Effect */}\n          <g transform=\"translate(60, 60)\" filter=\"url(#glow)\">\n            {/* Cross Shadow */}\n            <g transform=\"translate(2, 2)\" opacity=\"0.3\">\n              <rect x=\"-4\" y=\"-24\" width=\"8\" height=\"48\" fill=\"#0066cc\" rx=\"4\" />\n              <rect x=\"-24\" y=\"-4\" width=\"48\" height=\"8\" fill=\"#0066cc\" rx=\"4\" />\n            </g>\n\n            {/* Main Cross */}\n            <rect x=\"-4\" y=\"-24\" width=\"8\" height=\"48\" fill=\"url(#crossGradient)\" rx=\"4\" />\n            <rect x=\"-24\" y=\"-4\" width=\"48\" height=\"8\" fill=\"url(#crossGradient)\" rx=\"4\" />\n\n            {/* Cross Highlights */}\n            <rect x=\"-2\" y=\"-24\" width=\"4\" height=\"48\" fill=\"rgba(255, 255, 255, 0.4)\" rx=\"2\" />\n            <rect x=\"-24\" y=\"-2\" width=\"48\" height=\"4\" fill=\"rgba(255, 255, 255, 0.4)\" rx=\"2\" />\n          </g>\n\n          {/* Decorative Elements */}\n          <g transform=\"translate(60, 60)\">\n            {/* DNA Helix Accent */}\n            <motion.path\n              d=\"M -35 -20 Q -30 -15 -25 -20 Q -20 -25 -15 -20 Q -10 -15 -5 -20\"\n              stroke=\"url(#accentGradient)\"\n              strokeWidth=\"2\"\n              fill=\"none\"\n              opacity=\"0.6\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 2, delay: 0.5 }}\n            />\n\n            {/* Heartbeat Line */}\n            <motion.path\n              d=\"M -35 25 L -25 25 L -20 15 L -15 35 L -10 5 L -5 25 L 5 25\"\n              stroke=\"url(#accentGradient)\"\n              strokeWidth=\"2\"\n              fill=\"none\"\n              opacity=\"0.7\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 1.5, delay: 1 }}\n            />\n\n            {/* Floating Particles */}\n            {[...Array(6)].map((_, i) => (\n              <motion.circle\n                key={i}\n                cx={Math.cos(i * 60 * Math.PI / 180) * 35}\n                cy={Math.sin(i * 60 * Math.PI / 180) * 35}\n                r=\"2\"\n                fill=\"rgba(255, 255, 255, 0.8)\"\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{\n                  scale: [0, 1, 0],\n                  opacity: [0, 1, 0]\n                }}\n                transition={{\n                  duration: 3,\n                  delay: i * 0.2,\n                  repeat: Infinity,\n                  repeatDelay: 2\n                }}\n              />\n            ))}\n          </g>\n        </svg>\n      </motion.div>\n\n      {/* Premium Text Logo */}\n      {showText && (\n        <motion.div\n          className=\"flex flex-col\"\n          variants={textVariants}\n        >\n          <motion.h1\n            className={`font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-teal-600 bg-clip-text text-transparent leading-tight ${textSizeClasses[size]}`}\n            whileHover={{\n              scale: 1.02,\n              transition: { duration: 0.2 }\n            }}\n          >\n            Dr Satya's\n          </motion.h1>\n          <motion.p\n            className={`font-semibold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent leading-tight ${\n              size === 'sm' ? 'text-xs' :\n              size === 'md' ? 'text-sm' :\n              size === 'lg' ? 'text-lg' : 'text-xl'\n            }`}\n            whileHover={{\n              scale: 1.02,\n              transition: { duration: 0.2 }\n            }}\n          >\n            Liver & Gastro Care\n          </motion.p>\n          {(size === 'lg' || size === 'xl') && (\n            <motion.p\n              className=\"text-xs text-gray-500 mt-1 font-medium tracking-wide\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.5 }}\n            >\n              Excellence in Digestive Health\n            </motion.p>\n          )}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAYA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACd,UAAU,SAAS,EACpB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,QAAQ,CAAC;QACX;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK,CAAC;oBAAM;oBAAM;iBAAK;gBAC9B,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAG;QAChC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO;YACL,OAAO;gBAAC;gBAAG;gBAAM;aAAE;YACnB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,wBAAwB,EAAE,WAAW;QACjD,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;;0BAGX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC1C,UAAU;gBACV,YAAY;oBACV,OAAO;oBACP,QAAQ;oBACR,YAAY;wBAAE,UAAU;oBAAI;gBAC9B;0BAEA,cAAA,8OAAC;oBACC,SAAQ;oBACR,WAAU;oBACV,OAAM;;sCAEN,8OAAC;;8CAEC,8OAAC;oCAAe,IAAG;oCAAkB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAChE,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,8OAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAGhC,8OAAC;oCAAe,IAAG;oCAAgB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC9D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAGhC,8OAAC;oCAAe,IAAG;oCAAiB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC/D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAIhC,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAe,cAAa;4CAAI,QAAO;;;;;;sDACxC,8OAAC;;8DACC,8OAAC;oDAAY,IAAG;;;;;;8DAChB,8OAAC;oDAAY,IAAG;;;;;;;;;;;;;;;;;;8CAKpB,8OAAC;oCAAO,IAAG;oCAAa,GAAE;oCAAO,GAAE;oCAAO,OAAM;oCAAO,QAAO;8CAC5D,cAAA,8OAAC;wCAAa,IAAG;wCAAI,IAAG;wCAAI,cAAa;wCAAI,YAAW;wCAAU,cAAa;;;;;;;;;;;;;;;;;sCAKnF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,iBAAgB;4BAChB,SAAS;gCAAE,QAAQ;4BAAE;4BACrB,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,YAAY;gCAAE,UAAU;gCAAI,QAAQ;gCAAU,MAAM;4BAAS;4BAC7D,OAAO;gCAAE,iBAAiB;4BAAY;;;;;;sCAIxC,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;;;;;;sCAIT,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;;;;;;sCAId,8OAAC;4BAAE,WAAU;4BAAoB,QAAO;;8CAEtC,8OAAC;oCAAE,WAAU;oCAAkB,SAAQ;;sDACrC,8OAAC;4CAAK,GAAE;4CAAK,GAAE;4CAAM,OAAM;4CAAI,QAAO;4CAAK,MAAK;4CAAU,IAAG;;;;;;sDAC7D,8OAAC;4CAAK,GAAE;4CAAM,GAAE;4CAAK,OAAM;4CAAK,QAAO;4CAAI,MAAK;4CAAU,IAAG;;;;;;;;;;;;8CAI/D,8OAAC;oCAAK,GAAE;oCAAK,GAAE;oCAAM,OAAM;oCAAI,QAAO;oCAAK,MAAK;oCAAsB,IAAG;;;;;;8CACzE,8OAAC;oCAAK,GAAE;oCAAM,GAAE;oCAAK,OAAM;oCAAK,QAAO;oCAAI,MAAK;oCAAsB,IAAG;;;;;;8CAGzE,8OAAC;oCAAK,GAAE;oCAAK,GAAE;oCAAM,OAAM;oCAAI,QAAO;oCAAK,MAAK;oCAA2B,IAAG;;;;;;8CAC9E,8OAAC;oCAAK,GAAE;oCAAM,GAAE;oCAAK,OAAM;oCAAK,QAAO;oCAAI,MAAK;oCAA2B,IAAG;;;;;;;;;;;;sCAIhF,8OAAC;4BAAE,WAAU;;8CAEX,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,SAAQ;oCACR,SAAS;wCAAE,YAAY;oCAAE;oCACzB,SAAS;wCAAE,YAAY;oCAAE;oCACzB,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAI;;;;;;8CAIxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,SAAQ;oCACR,SAAS;wCAAE,YAAY;oCAAE;oCACzB,SAAS;wCAAE,YAAY;oCAAE;oCACzB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAE;;;;;;gCAIvC;uCAAI,MAAM;iCAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO;wCACvC,IAAI,KAAK,GAAG,CAAC,IAAI,KAAK,KAAK,EAAE,GAAG,OAAO;wCACvC,GAAE;wCACF,MAAK;wCACL,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAG;6CAAE;4CAChB,SAAS;gDAAC;gDAAG;gDAAG;6CAAE;wCACpB;wCACA,YAAY;4CACV,UAAU;4CACV,OAAO,IAAI;4CACX,QAAQ;4CACR,aAAa;wCACf;uCAfK;;;;;;;;;;;;;;;;;;;;;;YAuBd,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,8GAA8G,EAAE,eAAe,CAAC,KAAK,EAAE;wBACnJ,YAAY;4BACV,OAAO;4BACP,YAAY;gCAAE,UAAU;4BAAI;wBAC9B;kCACD;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAW,CAAC,qGAAqG,EAC/G,SAAS,OAAO,YAChB,SAAS,OAAO,YAChB,SAAS,OAAO,YAAY,WAC5B;wBACF,YAAY;4BACV,OAAO;4BACP,YAAY;gCAAE,UAAU;4BAAI;wBAC9B;kCACD;;;;;;oBAGA,CAAC,SAAS,QAAQ,SAAS,IAAI,mBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;kCACzC;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 617, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, Patient, Comorbidity, DashboardStats } from '@/types';\n\n// Authentication Store\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: { email: string; password: string }) => Promise<boolean>;\n  logout: () => void;\n  setUser: (user: User) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      \n      login: async (credentials) => {\n        set({ isLoading: true });\n        try {\n          const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(credentials),\n          });\n\n          const data = await response.json();\n\n          if (response.ok && data.success) {\n            set({ user: data.user, isAuthenticated: true, isLoading: false });\n            return true;\n          }\n\n          set({ isLoading: false });\n          return false;\n        } catch (error) {\n          console.error('Login error:', error);\n          set({ isLoading: false });\n          return false;\n        }\n      },\n      \n      logout: () => {\n        set({ user: null, isAuthenticated: false });\n      },\n      \n      setUser: (user) => {\n        set({ user, isAuthenticated: true });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n\n// Patients Store\ninterface PatientsState {\n  patients: Patient[];\n  currentPatient: Patient | null;\n  isLoading: boolean;\n  searchQuery: string;\n  filters: {\n    gender?: string;\n    bloodGroup?: string;\n    ageRange?: { min: number; max: number };\n  };\n  \n  // Actions\n  setPatients: (patients: Patient[]) => void;\n  addPatient: (patient: Patient) => void;\n  updatePatient: (id: string, updates: Partial<Patient>) => void;\n  deletePatient: (id: string) => void;\n  setCurrentPatient: (patient: Patient | null) => void;\n  setSearchQuery: (query: string) => void;\n  setFilters: (filters: any) => void;\n  setLoading: (loading: boolean) => void;\n  \n  // Computed\n  filteredPatients: () => Patient[];\n}\n\nexport const usePatientsStore = create<PatientsState>((set, get) => ({\n  patients: [],\n  currentPatient: null,\n  isLoading: false,\n  searchQuery: '',\n  filters: {},\n  \n  setPatients: (patients) => set({ patients }),\n  \n  addPatient: (patient) => \n    set((state) => ({ patients: [...state.patients, patient] })),\n  \n  updatePatient: (id, updates) =>\n    set((state) => ({\n      patients: state.patients.map((p) => \n        p.id === id ? { ...p, ...updates } : p\n      ),\n      currentPatient: state.currentPatient?.id === id \n        ? { ...state.currentPatient, ...updates } \n        : state.currentPatient,\n    })),\n  \n  deletePatient: (id) =>\n    set((state) => ({\n      patients: state.patients.filter((p) => p.id !== id),\n      currentPatient: state.currentPatient?.id === id ? null : state.currentPatient,\n    })),\n  \n  setCurrentPatient: (patient) => set({ currentPatient: patient }),\n  \n  setSearchQuery: (searchQuery) => set({ searchQuery }),\n  \n  setFilters: (filters) => set({ filters }),\n  \n  setLoading: (isLoading) => set({ isLoading }),\n  \n  filteredPatients: () => {\n    const { patients, searchQuery, filters } = get();\n    \n    return patients.filter((patient) => {\n      // Search query filter\n      if (searchQuery) {\n        const query = searchQuery.toLowerCase();\n        const matchesSearch = \n          patient.fullName.toLowerCase().includes(query) ||\n          patient.patientId.toLowerCase().includes(query) ||\n          patient.mobileNumber.includes(query) ||\n          patient.email?.toLowerCase().includes(query);\n        \n        if (!matchesSearch) return false;\n      }\n      \n      // Gender filter\n      if (filters.gender && patient.gender !== filters.gender) {\n        return false;\n      }\n      \n      // Blood group filter\n      if (filters.bloodGroup && patient.bloodGroup !== filters.bloodGroup) {\n        return false;\n      }\n      \n      // Age range filter\n      if (filters.ageRange) {\n        const age = new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear();\n        if (age < filters.ageRange.min || age > filters.ageRange.max) {\n          return false;\n        }\n      }\n      \n      return true;\n    });\n  },\n}));\n\n// Comorbidities Store\ninterface ComorbiditiesState {\n  comorbidities: Comorbidity[];\n  isLoading: boolean;\n  setComorbidities: (comorbidities: Comorbidity[]) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useComorbiditiesStore = create<ComorbiditiesState>((set) => ({\n  comorbidities: [],\n  isLoading: false,\n  \n  setComorbidities: (comorbidities) => set({ comorbidities }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// Dashboard Store\ninterface DashboardState {\n  stats: DashboardStats | null;\n  isLoading: boolean;\n  setStats: (stats: DashboardStats) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useDashboardStore = create<DashboardState>((set) => ({\n  stats: null,\n  isLoading: false,\n  \n  setStats: (stats) => set({ stats }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// UI Store for global UI state\ninterface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    message: string;\n    timestamp: number;\n  }>;\n  \n  toggleSidebar: () => void;\n  setTheme: (theme: 'light' | 'dark') => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n}\n\nexport const useUIStore = create<UIState>()(\n  persist(\n    (set, get) => ({\n      sidebarOpen: true,\n      theme: 'light',\n      notifications: [],\n      \n      toggleSidebar: () => \n        set((state) => ({ sidebarOpen: !state.sidebarOpen })),\n      \n      setTheme: (theme) => set({ theme }),\n      \n      addNotification: (notification) => {\n        const id = Math.random().toString(36).substr(2, 9);\n        const timestamp = Date.now();\n        \n        set((state) => ({\n          notifications: [...state.notifications, { ...notification, id, timestamp }],\n        }));\n        \n        // Auto-remove after 5 seconds\n        setTimeout(() => {\n          set((state) => ({\n            notifications: state.notifications.filter((n) => n.id !== id),\n          }));\n        }, 5000);\n      },\n      \n      removeNotification: (id) =>\n        set((state) => ({\n          notifications: state.notifications.filter((n) => n.id !== id),\n        })),\n    }),\n    {\n      name: 'ui-storage',\n      partialize: (state) => ({ \n        sidebarOpen: state.sidebarOpen, \n        theme: state.theme \n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAaO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;oBAC/B,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,iBAAiB;wBAAM,WAAW;oBAAM;oBAC/D,OAAO;gBACT;gBAEA,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT;QACF;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;YAAM;QAC3C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB;YAAK;QACpC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AA8BG,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACnE,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,aAAa;QACb,SAAS,CAAC;QAEV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAE5D,eAAe,CAAC,IAAI,UAClB,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,IAC5B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;oBAEvC,gBAAgB,MAAM,cAAc,EAAE,OAAO,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;oBAAC,IACtC,MAAM,cAAc;gBAC1B,CAAC;QAEH,eAAe,CAAC,KACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAChD,gBAAgB,MAAM,cAAc,EAAE,OAAO,KAAK,OAAO,MAAM,cAAc;gBAC/E,CAAC;QAEH,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAE9D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QAEvC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,kBAAkB;YAChB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YAE3C,OAAO,SAAS,MAAM,CAAC,CAAC;gBACtB,sBAAsB;gBACtB,IAAI,aAAa;oBACf,MAAM,QAAQ,YAAY,WAAW;oBACrC,MAAM,gBACJ,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UACxC,QAAQ,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACzC,QAAQ,YAAY,CAAC,QAAQ,CAAC,UAC9B,QAAQ,KAAK,EAAE,cAAc,SAAS;oBAExC,IAAI,CAAC,eAAe,OAAO;gBAC7B;gBAEA,gBAAgB;gBAChB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACvD,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,QAAQ,UAAU,EAAE;oBACnE,OAAO;gBACT;gBAEA,mBAAmB;gBACnB,IAAI,QAAQ,QAAQ,EAAE;oBACpB,MAAM,MAAM,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,QAAQ,WAAW,EAAE,WAAW;oBAChF,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,EAAE;wBAC5D,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT;QACF;IACF,CAAC;AAUM,MAAM,wBAAwB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,MAAQ,CAAC;QACxE,eAAe,EAAE;QACjB,WAAW;QAEX,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAUM,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QAChE,OAAO;QACP,WAAW;QAEX,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAmBM,MAAM,aAAa,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,aAAa;QACb,OAAO;QACP,eAAe,EAAE;QAEjB,eAAe,IACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QAErD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,iBAAiB,CAAC;YAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChD,MAAM,YAAY,KAAK,GAAG;YAE1B,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;2BAAI,MAAM,aAAa;wBAAE;4BAAE,GAAG,YAAY;4BAAE;4BAAI;wBAAU;qBAAE;gBAC7E,CAAC;YAED,8BAA8B;YAC9B,WAAW;gBACT,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC5D,CAAC;YACH,GAAG;QACL;QAEA,oBAAoB,CAAC,KACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC5D,CAAC;IACL,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 817, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  UserGroupIcon, \n  UserPlusIcon, \n  ChartBarIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '../ui/Logo';\nimport { useUIStore, useAuthStore } from '@/store';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {\n  const pathname = usePathname();\n  const { sidebarOpen, toggleSidebar } = useUIStore();\n  const { user, logout } = useAuthStore();\n\n  const navigationItems = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: HomeIcon,\n      description: 'Overview and statistics'\n    },\n    {\n      name: 'Patients',\n      href: '/patients',\n      icon: UserGroupIcon,\n      description: 'View all patients'\n    },\n    {\n      name: 'Add Patient',\n      href: '/patients/new',\n      icon: UserPlusIcon,\n      description: 'Register new patient'\n    },\n    {\n      name: 'Analytics',\n      href: '/analytics',\n      icon: ChartBarIcon,\n      description: 'Medical reports & insights'\n    },\n    {\n      name: 'Settings',\n      href: '/settings',\n      icon: Cog6ToothIcon,\n      description: 'Application settings'\n    }\n  ];\n\n  const sidebarVariants = {\n    open: {\n      x: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      }\n    },\n    closed: {\n      x: \"-100%\",\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      }\n    }\n  };\n\n  const itemVariants = {\n    open: {\n      opacity: 1,\n      x: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      }\n    },\n    closed: {\n      opacity: 0,\n      x: -20\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    // Redirect to login page\n    window.location.href = '/login';\n  };\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={toggleSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Premium Sidebar */}\n      <motion.aside\n        className={`fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-white via-blue-50/30 to-teal-50/30 backdrop-blur-xl border-r border-white/20 z-50 lg:relative lg:translate-x-0 ${className}`}\n        variants={sidebarVariants}\n        animate={sidebarOpen ? \"open\" : \"closed\"}\n        initial=\"closed\"\n        style={{\n          boxShadow: '0 0 50px rgba(0, 102, 204, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)'\n        }}\n      >\n        {/* Sidebar Background Effects */}\n        <div className=\"absolute inset-0 overflow-hidden\">\n          {/* Animated Background Pattern */}\n          <div className=\"absolute inset-0 opacity-5\">\n            <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\">\n              <defs>\n                <pattern id=\"sidebar-pattern\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n                  <circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"url(#premiumGradient)\" />\n                </pattern>\n              </defs>\n              <rect width=\"100\" height=\"100\" fill=\"url(#sidebar-pattern)\" />\n            </svg>\n          </div>\n\n          {/* Floating Elements */}\n          {[...Array(3)].map((_, i) => (\n            <motion.div\n              key={i}\n              className=\"absolute w-32 h-32 rounded-full opacity-5\"\n              style={{\n                background: 'linear-gradient(135deg, #0066cc, #00a693)',\n                left: `${-20 + i * 30}%`,\n                top: `${20 + i * 25}%`,\n              }}\n              animate={{\n                y: [-10, 10, -10],\n                x: [-5, 5, -5],\n                scale: [1, 1.1, 1],\n              }}\n              transition={{\n                duration: 8 + i * 2,\n                repeat: Infinity,\n                ease: \"easeInOut\",\n              }}\n            />\n          ))}\n        </div>\n\n        <div className=\"flex flex-col h-full relative z-10\">\n          {/* Premium Header */}\n          <motion.div\n            className=\"flex items-center justify-between p-6 border-b border-white/20 bg-white/40 backdrop-blur-sm\"\n            initial={{ opacity: 0, y: -20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ duration: 0.6 }}\n          >\n            <Logo size=\"lg\" showText variant=\"premium\" />\n            <motion.button\n              onClick={toggleSidebar}\n              className=\"p-2 rounded-xl hover:bg-white/60 lg:hidden transition-all duration-200 backdrop-blur-sm\"\n              whileHover={{ scale: 1.1, rotate: 90 }}\n              whileTap={{ scale: 0.9 }}\n            >\n              <XMarkIcon className=\"w-6 h-6 text-gray-600\" />\n            </motion.button>\n          </motion.div>\n\n          {/* Premium User Info */}\n          {user && (\n            <motion.div\n              className=\"p-6 border-b border-white/20 bg-white/30 backdrop-blur-sm mx-4 my-4 rounded-2xl\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02 }}\n              transition={{ duration: 0.2 }}\n            >\n              <div className=\"flex items-center space-x-4\">\n                <motion.div\n                  className=\"relative\"\n                  whileHover={{ scale: 1.1 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <div className=\"w-14 h-14 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                    {user?.firstName?.[0]?.toUpperCase() || 'D'}{user?.lastName?.[0]?.toUpperCase() || 'R'}\n                  </div>\n                  {/* Online Status Indicator */}\n                  <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n                    <div className=\"w-full h-full bg-green-400 rounded-full animate-ping\"></div>\n                  </div>\n                </motion.div>\n                <div className=\"flex-1\">\n                  <motion.p\n                    className=\"font-bold text-gray-800 text-lg\"\n                    initial={{ opacity: 0, x: -10 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.1 }}\n                  >\n                    {user?.firstName || 'Doctor'} {user?.lastName || ''}\n                  </motion.p>\n                  <motion.div\n                    className=\"flex items-center gap-2 mt-1\"\n                    initial={{ opacity: 0, x: -10 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: 0.2 }}\n                  >\n                    <span className=\"px-2 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-semibold rounded-full capitalize\">\n                      {user?.role || 'doctor'}\n                    </span>\n                    <div className=\"flex items-center gap-1\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                      <span className=\"text-xs text-gray-600 font-medium\">Online</span>\n                    </div>\n                  </motion.div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Premium Navigation */}\n          <nav className=\"flex-1 p-6\">\n            <motion.div\n              className=\"mb-4\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.3 }}\n            >\n              <h3 className=\"text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 px-4\">\n                Navigation\n              </h3>\n            </motion.div>\n\n            <motion.ul className=\"space-y-3\">\n              {navigationItems.map((item, index) => {\n                const isActive = pathname === item.href;\n                const Icon = item.icon;\n\n                return (\n                  <motion.li\n                    key={item.name}\n                    variants={itemVariants}\n                    transition={{ delay: index * 0.1 }}\n                    whileHover={{ x: 4 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={`group relative flex items-center px-4 py-4 rounded-2xl transition-all duration-300 overflow-hidden ${\n                        isActive\n                          ? 'bg-gradient-to-r from-blue-500/20 via-blue-600/20 to-teal-500/20 text-blue-700 shadow-lg backdrop-blur-sm border border-blue-200/50'\n                          : 'text-gray-700 hover:bg-white/60 hover:text-blue-600 hover:shadow-md backdrop-blur-sm'\n                      }`}\n                    >\n                      {/* Active Background Effect */}\n                      {isActive && (\n                        <motion.div\n                          className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-teal-500/10\"\n                          layoutId=\"activeBackground\"\n                          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                        />\n                      )}\n\n                      {/* Icon Container */}\n                      <motion.div\n                        className={`relative z-10 p-2 rounded-xl mr-4 transition-all duration-300 ${\n                          isActive\n                            ? 'bg-gradient-to-r from-blue-500 to-teal-500 text-white shadow-lg'\n                            : 'bg-gray-100 text-gray-400 group-hover:bg-blue-100 group-hover:text-blue-600'\n                        }`}\n                        whileHover={{ scale: 1.1, rotate: 5 }}\n                        transition={{ duration: 0.2 }}\n                      >\n                        <Icon className=\"w-5 h-5\" />\n                      </motion.div>\n\n                      {/* Content */}\n                      <div className=\"flex-1 relative z-10\">\n                        <motion.p\n                          className={`font-semibold text-sm ${isActive ? 'text-blue-700' : 'group-hover:text-blue-600'}`}\n                          initial={{ opacity: 0, y: 5 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: index * 0.05 }}\n                        >\n                          {item.name}\n                        </motion.p>\n                        <motion.p\n                          className=\"text-xs text-gray-500 mt-0.5\"\n                          initial={{ opacity: 0, y: 5 }}\n                          animate={{ opacity: 1, y: 0 }}\n                          transition={{ delay: index * 0.05 + 0.1 }}\n                        >\n                          {item.description}\n                        </motion.p>\n                      </div>\n\n                      {/* Active Indicator */}\n                      {isActive && (\n                        <motion.div\n                          className=\"relative z-10 w-3 h-3 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full shadow-lg\"\n                          layoutId=\"activeIndicator\"\n                          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                        >\n                          <div className=\"absolute inset-0 bg-gradient-to-r from-blue-400 to-teal-400 rounded-full animate-ping opacity-75\"></div>\n                        </motion.div>\n                      )}\n\n                      {/* Hover Arrow */}\n                      <motion.div\n                        className={`relative z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${\n                          isActive ? 'hidden' : ''\n                        }`}\n                      >\n                        <svg className=\"w-4 h-4 text-blue-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n                        </svg>\n                      </motion.div>\n                    </Link>\n                  </motion.li>\n                );\n              })}\n            </motion.ul>\n          </nav>\n\n          {/* Premium Footer */}\n          <motion.div\n            className=\"p-6 border-t border-white/20 bg-white/30 backdrop-blur-sm mx-4 mb-4 rounded-2xl\"\n            variants={itemVariants}\n          >\n            <motion.button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-4 py-4 text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-600 rounded-2xl transition-all duration-300 group relative overflow-hidden\"\n              whileHover={{ scale: 1.02 }}\n              whileTap={{ scale: 0.98 }}\n            >\n              {/* Hover Background Effect */}\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                initial={false}\n              />\n\n              <motion.div\n                className=\"relative z-10 p-2 rounded-xl mr-4 bg-gray-100 group-hover:bg-red-100 transition-all duration-300\"\n                whileHover={{ scale: 1.1, rotate: -5 }}\n              >\n                <ArrowRightOnRectangleIcon className=\"w-5 h-5 text-gray-400 group-hover:text-red-600 transition-colors duration-300\" />\n              </motion.div>\n\n              <span className=\"font-semibold relative z-10\">Sign Out</span>\n\n              {/* Logout Arrow */}\n              <motion.div\n                className=\"ml-auto relative z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n                initial={{ x: -10 }}\n                whileHover={{ x: 0 }}\n              >\n                <svg className=\"w-4 h-4 text-red-500\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\" />\n                </svg>\n              </motion.div>\n            </motion.button>\n\n            {/* App Info */}\n            <motion.div\n              className=\"mt-6 text-center space-y-2\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.5 }}\n            >\n              <div className=\"flex items-center justify-center gap-2\">\n                <div className=\"w-6 h-6 bg-gradient-to-r from-blue-500 to-teal-500 rounded-lg flex items-center justify-center\">\n                  <svg className=\"w-3 h-3 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                    <path fillRule=\"evenodd\" d=\"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                  </svg>\n                </div>\n                <p className=\"text-xs font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent\">\n                  Dr Satya's Medical Suite\n                </p>\n              </div>\n\n              <div className=\"flex items-center justify-center gap-4 text-xs text-gray-500\">\n                <span className=\"flex items-center gap-1\">\n                  <div className=\"w-2 h-2 bg-green-500 rounded-full\"></div>\n                  v1.0.0\n                </span>\n                <span>•</span>\n                <span>Premium Edition</span>\n              </div>\n\n              <motion.div\n                className=\"pt-2 border-t border-white/20\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.8 }}\n              >\n                <p className=\"text-xs text-gray-400 font-medium\">\n                  Excellence in Digital Healthcare\n                </p>\n              </motion.div>\n            </motion.div>\n          </motion.div>\n        </div>\n      </motion.aside>\n\n      {/* Premium Mobile Menu Button */}\n      <motion.button\n        className=\"fixed top-6 left-6 z-50 p-4 bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl lg:hidden border border-white/20\"\n        onClick={toggleSidebar}\n        whileHover={{\n          scale: 1.1,\n          rotate: 5,\n          boxShadow: '0 20px 40px rgba(0, 102, 204, 0.3)'\n        }}\n        whileTap={{ scale: 0.9 }}\n        style={{\n          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 249, 255, 0.9) 100%)'\n        }}\n      >\n        <motion.div\n          animate={{ rotate: sidebarOpen ? 90 : 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          <Bars3Icon className=\"w-6 h-6 text-blue-600\" />\n        </motion.div>\n\n        {/* Pulse Effect */}\n        <motion.div\n          className=\"absolute inset-0 rounded-2xl bg-blue-500/20\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.5, 0, 0.5],\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </motion.button>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;AAjBA;;;;;;;;AAuBA,MAAM,UAAkC,CAAC,EAAE,YAAY,EAAE,EAAE;IACzD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAChD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAEpC,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,MAAM,+MAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,yNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uNAAA,CAAA,eAAY;YAClB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,uNAAA,CAAA,eAAY;YAClB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,yNAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,kBAAkB;QACtB,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM;YACJ,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,SAAS;YACT,GAAG,CAAC;QACN;IACF;IAEA,MAAM,eAAe;QACnB;QACA,yBAAyB;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE;;0BAEE,8OAAC,yLAAA,CAAA,kBAAe;0BACb,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS;;;;;;;;;;;0BAMf,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,WAAW,CAAC,oKAAoK,EAAE,WAAW;gBAC7L,UAAU;gBACV,SAAS,cAAc,SAAS;gBAChC,SAAQ;gBACR,OAAO;oBACL,WAAW;gBACb;;kCAGA,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAgB,SAAQ;;sDACrC,8OAAC;sDACC,cAAA,8OAAC;gDAAQ,IAAG;gDAAkB,GAAE;gDAAI,GAAE;gDAAI,OAAM;gDAAK,QAAO;gDAAK,cAAa;0DAC5E,cAAA,8OAAC;oDAAO,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAI,MAAK;;;;;;;;;;;;;;;;sDAGvC,8OAAC;4CAAK,OAAM;4CAAM,QAAO;4CAAM,MAAK;;;;;;;;;;;;;;;;;4BAKvC;mCAAI,MAAM;6BAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCACL,YAAY;wCACZ,MAAM,GAAG,CAAC,KAAK,IAAI,GAAG,CAAC,CAAC;wCACxB,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;oCACxB;oCACA,SAAS;wCACP,GAAG;4CAAC,CAAC;4CAAI;4CAAI,CAAC;yCAAG;wCACjB,GAAG;4CAAC,CAAC;4CAAG;4CAAG,CAAC;yCAAE;wCACd,OAAO;4CAAC;4CAAG;4CAAK;yCAAE;oCACpB;oCACA,YAAY;wCACV,UAAU,IAAI,IAAI;wCAClB,QAAQ;wCACR,MAAM;oCACR;mCAhBK;;;;;;;;;;;kCAqBX,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;gCAAI;;kDAE5B,8OAAC,gIAAA,CAAA,UAAI;wCAAC,MAAK;wCAAK,QAAQ;wCAAC,SAAQ;;;;;;kDACjC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;4CAAK,QAAQ;wCAAG;wCACrC,UAAU;4CAAE,OAAO;wCAAI;kDAEvB,cAAA,8OAAC,iNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;;;;;;;;;;;;4BAKxB,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,YAAY;gDAAE,UAAU;4CAAI;;8DAE5B,8OAAC;oDAAI,WAAU;;wDACZ,MAAM,WAAW,CAAC,EAAE,EAAE,iBAAiB;wDAAK,MAAM,UAAU,CAAC,EAAE,EAAE,iBAAiB;;;;;;;8DAGrF,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;sDAGnB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO;oDAAI;;wDAExB,MAAM,aAAa;wDAAS;wDAAE,MAAM,YAAY;;;;;;;8DAEnD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO;oDAAI;;sEAEzB,8OAAC;4DAAK,WAAU;sEACb,MAAM,QAAQ;;;;;;sEAEjB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;;;;;8EACf,8OAAC;oEAAK,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAShE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAEzB,cAAA,8OAAC;4CAAG,WAAU;sDAAqE;;;;;;;;;;;kDAKrF,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wCAAC,WAAU;kDAClB,gBAAgB,GAAG,CAAC,CAAC,MAAM;4CAC1B,MAAM,WAAW,aAAa,KAAK,IAAI;4CACvC,MAAM,OAAO,KAAK,IAAI;4CAEtB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDAER,UAAU;gDACV,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,YAAY;oDAAE,GAAG;gDAAE;0DAEnB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,KAAK,IAAI;oDACf,WAAW,CAAC,mGAAmG,EAC7G,WACI,wIACA,wFACJ;;wDAGD,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,UAAS;4DACT,YAAY;gEAAE,MAAM;gEAAU,WAAW;gEAAK,SAAS;4DAAG;;;;;;sEAK9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAW,CAAC,8DAA8D,EACxE,WACI,oEACA,+EACJ;4DACF,YAAY;gEAAE,OAAO;gEAAK,QAAQ;4DAAE;4DACpC,YAAY;gEAAE,UAAU;4DAAI;sEAE5B,cAAA,8OAAC;gEAAK,WAAU;;;;;;;;;;;sEAIlB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oEACP,WAAW,CAAC,sBAAsB,EAAE,WAAW,kBAAkB,6BAA6B;oEAC9F,SAAS;wEAAE,SAAS;wEAAG,GAAG;oEAAE;oEAC5B,SAAS;wEAAE,SAAS;wEAAG,GAAG;oEAAE;oEAC5B,YAAY;wEAAE,OAAO,QAAQ;oEAAK;8EAEjC,KAAK,IAAI;;;;;;8EAEZ,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oEACP,WAAU;oEACV,SAAS;wEAAE,SAAS;wEAAG,GAAG;oEAAE;oEAC5B,SAAS;wEAAE,SAAS;wEAAG,GAAG;oEAAE;oEAC5B,YAAY;wEAAE,OAAO,QAAQ,OAAO;oEAAI;8EAEvC,KAAK,WAAW;;;;;;;;;;;;wDAKpB,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAU;4DACV,UAAS;4DACT,YAAY;gEAAE,MAAM;gEAAU,WAAW;gEAAK,SAAS;4DAAG;sEAE1D,cAAA,8OAAC;gEAAI,WAAU;;;;;;;;;;;sEAKnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4DACT,WAAW,CAAC,gFAAgF,EAC1F,WAAW,WAAW,IACtB;sEAEF,cAAA,8OAAC;gEAAI,WAAU;gEAAwB,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EAC/E,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAa;oEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;+CAzEtE,KAAK,IAAI;;;;;wCA+EpB;;;;;;;;;;;;0CAKJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;;kDAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAGxB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;;;;;;0DAGX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,YAAY;oDAAE,OAAO;oDAAK,QAAQ,CAAC;gDAAE;0DAErC,cAAA,8OAAC,iPAAA,CAAA,4BAAyB;oDAAC,WAAU;;;;;;;;;;;0DAGvC,8OAAC;gDAAK,WAAU;0DAA8B;;;;;;0DAG9C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,GAAG,CAAC;gDAAG;gDAClB,YAAY;oDAAE,GAAG;gDAAE;0DAEnB,cAAA,8OAAC;oDAAI,WAAU;oDAAuB,MAAK;oDAAO,QAAO;oDAAe,SAAQ;8DAC9E,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kDAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC;4DAAI,WAAU;4DAAqB,MAAK;4DAAe,SAAQ;sEAC9D,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAkiB,UAAS;;;;;;;;;;;;;;;;kEAG1kB,8OAAC;wDAAE,WAAU;kEAA6F;;;;;;;;;;;;0DAK5G,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAK,WAAU;;0EACd,8OAAC;gEAAI,WAAU;;;;;;4DAA0C;;;;;;;kEAG3D,8OAAC;kEAAK;;;;;;kEACN,8OAAC;kEAAK;;;;;;;;;;;;0DAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,SAAS;gDAAE;gDACtB,SAAS;oDAAE,SAAS;gDAAE;gDACtB,YAAY;oDAAE,OAAO;gDAAI;0DAEzB,cAAA,8OAAC;oDAAE,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAU3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;gBACT,YAAY;oBACV,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;gBACA,UAAU;oBAAE,OAAO;gBAAI;gBACvB,OAAO;oBACL,YAAY;gBACd;;kCAEA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,cAAc,KAAK;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC,iNAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;;;;;;kCAIvB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 1751, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Sidebar from '@/components/layout/Sidebar';\nimport { useUIStore } from '@/store';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {\n  const { sidebarOpen } = useUIStore();\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Sidebar */}\n      <Sidebar />\n      \n      {/* Main Content */}\n      <motion.main\n        className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ${\n          sidebarOpen ? 'lg:ml-80' : 'lg:ml-0'\n        }`}\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        {/* Content Area */}\n        <div className=\"flex-1 overflow-auto\">\n          <div className=\"p-6 lg:p-8\">\n            {children}\n          </div>\n        </div>\n      </motion.main>\n    </div>\n  );\n};\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AALA;;;;;AAWA,MAAM,kBAAkD,CAAC,EAAE,QAAQ,EAAE;IACnE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,aAAU,AAAD;IAEjC,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,uIAAA,CAAA,UAAO;;;;;0BAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAW,CAAC,iEAAiE,EAC3E,cAAc,aAAa,WAC3B;gBACF,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAG5B,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;uCAEe", "debugId": null}}]}