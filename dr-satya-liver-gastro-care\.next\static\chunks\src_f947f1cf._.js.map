{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: {\n      gradient: 'from-blue-500 via-blue-600 to-blue-700',\n      bg: 'from-blue-50 to-blue-100',\n      text: 'text-blue-700',\n      border: 'border-blue-200'\n    },\n    green: {\n      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',\n      bg: 'from-emerald-50 to-emerald-100',\n      text: 'text-emerald-700',\n      border: 'border-emerald-200'\n    },\n    amber: {\n      gradient: 'from-amber-500 via-amber-600 to-amber-700',\n      bg: 'from-amber-50 to-amber-100',\n      text: 'text-amber-700',\n      border: 'border-amber-200'\n    },\n    red: {\n      gradient: 'from-red-500 via-red-600 to-red-700',\n      bg: 'from-red-50 to-red-100',\n      text: 'text-red-700',\n      border: 'border-red-200'\n    },\n    teal: {\n      gradient: 'from-teal-500 via-teal-600 to-teal-700',\n      bg: 'from-teal-50 to-teal-100',\n      text: 'text-teal-700',\n      border: 'border-teal-200'\n    }\n  };\n\n  const currentColor = colorClasses[color];\n\n  return (\n    <motion.div\n      className={`medical-card-glass relative overflow-hidden border ${currentColor.border} ${className}`}\n      whileHover={{\n        y: -8,\n        scale: 1.02,\n        transition: { duration: 0.3 }\n      }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className={`absolute inset-0 bg-gradient-to-br ${currentColor.bg} opacity-50`} />\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/20\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-white/10\"\n          animate={{\n            scale: [1, 1.3, 1],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <motion.p\n              className=\"text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              {title}\n            </motion.p>\n\n            <motion.p\n              className=\"text-4xl font-bold text-gray-900 mb-2\"\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            >\n              {value}\n            </motion.p>\n\n            {subtitle && (\n              <motion.p\n                className=\"text-sm text-gray-600 font-medium\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                {subtitle}\n              </motion.p>\n            )}\n\n            {trend && (\n              <motion.div\n                className=\"flex items-center mt-3\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n              >\n                <motion.span\n                  className={`inline-flex items-center gap-1 text-sm font-bold px-2 py-1 rounded-full ${\n                    trend.isPositive\n                      ? 'text-emerald-700 bg-emerald-100'\n                      : 'text-red-700 bg-red-100'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                >\n                  <motion.span\n                    animate={{ rotate: trend.isPositive ? 0 : 180 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    ↗\n                  </motion.span>\n                  {Math.abs(trend.value)}%\n                </motion.span>\n                <span className=\"text-xs text-gray-500 ml-2 font-medium\">vs last month</span>\n              </motion.div>\n            )}\n          </div>\n\n          {icon && (\n            <motion.div\n              className={`p-4 rounded-2xl bg-gradient-to-br ${currentColor.gradient} text-white shadow-lg`}\n              whileHover={{\n                scale: 1.1,\n                rotate: 5,\n                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)'\n              }}\n              initial={{ opacity: 0, scale: 0, rotate: -90 }}\n              animate={{ opacity: 1, scale: 1, rotate: 0 }}\n              transition={{ delay: 0.3, type: \"spring\", stiffness: 200 }}\n            >\n              {icon}\n            </motion.div>\n          )}\n        </div>\n      </div>\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className={`absolute inset-0 bg-gradient-to-r ${currentColor.gradient} opacity-0 rounded-2xl`}\n        whileHover={{ opacity: 0.1 }}\n        transition={{ duration: 0.3 }}\n      />\n    </motion.div>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <motion.div\n      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}\n      onClick={() => onClick?.(patient)}\n      whileHover={{\n        y: -6,\n        scale: 1.02,\n        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',\n        transition: { duration: 0.3 }\n      }}\n      whileTap={{ scale: 0.98 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50\" />\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n      />\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Premium Avatar */}\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ duration: 0.2 }}\n          >\n            {patient.profileImageUrl ? (\n              <img\n                src={patient.profileImageUrl}\n                alt={patient.fullName}\n                className=\"w-16 h-16 rounded-2xl object-cover shadow-lg\"\n              />\n            ) : (\n              <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                {getInitials(patient.fullName)}\n              </div>\n            )}\n            {/* Status Indicator */}\n            <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n              <div className=\"w-full h-full bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n          </motion.div>\n\n          {/* Patient Info */}\n          <div className=\"flex-1 min-w-0\">\n            <motion.div\n              className=\"flex items-center justify-between mb-2\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h3 className=\"font-bold text-gray-900 text-lg truncate\">\n                {patient.fullName}\n              </h3>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200\">\n                {patient.patientId}\n              </span>\n            </motion.div>\n\n            <motion.div\n              className=\"flex items-center space-x-3 text-sm\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  patient.gender === 'Male' ? 'bg-blue-500' :\n                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'\n                }`}></div>\n                <span className=\"text-gray-600 font-medium\">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n              </div>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <span className=\"text-gray-600 font-medium\">{patient.gender}</span>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <div className=\"flex items-center gap-1\">\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span className=\"text-gray-600 font-medium\">{patient.mobileNumber}</span>\n              </div>\n            </motion.div>\n\n            {patient.lastVisit && (\n              <motion.div\n                className=\"flex items-center gap-1 mt-2\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-xs text-gray-500 font-medium\">\n                  Last visit: {patient.lastVisit}\n                </p>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Arrow Indicator */}\n          <motion.div\n            className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-200\"\n            whileHover={{ x: 5 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.div>\n        </div>\n\n        {/* Bottom Border Animation */}\n        <motion.div\n          className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full\"\n          initial={{ width: 0 }}\n          whileHover={{ width: '100%' }}\n          transition={{ duration: 0.3 }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,AAAC,4BAGjB,OADA,cAAc,CAAC,QAAQ,EAAC,WAExB,OADA,aAAa,CAAC,OAAO,EAAC,UAEtB,OADA,WAAW,4CAA4C,YAAW,UAElE,OADA,UAAU,mBAAmB,IAAG,UACtB,OAAV,WAAU;IAGd,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;KApDM;AAqEC,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,KAAK;YACH,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,MAAM;IAExC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,sDAA4E,OAAvB,aAAa,MAAM,EAAC,KAAa,OAAV;QACxF,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAW,AAAC,sCAAqD,OAAhB,aAAa,EAAE,EAAC;;;;;;0BAGtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG;gCAAK;6BAAI;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAK;gCAAK;6BAAE;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;8CAGH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;8CAExD;;;;;;gCAGF,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;gCAIJ,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;;sDAEzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAW,AAAC,2EAIX,OAHC,MAAM,UAAU,GACZ,oCACA;4CAEN,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,QAAQ,MAAM,UAAU,GAAG,IAAI;oDAAI;oDAC9C,YAAY;wDAAE,UAAU;oDAAI;8DAC7B;;;;;;gDAGA,KAAK,GAAG,CAAC,MAAM,KAAK;gDAAE;;;;;;;sDAEzB,6LAAC;4CAAK,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;wBAK9D,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,AAAC,qCAA0D,OAAtB,aAAa,QAAQ,EAAC;4BACtE,YAAY;gCACV,OAAO;gCACP,QAAQ;gCACR,WAAW;4BACb;4BACA,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ,CAAC;4BAAG;4BAC7C,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ;4BAAE;4BAC3C,YAAY;gCAAE,OAAO;gCAAK,MAAM;gCAAU,WAAW;4BAAI;sCAExD;;;;;;;;;;;;;;;;;0BAOT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,qCAA0D,OAAtB,aAAa,QAAQ,EAAC;gBACtE,YAAY;oBAAE,SAAS;gBAAI;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;MA7Ka;AA8LN,MAAM,cAA0C;QAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,2FAAoG,OAAV;QACtG,SAAS,IAAM,oBAAA,8BAAA,QAAU;QACzB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,UAAU;gCAAI;;oCAE3B,QAAQ,eAAe,iBACtB,6LAAC;wCACC,KAAK,QAAQ,eAAe;wCAC5B,KAAK,QAAQ,QAAQ;wCACrB,WAAU;;;;;iGAGZ,6LAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,QAAQ;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ;;;;;;0DAEnB,6LAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS;;;;;;;;;;;;kDAItB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAGhB,OAFC,QAAQ,MAAM,KAAK,SAAS,gBAC5B,QAAQ,MAAM,KAAK,WAAW,gBAAgB;;;;;;kEAEhD,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,GAAG,GAAG,AAAC,GAAc,OAAZ,QAAQ,GAAG,EAAC,YAAU;;;;;;;;;;;;0DAGtF,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAK,WAAU;0DAA6B,QAAQ,MAAM;;;;;;0DAE3D,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,YAAY;;;;;;;;;;;;;;;;;;oCAIpE,QAAQ,SAAS,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAE,WAAU;;oDAAoC;oDAClC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0CAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG;gCAAE;0CAEnB,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAO;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;AAKtC;MAhJa;AA2JN,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAK,WAAW;;0BACf,6LAAC;gBACC,WAAW,AAAC,qCAAwE,OAApC,cAAc,mBAAmB;gBACjF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GAnDa;MAAA;uCAqDE", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/FloatingActionButton.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface FloatingActionButtonProps {\n  onClick: () => void;\n  icon: React.ReactNode;\n  label?: string;\n  className?: string;\n  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';\n  size?: 'sm' | 'md' | 'lg';\n}\n\nconst FloatingActionButton: React.FC<FloatingActionButtonProps> = ({\n  onClick,\n  icon,\n  label,\n  className = '',\n  variant = 'primary',\n  size = 'md'\n}) => {\n  const variantClasses = {\n    primary: 'from-blue-500 via-blue-600 to-teal-500',\n    secondary: 'from-gray-500 via-gray-600 to-gray-700',\n    success: 'from-emerald-500 via-emerald-600 to-green-500',\n    warning: 'from-amber-500 via-amber-600 to-orange-500',\n    danger: 'from-red-500 via-red-600 to-pink-500'\n  };\n\n  const sizeClasses = {\n    sm: 'w-12 h-12',\n    md: 'w-16 h-16',\n    lg: 'w-20 h-20'\n  };\n\n  const iconSizeClasses = {\n    sm: 'w-5 h-5',\n    md: 'w-6 h-6',\n    lg: 'w-8 h-8'\n  };\n\n  return (\n    <motion.div\n      className=\"fixed bottom-8 right-8 z-50\"\n      initial={{ scale: 0, rotate: -180 }}\n      animate={{ scale: 1, rotate: 0 }}\n      transition={{ \n        type: \"spring\", \n        stiffness: 200, \n        damping: 15,\n        delay: 0.5 \n      }}\n    >\n      <motion.button\n        className={`${sizeClasses[size]} bg-gradient-to-br ${variantClasses[variant]} text-white rounded-full shadow-2xl flex items-center justify-center relative overflow-hidden group ${className}`}\n        onClick={onClick}\n        whileHover={{ \n          scale: 1.1, \n          rotate: 5,\n          boxShadow: '0 25px 50px rgba(0, 102, 204, 0.4)'\n        }}\n        whileTap={{ scale: 0.9 }}\n        style={{\n          filter: 'drop-shadow(0 10px 20px rgba(0, 102, 204, 0.3))'\n        }}\n      >\n        {/* Background Glow Effect */}\n        <motion.div\n          className=\"absolute inset-0 bg-white/20 rounded-full\"\n          initial={{ scale: 0 }}\n          whileHover={{ scale: 1.2 }}\n          transition={{ duration: 0.3 }}\n        />\n        \n        {/* Ripple Effect */}\n        <motion.div\n          className=\"absolute inset-0 bg-white/10 rounded-full\"\n          animate={{\n            scale: [1, 1.5, 1],\n            opacity: [0.5, 0, 0.5],\n          }}\n          transition={{\n            duration: 2,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        \n        {/* Icon */}\n        <motion.div\n          className={`relative z-10 ${iconSizeClasses[size]}`}\n          whileHover={{ rotate: 15 }}\n          transition={{ duration: 0.2 }}\n        >\n          {icon}\n        </motion.div>\n        \n        {/* Shine Effect */}\n        <motion.div\n          className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent rounded-full\"\n          initial={{ x: '-100%' }}\n          whileHover={{ x: '100%' }}\n          transition={{ duration: 0.6 }}\n        />\n      </motion.button>\n      \n      {/* Tooltip */}\n      {label && (\n        <motion.div\n          className=\"absolute right-full top-1/2 transform -translate-y-1/2 mr-4 opacity-0 group-hover:opacity-100 transition-opacity duration-200\"\n          initial={{ x: 20, opacity: 0 }}\n          whileHover={{ x: 0, opacity: 1 }}\n        >\n          <div className=\"bg-gray-900 text-white text-sm font-medium px-3 py-2 rounded-lg shadow-lg whitespace-nowrap\">\n            {label}\n            <div className=\"absolute left-full top-1/2 transform -translate-y-1/2 w-0 h-0 border-l-4 border-l-gray-900 border-t-4 border-t-transparent border-b-4 border-b-transparent\"></div>\n          </div>\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default FloatingActionButton;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAcA,MAAM,uBAA4D;QAAC,EACjE,OAAO,EACP,IAAI,EACJ,KAAK,EACL,YAAY,EAAE,EACd,UAAU,SAAS,EACnB,OAAO,IAAI,EACZ;IACC,MAAM,iBAAiB;QACrB,SAAS;QACT,WAAW;QACX,SAAS;QACT,SAAS;QACT,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAI;QAClC,SAAS;YAAE,OAAO;YAAG,QAAQ;QAAE;QAC/B,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;YACT,OAAO;QACT;;0BAEA,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAW,AAAC,GAAyC,OAAvC,WAAW,CAAC,KAAK,EAAC,uBAAmJ,OAA9H,cAAc,CAAC,QAAQ,EAAC,wGAAgH,OAAV;gBACnL,SAAS;gBACT,YAAY;oBACV,OAAO;oBACP,QAAQ;oBACR,WAAW;gBACb;gBACA,UAAU;oBAAE,OAAO;gBAAI;gBACvB,OAAO;oBACL,QAAQ;gBACV;;kCAGA,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAI;wBACzB,YAAY;4BAAE,UAAU;wBAAI;;;;;;kCAI9B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAG;6BAAI;wBACxB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAIF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAW,AAAC,iBAAsC,OAAtB,eAAe,CAAC,KAAK;wBACjD,YAAY;4BAAE,QAAQ;wBAAG;wBACzB,YAAY;4BAAE,UAAU;wBAAI;kCAE3B;;;;;;kCAIH,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,GAAG;wBAAQ;wBACtB,YAAY;4BAAE,GAAG;wBAAO;wBACxB,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;YAK/B,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,GAAG;oBAAI,SAAS;gBAAE;gBAC7B,YAAY;oBAAE,GAAG;oBAAG,SAAS;gBAAE;0BAE/B,cAAA,6LAAC;oBAAI,WAAU;;wBACZ;sCACD,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM3B;KA5GM;uCA8GS", "debugId": null}}, {"offset": {"line": 1049, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/patients/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  UserPlusIcon,\n  AdjustmentsHorizontalIcon\n} from '@heroicons/react/24/outline';\nimport { PatientCard } from '@/components/ui/Card';\nimport { ButtonLoading } from '@/components/ui/Loading';\nimport FloatingActionButton from '@/components/ui/FloatingActionButton';\nimport { toast } from 'react-hot-toast';\n\nconst PatientsPage: React.FC = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [patients, setPatients] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    limit: 50,\n    offset: 0,\n    hasMore: false\n  });\n\n  // Fetch patients from API\n  const fetchPatients = async (search = '', offset = 0) => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        search,\n        limit: pagination.limit.toString(),\n        offset: offset.toString()\n      });\n\n      const response = await fetch(`/api/patients?${params}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setPatients(data.data);\n        setPagination(data.pagination);\n      } else {\n        toast.error('Failed to fetch patients');\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n      toast.error('Failed to fetch patients');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  useEffect(() => {\n    const debounceTimer = setTimeout(() => {\n      fetchPatients(searchQuery, 0);\n    }, 300);\n\n    return () => clearTimeout(debounceTimer);\n  }, [searchQuery]);\n\n  const handlePatientClick = (patient: any) => {\n    window.location.href = `/patients/${patient.id}`;\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <motion.div\n      className=\"space-y-6\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Header */}\n      <motion.div \n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n        variants={itemVariants}\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Patients</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Manage and view all patient records\n          </p>\n        </div>\n        <motion.button\n          className=\"btn-primary\"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          onClick={() => window.location.href = '/patients/new'}\n        >\n          <UserPlusIcon className=\"w-5 h-5\" />\n          Add New Patient\n        </motion.button>\n      </motion.div>\n\n      {/* Search and Filters */}\n      <motion.div \n        className=\"medical-card p-6\"\n        variants={itemVariants}\n      >\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          {/* Search Bar */}\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              className=\"form-input pl-10\"\n              placeholder=\"Search by name, patient ID, or phone number...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n            />\n          </div>\n\n          {/* Filter Button */}\n          <motion.button\n            className=\"btn-secondary\"\n            onClick={() => setShowFilters(!showFilters)}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <FunnelIcon className=\"w-5 h-5\" />\n            Filters\n          </motion.button>\n        </div>\n\n        {/* Filter Panel */}\n        <motion.div\n          initial={false}\n          animate={{ \n            height: showFilters ? 'auto' : 0,\n            opacity: showFilters ? 1 : 0\n          }}\n          transition={{ duration: 0.3 }}\n          style={{ overflow: 'hidden' }}\n        >\n          <div className=\"mt-6 pt-6 border-t border-gray-200\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n              <div>\n                <label className=\"form-label\">Gender</label>\n                <select className=\"form-input\">\n                  <option value=\"\">All Genders</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"form-label\">Age Range</label>\n                <select className=\"form-input\">\n                  <option value=\"\">All Ages</option>\n                  <option value=\"0-18\">0-18 years</option>\n                  <option value=\"19-35\">19-35 years</option>\n                  <option value=\"36-50\">36-50 years</option>\n                  <option value=\"51-65\">51-65 years</option>\n                  <option value=\"65+\">65+ years</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"form-label\">Blood Group</label>\n                <select className=\"form-input\">\n                  <option value=\"\">All Blood Groups</option>\n                  <option value=\"A+\">A+</option>\n                  <option value=\"A-\">A-</option>\n                  <option value=\"B+\">B+</option>\n                  <option value=\"B-\">B-</option>\n                  <option value=\"AB+\">AB+</option>\n                  <option value=\"AB-\">AB-</option>\n                  <option value=\"O+\">O+</option>\n                  <option value=\"O-\">O-</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n\n      {/* Results Summary */}\n      <motion.div variants={itemVariants}>\n        <p className=\"text-gray-600\">\n          {loading ? 'Loading patients...' : `Showing ${patients.length} of ${pagination.total} patients`}\n        </p>\n      </motion.div>\n\n      {/* Patient List */}\n      {loading ? (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {Array.from({ length: 6 }).map((_, index) => (\n            <div key={index} className=\"medical-card p-6 animate-pulse\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <motion.div\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"\n          variants={containerVariants}\n        >\n          {patients.map((patient, index) => (\n            <motion.div\n              key={patient.id}\n              variants={itemVariants}\n              transition={{ delay: index * 0.05 }}\n            >\n              <PatientCard\n                patient={patient}\n                onClick={handlePatientClick}\n              />\n            </motion.div>\n          ))}\n        </motion.div>\n      )}\n\n      {/* Empty State */}\n      {!loading && patients.length === 0 && (\n        <motion.div\n          className=\"text-center py-12\"\n          variants={itemVariants}\n        >\n          <div className=\"w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\">\n            <MagnifyingGlassIcon className=\"w-12 h-12 text-gray-400\" />\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n            No patients found\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            Try adjusting your search criteria or add a new patient.\n          </p>\n          <motion.button\n            className=\"btn-primary\"\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={() => window.location.href = '/patients/new'}\n          >\n            <UserPlusIcon className=\"w-5 h-5\" />\n            Add New Patient\n          </motion.button>\n        </motion.div>\n      )}\n\n      {/* Floating Action Button */}\n      <FloatingActionButton\n        onClick={() => window.location.href = '/patients/new'}\n        icon={<UserPlusIcon className=\"w-6 h-6\" />}\n        label=\"Add New Patient\"\n        variant=\"primary\"\n        size=\"lg\"\n      />\n    </motion.div>\n  );\n};\n\nexport default PatientsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAMA;AAEA;AACA;;;AAbA;;;;;;;AAeA,MAAM,eAAyB;;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB;YAAO,0EAAS,IAAI,0EAAS;QACjD,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC;gBACA,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,QAAQ,OAAO,QAAQ;YACzB;YAEA,MAAM,WAAW,MAAM,MAAM,AAAC,iBAAuB,OAAP;YAC9C,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;gBACrB,cAAc,KAAK,UAAU;YAC/B,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR;QACF;iCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;kCAAE;YACR,MAAM,gBAAgB;wDAAW;oBAC/B,cAAc,aAAa;gBAC7B;uDAAG;YAEH;0CAAO,IAAM,aAAa;;QAC5B;iCAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAC;QAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,AAAC,aAAuB,OAAX,QAAQ,EAAE;IAChD;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;;0CACC,6LAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6LAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0CAEtC,6LAAC,0NAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMxC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,6LAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAKlD,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,IAAM,eAAe,CAAC;gCAC/B,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC,sNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;wBACT,SAAS;4BACP,QAAQ,cAAc,SAAS;4BAC/B,SAAS,cAAc,IAAI;wBAC7B;wBACA,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,OAAO;4BAAE,UAAU;wBAAS;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAG1B,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;kDAGxB,6LAAC;;0DACC,6LAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,6LAAC;wDAAO,OAAM;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,6LAAC;oBAAE,WAAU;8BACV,UAAU,wBAAwB,AAAC,WAAgC,OAAtB,SAAS,MAAM,EAAC,QAAuB,OAAjB,WAAW,KAAK,EAAC;;;;;;;;;;;YAKxF,wBACC,6LAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,6LAAC;wBAAgB,WAAU;kCACzB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;uBALX;;;;;;;;;yEAYd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,YAAY;4BAAE,OAAO,QAAQ;wBAAK;kCAElC,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BACV,SAAS;4BACT,SAAS;;;;;;uBANN,QAAQ,EAAE;;;;;;;;;;YActB,CAAC,WAAW,SAAS,MAAM,KAAK,mBAC/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC,wOAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;;;;;;kCAEjC,6LAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,6LAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0CAEtC,6LAAC,0NAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAO1C,6LAAC,mJAAA,CAAA,UAAoB;gBACnB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;gBACtC,oBAAM,6LAAC,0NAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;gBAC9B,OAAM;gBACN,SAAQ;gBACR,MAAK;;;;;;;;;;;;AAIb;GArQM;KAAA;uCAuQS", "debugId": null}}]}