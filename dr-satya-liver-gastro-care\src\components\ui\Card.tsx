'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  padding?: 'sm' | 'md' | 'lg';
  shadow?: 'sm' | 'md' | 'lg';
  onClick?: () => void;
  gradient?: boolean;
}

const Card: React.FC<CardProps> = ({
  children,
  className = '',
  hover = true,
  padding = 'md',
  shadow = 'md',
  onClick,
  gradient = false
}) => {
  const paddingClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  const shadowClasses = {
    sm: 'shadow-sm',
    md: 'shadow-md',
    lg: 'shadow-lg'
  };

  const baseClasses = `
    medical-card 
    ${paddingClasses[padding]} 
    ${shadowClasses[shadow]}
    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}
    ${onClick ? 'cursor-pointer' : ''}
    ${className}
  `;

  const cardVariants = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    hover: hover ? { 
      y: -4, 
      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' 
    } : {}
  };

  return (
    <motion.div
      className={baseClasses}
      variants={cardVariants}
      initial="initial"
      animate="animate"
      whileHover="hover"
      transition={{ duration: 0.2, ease: "easeOut" }}
      onClick={onClick}
    >
      {children}
    </motion.div>
  );
};

// Specialized Card Components

interface StatCardProps {
  title: string;
  value: string | number;
  subtitle?: string;
  icon?: React.ReactNode;
  trend?: {
    value: number;
    isPositive: boolean;
  };
  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';
  className?: string;
}

export const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  trend,
  color = 'blue',
  className = ''
}) => {
  const colorClasses = {
    blue: {
      gradient: 'from-blue-500 via-blue-600 to-blue-700',
      bg: 'from-blue-50 to-blue-100',
      text: 'text-blue-700',
      border: 'border-blue-200'
    },
    green: {
      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',
      bg: 'from-emerald-50 to-emerald-100',
      text: 'text-emerald-700',
      border: 'border-emerald-200'
    },
    amber: {
      gradient: 'from-amber-500 via-amber-600 to-amber-700',
      bg: 'from-amber-50 to-amber-100',
      text: 'text-amber-700',
      border: 'border-amber-200'
    },
    red: {
      gradient: 'from-red-500 via-red-600 to-red-700',
      bg: 'from-red-50 to-red-100',
      text: 'text-red-700',
      border: 'border-red-200'
    },
    teal: {
      gradient: 'from-teal-500 via-teal-600 to-teal-700',
      bg: 'from-teal-50 to-teal-100',
      text: 'text-teal-700',
      border: 'border-teal-200'
    }
  };

  const currentColor = colorClasses[color];

  return (
    <motion.div
      className={`medical-card-glass relative overflow-hidden border ${currentColor.border} ${className}`}
      whileHover={{
        y: -8,
        scale: 1.02,
        transition: { duration: 0.3 }
      }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background Pattern */}
      <div className={`absolute inset-0 bg-gradient-to-br ${currentColor.bg} opacity-50`} />

      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          className="absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/20"
          animate={{
            scale: [1, 1.2, 1],
            rotate: [0, 180, 360],
          }}
          transition={{
            duration: 8,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
        <motion.div
          className="absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-white/10"
          animate={{
            scale: [1, 1.3, 1],
            rotate: [360, 180, 0],
          }}
          transition={{
            duration: 6,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </div>

      <div className="relative z-10 p-6">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <motion.p
              className="text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              {title}
            </motion.p>

            <motion.p
              className="text-4xl font-bold text-gray-900 mb-2"
              initial={{ opacity: 0, scale: 0.5 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
            >
              {value}
            </motion.p>

            {subtitle && (
              <motion.p
                className="text-sm text-gray-600 font-medium"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                {subtitle}
              </motion.p>
            )}

            {trend && (
              <motion.div
                className="flex items-center mt-3"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
              >
                <motion.span
                  className={`inline-flex items-center gap-1 text-sm font-bold px-2 py-1 rounded-full ${
                    trend.isPositive
                      ? 'text-emerald-700 bg-emerald-100'
                      : 'text-red-700 bg-red-100'
                  }`}
                  whileHover={{ scale: 1.05 }}
                >
                  <motion.span
                    animate={{ rotate: trend.isPositive ? 0 : 180 }}
                    transition={{ duration: 0.3 }}
                  >
                    ↗
                  </motion.span>
                  {Math.abs(trend.value)}%
                </motion.span>
                <span className="text-xs text-gray-500 ml-2 font-medium">vs last month</span>
              </motion.div>
            )}
          </div>

          {icon && (
            <motion.div
              className={`p-4 rounded-2xl bg-gradient-to-br ${currentColor.gradient} text-white shadow-lg`}
              whileHover={{
                scale: 1.1,
                rotate: 5,
                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)'
              }}
              initial={{ opacity: 0, scale: 0, rotate: -90 }}
              animate={{ opacity: 1, scale: 1, rotate: 0 }}
              transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
            >
              {icon}
            </motion.div>
          )}
        </div>
      </div>

      {/* Hover Glow Effect */}
      <motion.div
        className={`absolute inset-0 bg-gradient-to-r ${currentColor.gradient} opacity-0 rounded-2xl`}
        whileHover={{ opacity: 0.1 }}
        transition={{ duration: 0.3 }}
      />
    </motion.div>
  );
};

interface PatientCardProps {
  patient: {
    id: string;
    fullName: string;
    patientId: string;
    age?: number;
    gender: string;
    mobileNumber: string;
    profileImageUrl?: string;
    lastVisit?: string;
  };
  onClick?: (patient: any) => void;
  className?: string;
}

export const PatientCard: React.FC<PatientCardProps> = ({
  patient,
  onClick,
  className = ''
}) => {
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map(word => word[0])
      .join('')
      .toUpperCase()
      .slice(0, 2);
  };

  return (
    <motion.div
      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}
      onClick={() => onClick?.(patient)}
      whileHover={{
        y: -6,
        scale: 1.02,
        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',
        transition: { duration: 0.3 }
      }}
      whileTap={{ scale: 0.98 }}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50" />

      {/* Hover Glow Effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      />

      <div className="relative z-10 p-6">
        <div className="flex items-center space-x-4">
          {/* Premium Avatar */}
          <motion.div
            className="relative"
            whileHover={{ scale: 1.1, rotate: 5 }}
            transition={{ duration: 0.2 }}
          >
            {patient.profileImageUrl ? (
              <img
                src={patient.profileImageUrl}
                alt={patient.fullName}
                className="w-16 h-16 rounded-2xl object-cover shadow-lg"
              />
            ) : (
              <div className="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                {getInitials(patient.fullName)}
              </div>
            )}
            {/* Status Indicator */}
            <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg">
              <div className="w-full h-full bg-green-400 rounded-full animate-pulse"></div>
            </div>
          </motion.div>

          {/* Patient Info */}
          <div className="flex-1 min-w-0">
            <motion.div
              className="flex items-center justify-between mb-2"
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <h3 className="font-bold text-gray-900 text-lg truncate">
                {patient.fullName}
              </h3>
              <span className="px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200">
                {patient.patientId}
              </span>
            </motion.div>

            <motion.div
              className="flex items-center space-x-3 text-sm"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              <div className="flex items-center gap-1">
                <div className={`w-2 h-2 rounded-full ${
                  patient.gender === 'Male' ? 'bg-blue-500' :
                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'
                }`}></div>
                <span className="text-gray-600 font-medium">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>
              </div>

              <span className="text-gray-400">•</span>

              <span className="text-gray-600 font-medium">{patient.gender}</span>

              <span className="text-gray-400">•</span>

              <div className="flex items-center gap-1">
                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                <span className="text-gray-600 font-medium">{patient.mobileNumber}</span>
              </div>
            </motion.div>

            {patient.lastVisit && (
              <motion.div
                className="flex items-center gap-1 mt-2"
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
              >
                <svg className="w-3 h-3 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
                <p className="text-xs text-gray-500 font-medium">
                  Last visit: {patient.lastVisit}
                </p>
              </motion.div>
            )}
          </div>

          {/* Arrow Indicator */}
          <motion.div
            className="text-gray-400 group-hover:text-blue-500 transition-colors duration-200"
            whileHover={{ x: 5 }}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </motion.div>
        </div>

        {/* Bottom Border Animation */}
        <motion.div
          className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full"
          initial={{ width: 0 }}
          whileHover={{ width: '100%' }}
          transition={{ duration: 0.3 }}
        />
      </div>
    </motion.div>
  );
};

interface InfoCardProps {
  title: string;
  children: React.ReactNode;
  icon?: React.ReactNode;
  className?: string;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

export const InfoCard: React.FC<InfoCardProps> = ({
  title,
  children,
  icon,
  className = '',
  collapsible = false,
  defaultExpanded = true
}) => {
  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);

  return (
    <Card className={className}>
      <div 
        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}
        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}
      >
        <div className="flex items-center space-x-3">
          {icon && (
            <div className="p-2 bg-blue-100 rounded-lg text-blue-600">
              {icon}
            </div>
          )}
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
        </div>
        {collapsible && (
          <motion.div
            animate={{ rotate: isExpanded ? 180 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </motion.div>
        )}
      </div>
      
      <motion.div
        initial={false}
        animate={{ 
          height: isExpanded ? 'auto' : 0,
          opacity: isExpanded ? 1 : 0
        }}
        transition={{ duration: 0.3 }}
        style={{ overflow: 'hidden' }}
      >
        <div className="mt-4">
          {children}
        </div>
      </motion.div>
    </Card>
  );
};

export default Card;
