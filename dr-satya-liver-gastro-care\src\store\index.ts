import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, Patient, Comorbidity, DashboardStats } from '@/types';

// Authentication Store
interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: { email: string; password: string }) => Promise<boolean>;
  logout: () => void;
  setUser: (user: User) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      
      login: async (credentials) => {
        set({ isLoading: true });
        try {
          // Demo authentication - replace with actual API call
          await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay

          // Check demo credentials
          if (credentials.email === '<EMAIL>' && credentials.password === 'demo123') {
            const mockUser: User = {
              id: '1',
              email: credentials.email,
              fullName: '<PERSON>. <PERSON>',
              role: 'doctor',
              isActive: true,
              createdAt: new Date().toISOString(),
              updatedAt: new Date().toISOString(),
            };

            set({ user: mockUser, isAuthenticated: true, isLoading: false });
            return true;
          }

          set({ isLoading: false });
          return false;
        } catch (error) {
          set({ isLoading: false });
          return false;
        }
      },
      
      logout: () => {
        set({ user: null, isAuthenticated: false });
      },
      
      setUser: (user) => {
        set({ user, isAuthenticated: true });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({ 
        user: state.user, 
        isAuthenticated: state.isAuthenticated 
      }),
    }
  )
);

// Patients Store
interface PatientsState {
  patients: Patient[];
  currentPatient: Patient | null;
  isLoading: boolean;
  searchQuery: string;
  filters: {
    gender?: string;
    bloodGroup?: string;
    ageRange?: { min: number; max: number };
  };
  
  // Actions
  setPatients: (patients: Patient[]) => void;
  addPatient: (patient: Patient) => void;
  updatePatient: (id: string, updates: Partial<Patient>) => void;
  deletePatient: (id: string) => void;
  setCurrentPatient: (patient: Patient | null) => void;
  setSearchQuery: (query: string) => void;
  setFilters: (filters: any) => void;
  setLoading: (loading: boolean) => void;
  
  // Computed
  filteredPatients: () => Patient[];
}

export const usePatientsStore = create<PatientsState>((set, get) => ({
  patients: [],
  currentPatient: null,
  isLoading: false,
  searchQuery: '',
  filters: {},
  
  setPatients: (patients) => set({ patients }),
  
  addPatient: (patient) => 
    set((state) => ({ patients: [...state.patients, patient] })),
  
  updatePatient: (id, updates) =>
    set((state) => ({
      patients: state.patients.map((p) => 
        p.id === id ? { ...p, ...updates } : p
      ),
      currentPatient: state.currentPatient?.id === id 
        ? { ...state.currentPatient, ...updates } 
        : state.currentPatient,
    })),
  
  deletePatient: (id) =>
    set((state) => ({
      patients: state.patients.filter((p) => p.id !== id),
      currentPatient: state.currentPatient?.id === id ? null : state.currentPatient,
    })),
  
  setCurrentPatient: (patient) => set({ currentPatient: patient }),
  
  setSearchQuery: (searchQuery) => set({ searchQuery }),
  
  setFilters: (filters) => set({ filters }),
  
  setLoading: (isLoading) => set({ isLoading }),
  
  filteredPatients: () => {
    const { patients, searchQuery, filters } = get();
    
    return patients.filter((patient) => {
      // Search query filter
      if (searchQuery) {
        const query = searchQuery.toLowerCase();
        const matchesSearch = 
          patient.fullName.toLowerCase().includes(query) ||
          patient.patientId.toLowerCase().includes(query) ||
          patient.mobileNumber.includes(query) ||
          patient.email?.toLowerCase().includes(query);
        
        if (!matchesSearch) return false;
      }
      
      // Gender filter
      if (filters.gender && patient.gender !== filters.gender) {
        return false;
      }
      
      // Blood group filter
      if (filters.bloodGroup && patient.bloodGroup !== filters.bloodGroup) {
        return false;
      }
      
      // Age range filter
      if (filters.ageRange) {
        const age = new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear();
        if (age < filters.ageRange.min || age > filters.ageRange.max) {
          return false;
        }
      }
      
      return true;
    });
  },
}));

// Comorbidities Store
interface ComorbiditiesState {
  comorbidities: Comorbidity[];
  isLoading: boolean;
  setComorbidities: (comorbidities: Comorbidity[]) => void;
  setLoading: (loading: boolean) => void;
}

export const useComorbiditiesStore = create<ComorbiditiesState>((set) => ({
  comorbidities: [],
  isLoading: false,
  
  setComorbidities: (comorbidities) => set({ comorbidities }),
  setLoading: (isLoading) => set({ isLoading }),
}));

// Dashboard Store
interface DashboardState {
  stats: DashboardStats | null;
  isLoading: boolean;
  setStats: (stats: DashboardStats) => void;
  setLoading: (loading: boolean) => void;
}

export const useDashboardStore = create<DashboardState>((set) => ({
  stats: null,
  isLoading: false,
  
  setStats: (stats) => set({ stats }),
  setLoading: (isLoading) => set({ isLoading }),
}));

// UI Store for global UI state
interface UIState {
  sidebarOpen: boolean;
  theme: 'light' | 'dark';
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    message: string;
    timestamp: number;
  }>;
  
  toggleSidebar: () => void;
  setTheme: (theme: 'light' | 'dark') => void;
  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;
  removeNotification: (id: string) => void;
}

export const useUIStore = create<UIState>()(
  persist(
    (set, get) => ({
      sidebarOpen: true,
      theme: 'light',
      notifications: [],
      
      toggleSidebar: () => 
        set((state) => ({ sidebarOpen: !state.sidebarOpen })),
      
      setTheme: (theme) => set({ theme }),
      
      addNotification: (notification) => {
        const id = Math.random().toString(36).substr(2, 9);
        const timestamp = Date.now();
        
        set((state) => ({
          notifications: [...state.notifications, { ...notification, id, timestamp }],
        }));
        
        // Auto-remove after 5 seconds
        setTimeout(() => {
          set((state) => ({
            notifications: state.notifications.filter((n) => n.id !== id),
          }));
        }, 5000);
      },
      
      removeNotification: (id) =>
        set((state) => ({
          notifications: state.notifications.filter((n) => n.id !== id),
        })),
    }),
    {
      name: 'ui-storage',
      partialize: (state) => ({ 
        sidebarOpen: state.sidebarOpen, 
        theme: state.theme 
      }),
    }
  )
);
