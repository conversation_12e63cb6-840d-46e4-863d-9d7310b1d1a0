{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600',\n    green: 'from-green-500 to-green-600',\n    amber: 'from-amber-500 to-amber-600',\n    red: 'from-red-500 to-red-600',\n    teal: 'from-teal-500 to-teal-600'\n  };\n\n  return (\n    <Card className={`relative overflow-hidden ${className}`} gradient>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-3xl font-bold text-gray-900 mb-1\">{value}</p>\n          {subtitle && (\n            <p className=\"text-sm text-gray-500\">{subtitle}</p>\n          )}\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              <span className={`text-sm font-medium ${\n                trend.isPositive ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%\n              </span>\n              <span className=\"text-xs text-gray-500 ml-1\">vs last month</span>\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div className={`p-3 rounded-lg bg-gradient-to-r ${colorClasses[color]} text-white`}>\n            {icon}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <Card \n      className={`cursor-pointer ${className}`}\n      onClick={() => onClick?.(patient)}\n      hover\n    >\n      <div className=\"flex items-center space-x-4\">\n        {/* Avatar */}\n        <div className=\"relative\">\n          {patient.profileImageUrl ? (\n            <img\n              src={patient.profileImageUrl}\n              alt={patient.fullName}\n              className=\"w-12 h-12 rounded-full object-cover\"\n            />\n          ) : (\n            <div className=\"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white font-semibold\">\n              {getInitials(patient.fullName)}\n            </div>\n          )}\n          <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\" />\n        </div>\n\n        {/* Patient Info */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n              {patient.fullName}\n            </h3>\n            <span className=\"text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full\">\n              {patient.patientId}\n            </span>\n          </div>\n          \n          <div className=\"flex items-center space-x-4 mt-1 text-sm text-gray-500\">\n            <span>{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n            <span>•</span>\n            <span>{patient.gender}</span>\n            <span>•</span>\n            <span>{patient.mobileNumber}</span>\n          </div>\n          \n          {patient.lastVisit && (\n            <p className=\"text-xs text-gray-400 mt-1\">\n              Last visit: {patient.lastVisit}\n            </p>\n          )}\n        </div>\n\n        {/* Action Arrow */}\n        <div className=\"text-gray-400\">\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n          </svg>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;;IAEnB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,aAAa,CAAC,OAAO,CAAC;IACxB,EAAE,WAAW,4CAA4C,WAAW;IACpE,EAAE,UAAU,mBAAmB,GAAG;IAClC,EAAE,UAAU;EACd,CAAC;IAED,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;AAiBO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,OAAO;QACP,KAAK;QACL,MAAM;IACR;IAEA,qBACE,8OAAC;QAAK,WAAW,CAAC,yBAAyB,EAAE,WAAW;QAAE,QAAQ;kBAChE,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,8OAAC;4BAAE,WAAU;sCAAyC;;;;;;wBACrD,0BACC,8OAAC;4BAAE,WAAU;sCAAyB;;;;;;wBAEvC,uBACC,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAK,WAAW,CAAC,oBAAoB,EACpC,MAAM,UAAU,GAAG,mBAAmB,gBACtC;;wCACC,MAAM,UAAU,GAAG,MAAM;wCAAI;wCAAE,KAAK,GAAG,CAAC,MAAM,KAAK;wCAAE;;;;;;;8CAExD,8OAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;gBAIlD,sBACC,8OAAC;oBAAI,WAAW,CAAC,gCAAgC,EAAE,YAAY,CAAC,MAAM,CAAC,WAAW,CAAC;8BAChF;;;;;;;;;;;;;;;;;AAMb;AAiBO,MAAM,cAA0C,CAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC;QACC,WAAW,CAAC,eAAe,EAAE,WAAW;QACxC,SAAS,IAAM,UAAU;QACzB,KAAK;kBAEL,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;wBACZ,QAAQ,eAAe,iBACtB,8OAAC;4BACC,KAAK,QAAQ,eAAe;4BAC5B,KAAK,QAAQ,QAAQ;4BACrB,WAAU;;;;;qFAGZ,8OAAC;4BAAI,WAAU;sCACZ,YAAY,QAAQ,QAAQ;;;;;;sCAGjC,8OAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ;;;;;;8CAEnB,8OAAC;oCAAK,WAAU;8CACb,QAAQ,SAAS;;;;;;;;;;;;sCAItB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;8CAAM,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,MAAM,CAAC,GAAG;;;;;;8CAC9C,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAM,QAAQ,MAAM;;;;;;8CACrB,8OAAC;8CAAK;;;;;;8CACN,8OAAC;8CAAM,QAAQ,YAAY;;;;;;;;;;;;wBAG5B,QAAQ,SAAS,kBAChB,8OAAC;4BAAE,WAAU;;gCAA6B;gCAC3B,QAAQ,SAAS;;;;;;;;;;;;;8BAMpC,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,8OAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;AAWO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,8OAAC;QAAK,WAAW;;0BACf,8OAAC;gBACC,WAAW,CAAC,kCAAkC,EAAE,cAAc,mBAAmB,IAAI;gBACrF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 446, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/patients/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  MagnifyingGlassIcon,\n  FunnelIcon,\n  UserPlusIcon,\n  AdjustmentsHorizontalIcon\n} from '@heroicons/react/24/outline';\nimport { PatientCard } from '@/components/ui/Card';\nimport { ButtonLoading } from '@/components/ui/Loading';\nimport { toast } from 'react-hot-toast';\n\nconst PatientsPage: React.FC = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showFilters, setShowFilters] = useState(false);\n  const [patients, setPatients] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [pagination, setPagination] = useState({\n    total: 0,\n    limit: 50,\n    offset: 0,\n    hasMore: false\n  });\n\n  // Fetch patients from API\n  const fetchPatients = async (search = '', offset = 0) => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        search,\n        limit: pagination.limit.toString(),\n        offset: offset.toString()\n      });\n\n      const response = await fetch(`/api/patients?${params}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setPatients(data.data);\n        setPagination(data.pagination);\n      } else {\n        toast.error('Failed to fetch patients');\n      }\n    } catch (error) {\n      console.error('Error fetching patients:', error);\n      toast.error('Failed to fetch patients');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchPatients();\n  }, []);\n\n  useEffect(() => {\n    const debounceTimer = setTimeout(() => {\n      fetchPatients(searchQuery, 0);\n    }, 300);\n\n    return () => clearTimeout(debounceTimer);\n  }, [searchQuery]);\n\n  const handlePatientClick = (patient: any) => {\n    window.location.href = `/patients/${patient.id}`;\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <motion.div\n      className=\"space-y-6\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Header */}\n      <motion.div \n        className=\"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4\"\n        variants={itemVariants}\n      >\n        <div>\n          <h1 className=\"text-3xl font-bold text-gray-900\">Patients</h1>\n          <p className=\"text-gray-600 mt-1\">\n            Manage and view all patient records\n          </p>\n        </div>\n        <motion.button\n          className=\"btn-primary\"\n          whileHover={{ scale: 1.02 }}\n          whileTap={{ scale: 0.98 }}\n          onClick={() => window.location.href = '/patients/new'}\n        >\n          <UserPlusIcon className=\"w-5 h-5\" />\n          Add New Patient\n        </motion.button>\n      </motion.div>\n\n      {/* Search and Filters */}\n      <motion.div \n        className=\"medical-card p-6\"\n        variants={itemVariants}\n      >\n        <div className=\"flex flex-col sm:flex-row gap-4\">\n          {/* Search Bar */}\n          <div className=\"flex-1 relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              className=\"form-input pl-10\"\n              placeholder=\"Search by name, patient ID, or phone number...\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n            />\n          </div>\n\n          {/* Filter Button */}\n          <motion.button\n            className=\"btn-secondary\"\n            onClick={() => setShowFilters(!showFilters)}\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <FunnelIcon className=\"w-5 h-5\" />\n            Filters\n          </motion.button>\n        </div>\n\n        {/* Filter Panel */}\n        <motion.div\n          initial={false}\n          animate={{ \n            height: showFilters ? 'auto' : 0,\n            opacity: showFilters ? 1 : 0\n          }}\n          transition={{ duration: 0.3 }}\n          style={{ overflow: 'hidden' }}\n        >\n          <div className=\"mt-6 pt-6 border-t border-gray-200\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-4\">\n              <div>\n                <label className=\"form-label\">Gender</label>\n                <select className=\"form-input\">\n                  <option value=\"\">All Genders</option>\n                  <option value=\"Male\">Male</option>\n                  <option value=\"Female\">Female</option>\n                  <option value=\"Other\">Other</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"form-label\">Age Range</label>\n                <select className=\"form-input\">\n                  <option value=\"\">All Ages</option>\n                  <option value=\"0-18\">0-18 years</option>\n                  <option value=\"19-35\">19-35 years</option>\n                  <option value=\"36-50\">36-50 years</option>\n                  <option value=\"51-65\">51-65 years</option>\n                  <option value=\"65+\">65+ years</option>\n                </select>\n              </div>\n              <div>\n                <label className=\"form-label\">Blood Group</label>\n                <select className=\"form-input\">\n                  <option value=\"\">All Blood Groups</option>\n                  <option value=\"A+\">A+</option>\n                  <option value=\"A-\">A-</option>\n                  <option value=\"B+\">B+</option>\n                  <option value=\"B-\">B-</option>\n                  <option value=\"AB+\">AB+</option>\n                  <option value=\"AB-\">AB-</option>\n                  <option value=\"O+\">O+</option>\n                  <option value=\"O-\">O-</option>\n                </select>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n\n      {/* Results Summary */}\n      <motion.div variants={itemVariants}>\n        <p className=\"text-gray-600\">\n          {loading ? 'Loading patients...' : `Showing ${patients.length} of ${pagination.total} patients`}\n        </p>\n      </motion.div>\n\n      {/* Patient List */}\n      {loading ? (\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n          {Array.from({ length: 6 }).map((_, index) => (\n            <div key={index} className=\"medical-card p-6 animate-pulse\">\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n                <div className=\"flex-1 space-y-2\">\n                  <div className=\"h-4 bg-gray-200 rounded w-3/4\" />\n                  <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n      ) : (\n        <motion.div\n          className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\"\n          variants={containerVariants}\n        >\n          {patients.map((patient, index) => (\n            <motion.div\n              key={patient.id}\n              variants={itemVariants}\n              transition={{ delay: index * 0.05 }}\n            >\n              <PatientCard\n                patient={patient}\n                onClick={handlePatientClick}\n              />\n            </motion.div>\n          ))}\n        </motion.div>\n      )}\n\n      {/* Empty State */}\n      {!loading && patients.length === 0 && (\n        <motion.div\n          className=\"text-center py-12\"\n          variants={itemVariants}\n        >\n          <div className=\"w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center\">\n            <MagnifyingGlassIcon className=\"w-12 h-12 text-gray-400\" />\n          </div>\n          <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n            No patients found\n          </h3>\n          <p className=\"text-gray-600 mb-6\">\n            Try adjusting your search criteria or add a new patient.\n          </p>\n          <motion.button\n            className=\"btn-primary\"\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n            onClick={() => window.location.href = '/patients/new'}\n          >\n            <UserPlusIcon className=\"w-5 h-5\" />\n            Add New Patient\n          </motion.button>\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default PatientsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAMA;AAEA;AAZA;;;;;;;AAcA,MAAM,eAAyB;IAC7B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,OAAO;QACP,OAAO;QACP,QAAQ;QACR,SAAS;IACX;IAEA,0BAA0B;IAC1B,MAAM,gBAAgB,OAAO,SAAS,EAAE,EAAE,SAAS,CAAC;QAClD,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC;gBACA,OAAO,WAAW,KAAK,CAAC,QAAQ;gBAChC,QAAQ,OAAO,QAAQ;YACzB;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,QAAQ;YACtD,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI;gBACrB,cAAc,KAAK,UAAU;YAC/B,OAAO;gBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,WAAW;YAC/B,cAAc,aAAa;QAC7B,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAY;IAEhB,MAAM,qBAAqB,CAAC;QAC1B,OAAO,QAAQ,CAAC,IAAI,GAAG,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;IAClD;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0CAEtC,8OAAC,uNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4CAAC,WAAU;;;;;;;;;;;kDAEjC,8OAAC;wCACC,MAAK;wCACL,WAAU;wCACV,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;0CAKlD,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,SAAS,IAAM,eAAe,CAAC;gCAC/B,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,8OAAC,mNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAMtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;wBACT,SAAS;4BACP,QAAQ,cAAc,SAAS;4BAC/B,SAAS,cAAc,IAAI;wBAC7B;wBACA,YAAY;4BAAE,UAAU;wBAAI;wBAC5B,OAAO;4BAAE,UAAU;wBAAS;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAS;;;;;;kEACvB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;;;;;;;;;;;;;kDAG1B,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAO;;;;;;kEACrB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAQ;;;;;;kEACtB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;;;;;;;;;;;;;kDAGxB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAa;;;;;;0DAC9B,8OAAC;gDAAO,WAAU;;kEAChB,8OAAC;wDAAO,OAAM;kEAAG;;;;;;kEACjB,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAM;;;;;;kEACpB,8OAAC;wDAAO,OAAM;kEAAK;;;;;;kEACnB,8OAAC;wDAAO,OAAM;kEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,8OAAC;oBAAE,WAAU;8BACV,UAAU,wBAAwB,CAAC,QAAQ,EAAE,SAAS,MAAM,CAAC,IAAI,EAAE,WAAW,KAAK,CAAC,SAAS,CAAC;;;;;;;;;;;YAKlG,wBACC,8OAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;;uBALX;;;;;;;;;yEAYd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAET,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU;wBACV,YAAY;4BAAE,OAAO,QAAQ;wBAAK;kCAElC,cAAA,8OAAC,gIAAA,CAAA,cAAW;4BACV,SAAS;4BACT,SAAS;;;;;;uBANN,QAAQ,EAAE;;;;;;;;;;YActB,CAAC,WAAW,SAAS,MAAM,KAAK,mBAC/B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,qOAAA,CAAA,sBAAmB;4BAAC,WAAU;;;;;;;;;;;kCAEjC,8OAAC;wBAAG,WAAU;kCAAyC;;;;;;kCAGvD,8OAAC;wBAAE,WAAU;kCAAqB;;;;;;kCAGlC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wBACZ,WAAU;wBACV,YAAY;4BAAE,OAAO;wBAAK;wBAC1B,UAAU;4BAAE,OAAO;wBAAK;wBACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0CAEtC,8OAAC,uNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAOhD;uCAEe", "debugId": null}}]}