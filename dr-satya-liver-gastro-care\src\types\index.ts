// User and Authentication Types
export interface User {
  id: string;
  email: string;
  fullName: string;
  role: 'doctor' | 'nurse' | 'admin';
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// Patient Types
export interface Patient {
  id: string;
  patientId: string;
  
  // Basic Information
  fullName: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female' | 'Other';
  aadharNumber?: string;
  mobileNumber: string;
  email?: string;
  address?: string;
  
  // Physical Information
  heightCm?: number;
  weightKg?: number;
  bmi?: number;
  bloodGroup?: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-';
  
  // Medical Scores
  charlsonIndex?: number; // 0-37
  asaGrade?: number; // 1-6
  ecogGrade?: number; // 0-5
  
  // Profile Image
  profileImageUrl?: string;
  
  // Comorbidities
  comorbidities?: PatientComorbidity[];
  
  // Metadata
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface PatientFormData {
  // Basic Information
  fullName: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female' | 'Other';
  aadharNumber: string;
  mobileNumber: string;
  email: string;
  address: string;
  
  // Physical Information
  heightCm: number;
  weightKg: number;
  bloodGroup: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-' | '';
  
  // Medical Scores
  charlsonIndex: number;
  asaGrade: number;
  ecogGrade: number;
  
  // Comorbidities
  comorbidities: string[]; // Array of comorbidity IDs
  
  // Profile Image
  profileImage?: File;
}

// Comorbidity Types
export interface Comorbidity {
  id: string;
  name: string;
  description?: string;
  category: string;
  isActive: boolean;
  createdAt: string;
}

export interface PatientComorbidity {
  id: string;
  patientId: string;
  comorbidity: Comorbidity;
  severity: 'Mild' | 'Moderate' | 'Severe';
  diagnosedDate?: string;
  notes?: string;
  createdAt: string;
}

// Search and Filter Types
export interface PatientSearchParams {
  query?: string;
  gender?: string;
  bloodGroup?: string;
  ageRange?: {
    min: number;
    max: number;
  };
  comorbidities?: string[];
  sortBy?: 'name' | 'createdAt' | 'patientId';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Dashboard Types
export interface DashboardStats {
  totalPatients: number;
  newPatientsThisMonth: number;
  averageAge: number;
  genderDistribution: {
    male: number;
    female: number;
    other: number;
  };
  commonComorbidities: {
    name: string;
    count: number;
    percentage: number;
  }[];
  bloodGroupDistribution: {
    [key: string]: number;
  };
  bmiDistribution: {
    underweight: number;
    normal: number;
    overweight: number;
    obese: number;
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState {
  isLoading: boolean;
  errors: ValidationError[];
  isValid: boolean;
}

// File Upload Types
export interface FileUploadResponse {
  url: string;
  key: string;
  size: number;
  type: string;
}

// Medical Reference Types
export interface MedicalReference {
  charlsonIndexItems: {
    condition: string;
    score: number;
  }[];
  asaGrades: {
    grade: number;
    description: string;
  }[];
  ecogGrades: {
    grade: number;
    description: string;
  }[];
  bmiCategories: {
    category: string;
    range: string;
    color: string;
  }[];
}
