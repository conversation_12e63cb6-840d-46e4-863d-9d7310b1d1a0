-- <PERSON>'s Liver & Gastro Care Database Schema
-- PostgreSQL Schema for NeonDB

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table for medical staff authentication
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VA<PERSON>HA<PERSON>(255) NOT NULL,
    role VARCHAR(50) NOT NULL DEFAULT 'doctor', -- doctor, nurse, admin
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Patients table with comprehensive medical information
CREATE TABLE patients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id VARCHAR(20) UNIQUE NOT NULL, -- Auto-generated patient ID
    
    -- Basic Information
    full_name VARCHAR(255) NOT NULL,
    date_of_birth DATE NOT NULL,
    gender VARCHAR(10) NOT NULL CHECK (gender IN ('Male', 'Female', 'Other')),
    aadhar_number VARCHAR(12) UNIQUE,
    mobile_number VARCHAR(15) NOT NULL,
    email VARCHAR(255),
    address TEXT,
    
    -- Physical Information
    height_cm DECIMAL(5,2), -- Height in centimeters
    weight_kg DECIMAL(5,2), -- Weight in kilograms
    bmi DECIMAL(4,2) GENERATED ALWAYS AS (
        CASE 
            WHEN height_cm > 0 THEN weight_kg / POWER(height_cm / 100, 2)
            ELSE NULL
        END
    ) STORED,
    blood_group VARCHAR(5) CHECK (blood_group IN ('A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-')),
    
    -- Medical Scores
    charlson_index INTEGER CHECK (charlson_index >= 0 AND charlson_index <= 37),
    asa_grade INTEGER CHECK (asa_grade >= 1 AND asa_grade <= 6),
    ecog_grade INTEGER CHECK (ecog_grade >= 0 AND ecog_grade <= 5),
    
    -- Profile Image
    profile_image_url TEXT,
    
    -- Metadata
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Comorbidities lookup table
CREATE TABLE comorbidities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    category VARCHAR(100), -- cardiovascular, endocrine, respiratory, etc.
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Patient comorbidities junction table
CREATE TABLE patient_comorbidities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    patient_id UUID NOT NULL REFERENCES patients(id) ON DELETE CASCADE,
    comorbidity_id UUID NOT NULL REFERENCES comorbidities(id),
    severity VARCHAR(20) CHECK (severity IN ('Mild', 'Moderate', 'Severe')),
    diagnosed_date DATE,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(patient_id, comorbidity_id)
);

-- Indexes for better performance
CREATE INDEX idx_patients_patient_id ON patients(patient_id);
CREATE INDEX idx_patients_mobile ON patients(mobile_number);
CREATE INDEX idx_patients_aadhar ON patients(aadhar_number);
CREATE INDEX idx_patients_name ON patients(full_name);
CREATE INDEX idx_patients_created_at ON patients(created_at);
CREATE INDEX idx_patient_comorbidities_patient ON patient_comorbidities(patient_id);
CREATE INDEX idx_users_email ON users(email);

-- Insert default comorbidities
INSERT INTO comorbidities (name, description, category) VALUES
('Diabetes Mellitus', 'Type 1 or Type 2 Diabetes', 'Endocrine'),
('Hypertension', 'High Blood Pressure', 'Cardiovascular'),
('Heart Disease', 'Coronary Artery Disease, Heart Failure', 'Cardiovascular'),
('Chronic Kidney Disease', 'Reduced kidney function', 'Renal'),
('Liver Disease', 'Chronic liver conditions', 'Hepatic'),
('COPD', 'Chronic Obstructive Pulmonary Disease', 'Respiratory'),
('Asthma', 'Chronic respiratory condition', 'Respiratory'),
('Obesity', 'BMI > 30', 'Metabolic'),
('Stroke', 'Cerebrovascular accident', 'Neurological'),
('Cancer', 'Active or history of malignancy', 'Oncological'),
('Depression', 'Major depressive disorder', 'Psychiatric'),
('Anxiety', 'Anxiety disorders', 'Psychiatric'),
('Arthritis', 'Rheumatoid or Osteoarthritis', 'Musculoskeletal'),
('Osteoporosis', 'Bone density loss', 'Musculoskeletal'),
('Thyroid Disease', 'Hyper or Hypothyroidism', 'Endocrine');

-- Function to generate patient ID
CREATE OR REPLACE FUNCTION generate_patient_id()
RETURNS TRIGGER AS $$
BEGIN
    NEW.patient_id := 'PAT' || TO_CHAR(CURRENT_DATE, 'YYYY') || 
                      LPAD(EXTRACT(DOY FROM CURRENT_DATE)::TEXT, 3, '0') || 
                      LPAD(NEXTVAL('patient_id_seq')::TEXT, 4, '0');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create sequence for patient ID
CREATE SEQUENCE patient_id_seq START 1;

-- Trigger to auto-generate patient ID
CREATE TRIGGER trigger_generate_patient_id
    BEFORE INSERT ON patients
    FOR EACH ROW
    EXECUTE FUNCTION generate_patient_id();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for updated_at
CREATE TRIGGER trigger_users_updated_at
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_patients_updated_at
    BEFORE UPDATE ON patients
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
