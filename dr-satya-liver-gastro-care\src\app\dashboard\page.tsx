'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  UserPlusIcon,
  ChartBarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { StatCard } from '@/components/ui/Card';
import { useAuthStore } from '@/store';
import { toast } from 'react-hot-toast';

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/dashboard/stats');
        const data = await response.json();

        if (data.success) {
          setStats(data.data);
        } else {
          toast.error('Failed to fetch dashboard statistics');
        }
      } catch (error) {
        console.error('Error fetching stats:', error);
        toast.error('Failed to fetch dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = stats ? [
    {
      title: 'Total Patients',
      value: stats.totalPatients.toLocaleString(),
      subtitle: 'Active patients',
      icon: <UserGroupIcon className="w-6 h-6" />,
      trend: { value: 12, isPositive: true },
      color: 'blue' as const,
    },
    {
      title: 'New Patients',
      value: stats.newPatientsThisMonth.toString(),
      subtitle: 'This month',
      icon: <UserPlusIcon className="w-6 h-6" />,
      trend: { value: 8, isPositive: true },
      color: 'green' as const,
    },
    {
      title: 'Appointments',
      value: stats.appointments.toString(),
      subtitle: 'This week',
      icon: <ChartBarIcon className="w-6 h-6" />,
      trend: { value: 3, isPositive: false },
      color: 'amber' as const,
    },
    {
      title: 'Critical Cases',
      value: stats.criticalCases.toString(),
      subtitle: 'Require attention',
      icon: <HeartIcon className="w-6 h-6" />,
      trend: { value: 2, isPositive: false },
      color: 'red' as const,
    },
  ] : [];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Clean Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-2">
              Welcome back, {user ? `${user.firstName} ${user.lastName}` : 'Doctor'}
            </h1>
            <p className="text-gray-600 text-lg">
              Here's what's happening with your patients today.
            </p>
          </div>

          {/* Time Display */}
          <div className="text-right">
            <div className="text-2xl font-bold text-gray-800">
              {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
            </div>
            <div className="text-sm text-gray-500">
              {new Date().toLocaleDateString('en-US', {
                weekday: 'long',
                month: 'long',
                day: 'numeric'
              })}
            </div>
          </div>
        </div>
      </motion.div>

      {/* Stats Grid - Clean Layout */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={containerVariants}
      >
        {loading ? (
          // Loading skeletons
          Array.from({ length: 4 }).map((_, index) => (
            <motion.div key={index} variants={itemVariants}>
              <div className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 animate-pulse">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded mb-3"></div>
                    <div className="h-8 bg-gray-200 rounded mb-2"></div>
                    <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                  </div>
                  <div className="w-12 h-12 bg-gray-200 rounded-lg"></div>
                </div>
              </div>
            </motion.div>
          ))
        ) : (
          statCards.map((stat, index) => (
            <motion.div
              key={stat.title}
              variants={itemVariants}
              whileHover={{
                y: -4,
                transition: { duration: 0.2 }
              }}
            >
              <StatCard {...stat} />
            </motion.div>
          ))
        )}
      </motion.div>

      {/* Quick Actions - Clean Layout */}
      <motion.div variants={itemVariants}>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <motion.div
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 cursor-pointer group hover:shadow-md transition-all duration-200"
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.location.href = '/patients/new'}
          >
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-blue-100 rounded-lg group-hover:bg-blue-200 transition-colors">
                <UserPlusIcon className="w-6 h-6 text-blue-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">Add New Patient</h3>
                <p className="text-sm text-gray-600">Register a new patient in the system</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 cursor-pointer group hover:shadow-md transition-all duration-200"
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.location.href = '/patients'}
          >
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-emerald-100 rounded-lg group-hover:bg-emerald-200 transition-colors">
                <UserGroupIcon className="w-6 h-6 text-emerald-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">View All Patients</h3>
                <p className="text-sm text-gray-600">Browse and search patient records</p>
              </div>
            </div>
          </motion.div>

          <motion.div
            className="bg-white rounded-xl p-6 shadow-sm border border-gray-100 cursor-pointer group hover:shadow-md transition-all duration-200"
            whileHover={{ y: -2 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.location.href = '/analytics'}
          >
            <div className="flex items-center space-x-4">
              <div className="p-3 bg-purple-100 rounded-lg group-hover:bg-purple-200 transition-colors">
                <ChartBarIcon className="w-6 h-6 text-purple-600" />
              </div>
              <div>
                <h3 className="font-semibold text-gray-900">View Analytics</h3>
                <p className="text-sm text-gray-600">Check medical reports and insights</p>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Recent Activity - Clean Layout */}
      <motion.div variants={itemVariants}>
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Recent Activity</h2>
        <div className="bg-white rounded-xl shadow-sm border border-gray-100 p-6">
          <div className="space-y-4">
            {loading ? (
              // Loading skeleton
              Array.from({ length: 5 }).map((_, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0 animate-pulse">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-16"></div>
                </div>
              ))
            ) : (
              stats?.recentActivity?.map((activity: any, index: number) => (
                <motion.div
                  key={index}
                  className="flex items-center justify-between py-4 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded-lg px-2 transition-colors"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                      <UserPlusIcon className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="font-medium text-gray-900">{activity.action}</p>
                      <p className="text-sm text-gray-600">Patient: {activity.patient}</p>
                    </div>
                  </div>
                  <span className="text-sm text-gray-500">{activity.time}</span>
                </motion.div>
              )) || (
                <div className="text-center py-8">
                  <div className="w-12 h-12 bg-gray-100 rounded-full mx-auto mb-3 flex items-center justify-center">
                    <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                  <p className="text-gray-500">No recent activity</p>
                </div>
              )
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default DashboardPage;
