import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/database';

// GET - Fetch single patient by ID
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      const query = `
        SELECT 
          id,
          first_name,
          middle_name,
          last_name,
          date_of_birth,
          gender,
          aadhar_number,
          mobile_number,
          alternate_phone,
          email,
          instagram_id,
          facebook_id,
          referred_by,
          house_number,
          village,
          post_office,
          address,
          city,
          state,
          pincode,
          country,
          marital_status,
          occupation,
          education,
          religion,
          nationality,
          height,
          weight,
          bmi,
          height_unit,
          weight_unit,
          blood_group,
          charlson_index,
          asa_grade,
          ecog_grade,
          dm_status,
          htn_status,
          hyperlipidemia_status,
          hypothyroid_status,
          cardiac_disease_status,
          pulmonary_disease_status,
          neurological_disease_status,
          rheumatological_disease_status,
          other_diseases,
          date_of_visit,
          primary_disease,
          symptoms,
          clinical_examination,
          investigations,
          final_diagnosis,
          ultrasonography_date,
          ultrasonography_findings,
          cect_date,
          cect_findings,
          endoscopy_date,
          endoscopy_findings,
          biopsy_date,
          biopsy_findings,
          colonoscopy_date,
          colonoscopy_findings,
          colonoscopic_biopsy_date,
          colonoscopic_biopsy_findings,
          pet_ct_findings,
          other_biopsy_date,
          other_biopsy_findings,
          medications,
          primary_treatment_plan,
          admission_date,
          surgery_plan_date,
          surgery_name,
          surgery_risks,
          consent_obtained,
          surgery_date,
          surgeon,
          assistant_surgeon,
          ot_findings,
          ot_procedure,
          hospital_course,
          complications,
          clavien_dindo_grade,
          discharge_date,
          discharge_medications,
          discharge_advice,
          next_follow_up_date,
          conservative_treatment,
          icu_stay,
          hospital_stay,
          plan_for_surgery,
          surgery_plan_date_conservative,
          final_biopsy,
          disease_stage,
          chemotherapy_radiotherapy,
          further_management_plan,
          medication_1,
          medication_2,
          medication_3,
          medication_4,
          medication_5,
          comorbidities,
          allergies,
          habits,
          current_medications,
          family_history,
          social_history,
          notes,
          is_pregnant,
          is_breastfeeding,
          has_insurance,
          insurance_provider,
          policy_number,
          profile_image_url,
          created_by,
          created_at,
          updated_at
        FROM patients 
        WHERE id = $1
      `;

      const result = await client.query(query, [id]);
      
      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'Patient not found' },
          { status: 404 }
        );
      }

      const row = result.rows[0];
      
      // Transform data to match frontend expectations
      const patient = {
        id: row.id,
        fullName: `${row.first_name} ${row.middle_name ? row.middle_name + ' ' : ''}${row.last_name}`.trim(),
        patientId: row.id.substring(0, 8).toUpperCase(),
        firstName: row.first_name,
        middleName: row.middle_name,
        lastName: row.last_name,
        dateOfBirth: row.date_of_birth,
        age: row.date_of_birth ? new Date().getFullYear() - new Date(row.date_of_birth).getFullYear() : null,
        gender: row.gender,
        aadharNumber: row.aadhar_number,
        mobileNumber: row.mobile_number,
        alternatePhone: row.alternate_phone,
        email: row.email,
        instagramId: row.instagram_id,
        facebookId: row.facebook_id,
        referredBy: row.referred_by,
        houseNumber: row.house_number,
        village: row.village,
        postOffice: row.post_office,
        address: row.address,
        city: row.city,
        state: row.state,
        pincode: row.pincode,
        country: row.country,
        maritalStatus: row.marital_status,
        occupation: row.occupation,
        education: row.education,
        religion: row.religion,
        nationality: row.nationality,
        height: row.height,
        weight: row.weight,
        bmi: row.bmi,
        heightUnit: row.height_unit,
        weightUnit: row.weight_unit,
        bloodGroup: row.blood_group,
        charlsonIndex: row.charlson_index,
        asaGrade: row.asa_grade,
        ecogGrade: row.ecog_grade,
        dmStatus: row.dm_status,
        htnStatus: row.htn_status,
        hyperlipidemiaStatus: row.hyperlipidemia_status,
        hypothyroidStatus: row.hypothyroid_status,
        cardiacDiseaseStatus: row.cardiac_disease_status,
        pulmonaryDiseaseStatus: row.pulmonary_disease_status,
        neurologicalDiseaseStatus: row.neurological_disease_status,
        rheumatologicalDiseaseStatus: row.rheumatological_disease_status,
        otherDiseases: row.other_diseases,
        dateOfVisit: row.date_of_visit,
        primaryDisease: row.primary_disease,
        symptoms: row.symptoms,
        clinicalExamination: row.clinical_examination,
        investigations: row.investigations,
        finalDiagnosis: row.final_diagnosis,
        medications: row.medications,
        primaryTreatmentPlan: row.primary_treatment_plan,
        comorbidities: row.comorbidities,
        allergies: row.allergies,
        habits: row.habits,
        currentMedications: row.current_medications,
        familyHistory: row.family_history,
        socialHistory: row.social_history,
        notes: row.notes,
        isPregnant: row.is_pregnant,
        isBreastfeeding: row.is_breastfeeding,
        hasInsurance: row.has_insurance,
        insuranceProvider: row.insurance_provider,
        policyNumber: row.policy_number,
        profileImageUrl: row.profile_image_url,
        createdBy: row.created_by,
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };

      return NextResponse.json({
        success: true,
        data: patient
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching patient:', error);
    return NextResponse.json(
      { error: 'Failed to fetch patient' },
      { status: 500 }
    );
  }
}

// PUT - Update patient
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const patientData = await request.json();

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      // Build dynamic update query based on provided fields
      const updateFields = [];
      const values = [];
      let paramCount = 1;

      // Add fields that can be updated
      const updatableFields = [
        'first_name', 'middle_name', 'last_name', 'date_of_birth', 'gender',
        'mobile_number', 'email', 'address', 'height', 'weight', 'blood_group',
        'charlson_index', 'asa_grade', 'ecog_grade', 'occupation', 'marital_status',
        'city', 'state', 'pincode', 'notes'
      ];

      for (const field of updatableFields) {
        const camelCaseField = field.replace(/_([a-z])/g, (g) => g[1].toUpperCase());
        if (patientData[camelCaseField] !== undefined) {
          updateFields.push(`${field} = $${paramCount}`);
          values.push(patientData[camelCaseField]);
          paramCount++;
        }
      }

      if (updateFields.length === 0) {
        return NextResponse.json(
          { error: 'No valid fields to update' },
          { status: 400 }
        );
      }

      // Add updated_at
      updateFields.push(`updated_at = $${paramCount}`);
      values.push(new Date().toISOString());
      paramCount++;

      // Add ID for WHERE clause
      values.push(id);

      const query = `
        UPDATE patients 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING *
      `;

      const result = await client.query(query, values);
      
      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'Patient not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        data: result.rows[0],
        message: 'Patient updated successfully'
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error updating patient:', error);
    return NextResponse.json(
      { error: 'Failed to update patient' },
      { status: 500 }
    );
  }
}

// DELETE - Delete patient
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json(
        { error: 'Patient ID is required' },
        { status: 400 }
      );
    }

    const client = await pool.connect();
    
    try {
      const result = await client.query(
        'DELETE FROM patients WHERE id = $1 RETURNING id',
        [id]
      );
      
      if (result.rows.length === 0) {
        return NextResponse.json(
          { error: 'Patient not found' },
          { status: 404 }
        );
      }

      return NextResponse.json({
        success: true,
        message: 'Patient deleted successfully'
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error deleting patient:', error);
    return NextResponse.json(
      { error: 'Failed to delete patient' },
      { status: 500 }
    );
  }
}
