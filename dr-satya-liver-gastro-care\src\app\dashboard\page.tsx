'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  UserPlusIcon,
  ChartBarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { StatCard } from '@/components/ui/Card';
import { useAuthStore } from '@/store';
import { toast } from 'react-hot-toast';

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/dashboard/stats');
        const data = await response.json();

        if (data.success) {
          setStats(data.data);
        } else {
          toast.error('Failed to fetch dashboard statistics');
        }
      } catch (error) {
        console.error('Error fetching stats:', error);
        toast.error('Failed to fetch dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = stats ? [
    {
      title: 'Total Patients',
      value: stats.totalPatients.toLocaleString(),
      subtitle: 'Active patients',
      icon: <UserGroupIcon className="w-6 h-6" />,
      trend: { value: 12, isPositive: true },
      color: 'blue' as const,
    },
    {
      title: 'New Patients',
      value: stats.newPatientsThisMonth.toString(),
      subtitle: 'This month',
      icon: <UserPlusIcon className="w-6 h-6" />,
      trend: { value: 8, isPositive: true },
      color: 'green' as const,
    },
    {
      title: 'Appointments',
      value: stats.appointments.toString(),
      subtitle: 'This week',
      icon: <ChartBarIcon className="w-6 h-6" />,
      trend: { value: 3, isPositive: false },
      color: 'amber' as const,
    },
    {
      title: 'Critical Cases',
      value: stats.criticalCases.toString(),
      subtitle: 'Require attention',
      icon: <HeartIcon className="w-6 h-6" />,
      trend: { value: 2, isPositive: false },
      color: 'red' as const,
    },
  ] : [];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {user ? `${user.firstName} ${user.lastName}` : 'Doctor'}
        </h1>
        <p className="text-gray-600">
          Here's what's happening with your patients today.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={containerVariants}
      >
        {loading ? (
          // Loading skeleton
          Array.from({ length: 4 }).map((_, index) => (
            <motion.div key={index} variants={itemVariants}>
              <div className="medical-card p-6 animate-pulse">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-1/2 mb-2" />
                    <div className="h-8 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/3" />
                  </div>
                  <div className="w-12 h-12 bg-gray-200 rounded-lg" />
                </div>
              </div>
            </motion.div>
          ))
        ) : (
          statCards.map((stat, index) => (
            <motion.div key={stat.title} variants={itemVariants}>
              <StatCard {...stat} />
            </motion.div>
          ))
        )}
      </motion.div>

      {/* Quick Actions */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <motion.button
            className="medical-card p-6 text-left hover:shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.location.href = '/patients/new'}
          >
            <UserPlusIcon className="w-8 h-8 text-blue-600 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">Add New Patient</h3>
            <p className="text-sm text-gray-600">Register a new patient in the system</p>
          </motion.button>

          <motion.button
            className="medical-card p-6 text-left hover:shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.location.href = '/patients'}
          >
            <UserGroupIcon className="w-8 h-8 text-green-600 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">View All Patients</h3>
            <p className="text-sm text-gray-600">Browse and search patient records</p>
          </motion.button>

          <motion.button
            className="medical-card p-6 text-left hover:shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            onClick={() => window.location.href = '/analytics'}
          >
            <ChartBarIcon className="w-8 h-8 text-purple-600 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">View Analytics</h3>
            <p className="text-sm text-gray-600">Check medical reports and insights</p>
          </motion.button>
        </div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Recent Activity
        </h2>
        <div className="medical-card p-6">
          <div className="space-y-4">
            {loading ? (
              // Loading skeleton for recent activity
              Array.from({ length: 3 }).map((_, index) => (
                <div key={index} className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0 animate-pulse">
                  <div className="flex-1">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mb-2" />
                    <div className="h-3 bg-gray-200 rounded w-1/2" />
                  </div>
                  <div className="h-3 bg-gray-200 rounded w-16" />
                </div>
              ))
            ) : (
              stats?.recentActivity?.map((activity: any, index: number) => (
                <motion.div
                  key={index}
                  className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                >
                  <div>
                    <p className="font-medium text-gray-900">{activity.action}</p>
                    <p className="text-sm text-gray-600">Patient: {activity.patient}</p>
                  </div>
                  <span className="text-sm text-gray-500">{activity.time}</span>
                </motion.div>
              )) || (
                <p className="text-gray-500 text-center py-4">No recent activity</p>
              )
            )}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default DashboardPage;
