'use client';

import React from 'react';
import { motion } from 'framer-motion';
import { 
  UserGroupIcon, 
  UserPlusIcon, 
  ChartBarIcon,
  HeartIcon 
} from '@heroicons/react/24/outline';
import { StatCard } from '@/components/ui/Card';

const DashboardPage: React.FC = () => {
  const stats = [
    {
      title: 'Total Patients',
      value: '1,247',
      subtitle: 'Active patients',
      icon: <UserGroupIcon className="w-6 h-6" />,
      trend: { value: 12, isPositive: true },
      color: 'blue' as const,
    },
    {
      title: 'New Patients',
      value: '23',
      subtitle: 'This month',
      icon: <UserPlusIcon className="w-6 h-6" />,
      trend: { value: 8, isPositive: true },
      color: 'green' as const,
    },
    {
      title: 'Appointments',
      value: '156',
      subtitle: 'This week',
      icon: <ChartBarIcon className="w-6 h-6" />,
      trend: { value: 3, isPositive: false },
      color: 'amber' as const,
    },
    {
      title: 'Critical Cases',
      value: '4',
      subtitle: 'Require attention',
      icon: <HeartIcon className="w-6 h-6" />,
      trend: { value: 2, isPositive: false },
      color: 'red' as const,
    },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, Dr. Johnson
        </h1>
        <p className="text-gray-600">
          Here's what's happening with your patients today.
        </p>
      </motion.div>

      {/* Stats Grid */}
      <motion.div
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
        variants={containerVariants}
      >
        {stats.map((stat, index) => (
          <motion.div key={stat.title} variants={itemVariants}>
            <StatCard {...stat} />
          </motion.div>
        ))}
      </motion.div>

      {/* Quick Actions */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <motion.button
            className="medical-card p-6 text-left hover:shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <UserPlusIcon className="w-8 h-8 text-blue-600 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">Add New Patient</h3>
            <p className="text-sm text-gray-600">Register a new patient in the system</p>
          </motion.button>

          <motion.button
            className="medical-card p-6 text-left hover:shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <UserGroupIcon className="w-8 h-8 text-green-600 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">View All Patients</h3>
            <p className="text-sm text-gray-600">Browse and search patient records</p>
          </motion.button>

          <motion.button
            className="medical-card p-6 text-left hover:shadow-lg transition-all duration-200"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <ChartBarIcon className="w-8 h-8 text-purple-600 mb-3" />
            <h3 className="font-semibold text-gray-900 mb-1">View Analytics</h3>
            <p className="text-sm text-gray-600">Check medical reports and insights</p>
          </motion.button>
        </div>
      </motion.div>

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Recent Activity
        </h2>
        <div className="medical-card p-6">
          <div className="space-y-4">
            {[
              { action: 'New patient registered', patient: 'John Doe', time: '2 hours ago' },
              { action: 'Patient record updated', patient: 'Sarah Smith', time: '4 hours ago' },
              { action: 'Lab results uploaded', patient: 'Mike Johnson', time: '6 hours ago' },
            ].map((activity, index) => (
              <motion.div
                key={index}
                className="flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0"
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <div>
                  <p className="font-medium text-gray-900">{activity.action}</p>
                  <p className="text-sm text-gray-600">Patient: {activity.patient}</p>
                </div>
                <span className="text-sm text-gray-500">{activity.time}</span>
              </motion.div>
            ))}
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default DashboardPage;
