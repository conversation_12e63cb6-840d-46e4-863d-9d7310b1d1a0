'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ChartBarIcon, 
  UserGroupIcon, 
  HeartIcon,
  CalendarIcon 
} from '@heroicons/react/24/outline';
import { InfoCard } from '@/components/ui/Card';
import Loading from '@/components/ui/Loading';
import { toast } from 'react-hot-toast';

const AnalyticsPage: React.FC = () => {
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/dashboard/stats');
        const data = await response.json();
        
        if (data.success) {
          setStats(data.data);
        } else {
          toast.error('Failed to fetch analytics data');
        }
      } catch (error) {
        console.error('Error fetching analytics:', error);
        toast.error('Failed to fetch analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loading text="Loading analytics..." fullScreen />;
  }

  return (
    <motion.div
      className="space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Medical Analytics
        </h1>
        <p className="text-gray-600">
          Comprehensive insights and reports for patient care management.
        </p>
      </motion.div>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <motion.div variants={itemVariants}>
          <div className="medical-card p-6 text-center">
            <UserGroupIcon className="w-12 h-12 text-blue-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900">{stats?.totalPatients || 0}</h3>
            <p className="text-gray-600">Total Patients</p>
          </div>
        </motion.div>

        <motion.div variants={itemVariants}>
          <div className="medical-card p-6 text-center">
            <CalendarIcon className="w-12 h-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900">{stats?.newPatientsThisMonth || 0}</h3>
            <p className="text-gray-600">New This Month</p>
          </div>
        </motion.div>

        <motion.div variants={itemVariants}>
          <div className="medical-card p-6 text-center">
            <ChartBarIcon className="w-12 h-12 text-purple-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900">{stats?.averageAge || 0}</h3>
            <p className="text-gray-600">Average Age</p>
          </div>
        </motion.div>

        <motion.div variants={itemVariants}>
          <div className="medical-card p-6 text-center">
            <HeartIcon className="w-12 h-12 text-red-600 mx-auto mb-4" />
            <h3 className="text-2xl font-bold text-gray-900">{stats?.criticalCases || 0}</h3>
            <p className="text-gray-600">Critical Cases</p>
          </div>
        </motion.div>
      </div>

      {/* Detailed Analytics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Gender Distribution */}
        <motion.div variants={itemVariants}>
          <InfoCard title="Gender Distribution" icon={<UserGroupIcon className="w-5 h-5" />}>
            <div className="space-y-4">
              {stats?.genderDistribution && Object.entries(stats.genderDistribution).map(([gender, count]) => (
                <div key={gender} className="flex items-center justify-between">
                  <span className="capitalize text-gray-700">{gender}</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-32 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full" 
                        style={{ 
                          width: `${(count as number / stats.totalPatients) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-8">{count as number}</span>
                  </div>
                </div>
              ))}
            </div>
          </InfoCard>
        </motion.div>

        {/* Blood Group Distribution */}
        <motion.div variants={itemVariants}>
          <InfoCard title="Blood Group Distribution" icon={<HeartIcon className="w-5 h-5" />}>
            <div className="space-y-3">
              {stats?.bloodGroupDistribution && Object.entries(stats.bloodGroupDistribution)
                .sort(([,a], [,b]) => (b as number) - (a as number))
                .slice(0, 6)
                .map(([bloodGroup, count]) => (
                <div key={bloodGroup} className="flex items-center justify-between">
                  <span className="text-gray-700 font-medium">{bloodGroup}</span>
                  <div className="flex items-center space-x-3">
                    <div className="w-24 bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-red-500 h-2 rounded-full" 
                        style={{ 
                          width: `${(count as number / Math.max(...Object.values(stats.bloodGroupDistribution))) * 100}%` 
                        }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-900 w-6">{count as number}</span>
                  </div>
                </div>
              ))}
            </div>
          </InfoCard>
        </motion.div>

        {/* BMI Distribution */}
        <motion.div variants={itemVariants}>
          <InfoCard title="BMI Distribution" icon={<ChartBarIcon className="w-5 h-5" />}>
            <div className="space-y-4">
              {stats?.bmiDistribution && Object.entries(stats.bmiDistribution).map(([category, count]) => {
                const colors = {
                  underweight: 'bg-blue-500',
                  normal: 'bg-green-500',
                  overweight: 'bg-yellow-500',
                  obese: 'bg-red-500'
                };
                const total = Object.values(stats.bmiDistribution).reduce((a: number, b: number) => a + b, 0);
                
                return (
                  <div key={category} className="flex items-center justify-between">
                    <span className="capitalize text-gray-700">{category}</span>
                    <div className="flex items-center space-x-3">
                      <div className="w-32 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${colors[category as keyof typeof colors]}`}
                          style={{ 
                            width: total > 0 ? `${((count as number) / total) * 100}%` : '0%'
                          }}
                        />
                      </div>
                      <span className="text-sm font-medium text-gray-900 w-8">{count as number}</span>
                    </div>
                  </div>
                );
              })}
            </div>
          </InfoCard>
        </motion.div>

        {/* Recent Activity */}
        <motion.div variants={itemVariants}>
          <InfoCard title="Recent Activity" icon={<CalendarIcon className="w-5 h-5" />}>
            <div className="space-y-4">
              {stats?.recentActivity?.length > 0 ? (
                stats.recentActivity.map((activity: any, index: number) => (
                  <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                    <div className="w-2 h-2 bg-blue-600 rounded-full mt-2" />
                    <div className="flex-1">
                      <p className="text-sm font-medium text-gray-900">{activity.action}</p>
                      <p className="text-xs text-gray-600">Patient: {activity.patient}</p>
                      <p className="text-xs text-gray-500">{activity.time}</p>
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">No recent activity</p>
              )}
            </div>
          </InfoCard>
        </motion.div>
      </div>

      {/* Summary */}
      <motion.div variants={itemVariants}>
        <div className="medical-card p-6 bg-gradient-to-r from-blue-50 to-teal-50 border border-blue-200">
          <h3 className="text-lg font-semibold text-blue-900 mb-2">Analytics Summary</h3>
          <p className="text-blue-700 text-sm">
            Your medical practice is serving <strong>{stats?.totalPatients || 0} patients</strong> with 
            an average age of <strong>{stats?.averageAge || 0} years</strong>. 
            This month, you've registered <strong>{stats?.newPatientsThisMonth || 0} new patients</strong>.
            {stats?.criticalCases > 0 && (
              <span className="text-red-700 font-medium">
                {' '}Please note: {stats.criticalCases} patients require immediate attention.
              </span>
            )}
          </p>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default AnalyticsPage;
