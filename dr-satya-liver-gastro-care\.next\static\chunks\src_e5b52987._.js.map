{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, Patient, Comorbidity, DashboardStats } from '@/types';\n\n// Authentication Store\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: { email: string; password: string }) => Promise<boolean>;\n  logout: () => void;\n  setUser: (user: User) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      \n      login: async (credentials) => {\n        set({ isLoading: true });\n        try {\n          const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(credentials),\n          });\n\n          const data = await response.json();\n\n          if (response.ok && data.success) {\n            set({ user: data.user, isAuthenticated: true, isLoading: false });\n            return true;\n          }\n\n          set({ isLoading: false });\n          return false;\n        } catch (error) {\n          console.error('Login error:', error);\n          set({ isLoading: false });\n          return false;\n        }\n      },\n      \n      logout: () => {\n        set({ user: null, isAuthenticated: false });\n      },\n      \n      setUser: (user) => {\n        set({ user, isAuthenticated: true });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n\n// Patients Store\ninterface PatientsState {\n  patients: Patient[];\n  currentPatient: Patient | null;\n  isLoading: boolean;\n  searchQuery: string;\n  filters: {\n    gender?: string;\n    bloodGroup?: string;\n    ageRange?: { min: number; max: number };\n  };\n  \n  // Actions\n  setPatients: (patients: Patient[]) => void;\n  addPatient: (patient: Patient) => void;\n  updatePatient: (id: string, updates: Partial<Patient>) => void;\n  deletePatient: (id: string) => void;\n  setCurrentPatient: (patient: Patient | null) => void;\n  setSearchQuery: (query: string) => void;\n  setFilters: (filters: any) => void;\n  setLoading: (loading: boolean) => void;\n  \n  // Computed\n  filteredPatients: () => Patient[];\n}\n\nexport const usePatientsStore = create<PatientsState>((set, get) => ({\n  patients: [],\n  currentPatient: null,\n  isLoading: false,\n  searchQuery: '',\n  filters: {},\n  \n  setPatients: (patients) => set({ patients }),\n  \n  addPatient: (patient) => \n    set((state) => ({ patients: [...state.patients, patient] })),\n  \n  updatePatient: (id, updates) =>\n    set((state) => ({\n      patients: state.patients.map((p) => \n        p.id === id ? { ...p, ...updates } : p\n      ),\n      currentPatient: state.currentPatient?.id === id \n        ? { ...state.currentPatient, ...updates } \n        : state.currentPatient,\n    })),\n  \n  deletePatient: (id) =>\n    set((state) => ({\n      patients: state.patients.filter((p) => p.id !== id),\n      currentPatient: state.currentPatient?.id === id ? null : state.currentPatient,\n    })),\n  \n  setCurrentPatient: (patient) => set({ currentPatient: patient }),\n  \n  setSearchQuery: (searchQuery) => set({ searchQuery }),\n  \n  setFilters: (filters) => set({ filters }),\n  \n  setLoading: (isLoading) => set({ isLoading }),\n  \n  filteredPatients: () => {\n    const { patients, searchQuery, filters } = get();\n    \n    return patients.filter((patient) => {\n      // Search query filter\n      if (searchQuery) {\n        const query = searchQuery.toLowerCase();\n        const matchesSearch = \n          patient.fullName.toLowerCase().includes(query) ||\n          patient.patientId.toLowerCase().includes(query) ||\n          patient.mobileNumber.includes(query) ||\n          patient.email?.toLowerCase().includes(query);\n        \n        if (!matchesSearch) return false;\n      }\n      \n      // Gender filter\n      if (filters.gender && patient.gender !== filters.gender) {\n        return false;\n      }\n      \n      // Blood group filter\n      if (filters.bloodGroup && patient.bloodGroup !== filters.bloodGroup) {\n        return false;\n      }\n      \n      // Age range filter\n      if (filters.ageRange) {\n        const age = new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear();\n        if (age < filters.ageRange.min || age > filters.ageRange.max) {\n          return false;\n        }\n      }\n      \n      return true;\n    });\n  },\n}));\n\n// Comorbidities Store\ninterface ComorbiditiesState {\n  comorbidities: Comorbidity[];\n  isLoading: boolean;\n  setComorbidities: (comorbidities: Comorbidity[]) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useComorbiditiesStore = create<ComorbiditiesState>((set) => ({\n  comorbidities: [],\n  isLoading: false,\n  \n  setComorbidities: (comorbidities) => set({ comorbidities }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// Dashboard Store\ninterface DashboardState {\n  stats: DashboardStats | null;\n  isLoading: boolean;\n  setStats: (stats: DashboardStats) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useDashboardStore = create<DashboardState>((set) => ({\n  stats: null,\n  isLoading: false,\n  \n  setStats: (stats) => set({ stats }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// UI Store for global UI state\ninterface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    message: string;\n    timestamp: number;\n  }>;\n  \n  toggleSidebar: () => void;\n  setTheme: (theme: 'light' | 'dark') => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n}\n\nexport const useUIStore = create<UIState>()(\n  persist(\n    (set, get) => ({\n      sidebarOpen: true,\n      theme: 'light',\n      notifications: [],\n      \n      toggleSidebar: () => \n        set((state) => ({ sidebarOpen: !state.sidebarOpen })),\n      \n      setTheme: (theme) => set({ theme }),\n      \n      addNotification: (notification) => {\n        const id = Math.random().toString(36).substr(2, 9);\n        const timestamp = Date.now();\n        \n        set((state) => ({\n          notifications: [...state.notifications, { ...notification, id, timestamp }],\n        }));\n        \n        // Auto-remove after 5 seconds\n        setTimeout(() => {\n          set((state) => ({\n            notifications: state.notifications.filter((n) => n.id !== id),\n          }));\n        }, 5000);\n      },\n      \n      removeNotification: (id) =>\n        set((state) => ({\n          notifications: state.notifications.filter((n) => n.id !== id),\n        })),\n    }),\n    {\n      name: 'ui-storage',\n      partialize: (state) => ({ \n        sidebarOpen: state.sidebarOpen, \n        theme: state.theme \n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAaO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;oBAC/B,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,iBAAiB;wBAAM,WAAW;oBAAM;oBAC/D,OAAO;gBACT;gBAEA,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT;QACF;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;YAAM;QAC3C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB;YAAK;QACpC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AA8BG,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACnE,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,aAAa;QACb,SAAS,CAAC;QAEV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAE5D,eAAe,CAAC,IAAI,UAClB,IAAI,CAAC;oBAIa;uBAJF;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,IAC5B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;oBAEvC,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;oBAAC,IACtC,MAAM,cAAc;gBAC1B;;QAEF,eAAe,CAAC,KACd,IAAI,CAAC;oBAEa;uBAFF;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAChD,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KAAK,OAAO,MAAM,cAAc;gBAC/E;;QAEF,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAE9D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QAEvC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,kBAAkB;YAChB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YAE3C,OAAO,SAAS,MAAM,CAAC,CAAC;gBACtB,sBAAsB;gBACtB,IAAI,aAAa;wBAMb;oBALF,MAAM,QAAQ,YAAY,WAAW;oBACrC,MAAM,gBACJ,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UACxC,QAAQ,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACzC,QAAQ,YAAY,CAAC,QAAQ,CAAC,YAC9B,iBAAA,QAAQ,KAAK,cAAb,qCAAA,eAAe,WAAW,GAAG,QAAQ,CAAC;oBAExC,IAAI,CAAC,eAAe,OAAO;gBAC7B;gBAEA,gBAAgB;gBAChB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACvD,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,QAAQ,UAAU,EAAE;oBACnE,OAAO;gBACT;gBAEA,mBAAmB;gBACnB,IAAI,QAAQ,QAAQ,EAAE;oBACpB,MAAM,MAAM,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,QAAQ,WAAW,EAAE,WAAW;oBAChF,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,EAAE;wBAC5D,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT;QACF;IACF,CAAC;AAUM,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,MAAQ,CAAC;QACxE,eAAe,EAAE;QACjB,WAAW;QAEX,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAUM,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QAChE,OAAO;QACP,WAAW;QAEX,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAmBM,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,aAAa;QACb,OAAO;QACP,eAAe,EAAE;QAEjB,eAAe,IACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QAErD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,iBAAiB,CAAC;YAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChD,MAAM,YAAY,KAAK,GAAG;YAE1B,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;2BAAI,MAAM,aAAa;wBAAE;4BAAE,GAAG,YAAY;4BAAE;4BAAI;wBAAU;qBAAE;gBAC7E,CAAC;YAED,8BAA8B;YAC9B,WAAW;gBACT,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC5D,CAAC;YACH,GAAG;QACL;QAEA,oBAAoB,CAAC,KACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC5D,CAAC;IACL,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 219, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showText?: boolean;\n  className?: string;\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  showText = true, \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-8 h-8',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16',\n    xl: 'w-24 h-24'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-lg',\n    lg: 'text-xl',\n    xl: 'text-3xl'\n  };\n\n  return (\n    <motion.div \n      className={`flex items-center gap-3 ${className}`}\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.5, ease: \"easeOut\" }}\n    >\n      {/* Medical Logo Icon */}\n      <motion.div \n        className={`${sizeClasses[size]} relative`}\n        whileHover={{ scale: 1.05 }}\n        transition={{ type: \"spring\", stiffness: 300 }}\n      >\n        <svg\n          viewBox=\"0 0 100 100\"\n          className=\"w-full h-full\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          {/* Background Circle with Gradient */}\n          <defs>\n            <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#1e40af\" />\n              <stop offset=\"50%\" stopColor=\"#3b82f6\" />\n              <stop offset=\"100%\" stopColor=\"#0d9488\" />\n            </linearGradient>\n            <linearGradient id=\"liverGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#059669\" />\n              <stop offset=\"100%\" stopColor=\"#0d9488\" />\n            </linearGradient>\n          </defs>\n          \n          {/* Main Circle Background */}\n          <circle\n            cx=\"50\"\n            cy=\"50\"\n            r=\"48\"\n            fill=\"url(#logoGradient)\"\n            stroke=\"#ffffff\"\n            strokeWidth=\"2\"\n          />\n          \n          {/* Medical Cross */}\n          <g transform=\"translate(50, 50)\">\n            {/* Vertical bar of cross */}\n            <rect\n              x=\"-3\"\n              y=\"-20\"\n              width=\"6\"\n              height=\"40\"\n              fill=\"#ffffff\"\n              rx=\"3\"\n            />\n            {/* Horizontal bar of cross */}\n            <rect\n              x=\"-20\"\n              y=\"-3\"\n              width=\"40\"\n              height=\"6\"\n              fill=\"#ffffff\"\n              rx=\"3\"\n            />\n          </g>\n          \n          {/* Liver Shape (stylized) */}\n          <g transform=\"translate(50, 50)\">\n            <path\n              d=\"M -15 -8 Q -20 -12 -15 -16 Q -10 -18 -5 -16 Q 0 -14 5 -16 Q 10 -18 15 -16 Q 20 -12 15 -8 Q 12 -4 8 0 Q 5 4 0 6 Q -5 4 -8 0 Q -12 -4 -15 -8 Z\"\n              fill=\"url(#liverGradient)\"\n              opacity=\"0.3\"\n            />\n          </g>\n          \n          {/* Stethoscope accent */}\n          <g transform=\"translate(50, 50)\">\n            <circle cx=\"18\" cy=\"18\" r=\"4\" fill=\"#ffffff\" opacity=\"0.8\" />\n            <path\n              d=\"M 14 14 Q 10 10 5 12 Q 0 14 -5 12 Q -10 10 -14 14\"\n              stroke=\"#ffffff\"\n              strokeWidth=\"2\"\n              fill=\"none\"\n              opacity=\"0.8\"\n            />\n          </g>\n        </svg>\n      </motion.div>\n\n      {/* Text Logo */}\n      {showText && (\n        <motion.div \n          className=\"flex flex-col\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n        >\n          <motion.h1 \n            className={`font-bold text-gray-800 leading-tight ${textSizeClasses[size]}`}\n            whileHover={{ scale: 1.02 }}\n          >\n            Dr Satya's\n          </motion.h1>\n          <motion.p \n            className={`font-medium text-blue-600 leading-tight ${\n              size === 'sm' ? 'text-xs' : \n              size === 'md' ? 'text-sm' : \n              size === 'lg' ? 'text-base' : 'text-lg'\n            }`}\n            whileHover={{ scale: 1.02 }}\n          >\n            Liver & Gastro Care\n          </motion.p>\n          {size === 'lg' || size === 'xl' ? (\n            <motion.p \n              className=\"text-xs text-gray-500 mt-1\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.4 }}\n            >\n              Excellence in Digestive Health\n            </motion.p>\n          ) : null}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,OAA4B;QAAC,EACjC,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,2BAAoC,OAAV;QACtC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;;0BAG7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,GAAoB,OAAlB,WAAW,CAAC,KAAK,EAAC;gBAChC,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,YAAY;oBAAE,MAAM;oBAAU,WAAW;gBAAI;0BAE7C,cAAA,6LAAC;oBACC,SAAQ;oBACR,WAAU;oBACV,OAAM;;sCAGN,6LAAC;;8CACC,6LAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,6LAAC;oCAAe,IAAG;oCAAgB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC9D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;sCAKlC,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;;;;;;sCAId,6LAAC;4BAAE,WAAU;;8CAEX,6LAAC;oCACC,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;oCACP,MAAK;oCACL,IAAG;;;;;;8CAGL,6LAAC;oCACC,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;oCACP,MAAK;oCACL,IAAG;;;;;;;;;;;;sCAKP,6LAAC;4BAAE,WAAU;sCACX,cAAA,6LAAC;gCACC,GAAE;gCACF,MAAK;gCACL,SAAQ;;;;;;;;;;;sCAKZ,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAI,MAAK;oCAAU,SAAQ;;;;;;8CACrD,6LAAC;oCACC,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,SAAQ;;;;;;;;;;;;;;;;;;;;;;;YAOf,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,yCAA8D,OAAtB,eAAe,CAAC,KAAK;wBACzE,YAAY;4BAAE,OAAO;wBAAK;kCAC3B;;;;;;kCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAW,AAAC,2CAIX,OAHC,SAAS,OAAO,YAChB,SAAS,OAAO,YAChB,SAAS,OAAO,cAAc;wBAEhC,YAAY;4BAAE,OAAO;wBAAK;kCAC3B;;;;;;oBAGA,SAAS,QAAQ,SAAS,qBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAC1B;;;;;mEAGC;;;;;;;;;;;;;AAKd;KA/IM;uCAiJS", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n  fullScreen?: boolean;\n  className?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  size = 'md', \n  text = 'Loading...', \n  fullScreen = false,\n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-10 h-10',\n    lg: 'w-16 h-16'\n  };\n\n  const LoadingSpinner = () => (\n    <div className=\"relative\">\n      {/* Outer ring */}\n      <motion.div\n        className={`${sizeClasses[size]} border-4 border-blue-100 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Inner spinning ring */}\n      <motion.div\n        className={`absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-blue-600 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Medical cross in center */}\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <div className=\"w-3 h-3 relative\">\n          <div className=\"absolute inset-x-1/2 inset-y-0 w-0.5 bg-blue-600 transform -translate-x-1/2\" />\n          <div className=\"absolute inset-y-1/2 inset-x-0 h-0.5 bg-blue-600 transform -translate-y-1/2\" />\n        </div>\n      </div>\n    </div>\n  );\n\n  const LoadingDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-2 h-2 bg-blue-600 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const content = (\n    <motion.div\n      className={`flex flex-col items-center justify-center gap-4 ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <LoadingSpinner />\n      \n      {text && (\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <p className=\"text-gray-600 font-medium\">{text}</p>\n          <LoadingDots />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n\n  if (fullScreen) {\n    return (\n      <motion.div\n        className=\"fixed inset-0 bg-white bg-opacity-90 backdrop-blur-sm z-50 flex items-center justify-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n      >\n        {content}\n      </motion.div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton Loading Component\ninterface SkeletonProps {\n  className?: string;\n  lines?: number;\n  avatar?: boolean;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({ \n  className = '', \n  lines = 3, \n  avatar = false \n}) => {\n  return (\n    <div className={`animate-pulse ${className}`}>\n      <div className=\"flex items-start space-x-4\">\n        {avatar && (\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n        )}\n        <div className=\"flex-1 space-y-2\">\n          {Array.from({ length: lines }).map((_, index) => (\n            <div\n              key={index}\n              className={`h-4 bg-gray-200 rounded ${\n                index === lines - 1 ? 'w-3/4' : 'w-full'\n              }`}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Card Skeleton\nexport const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {\n  return (\n    <div className={`medical-card p-6 ${className}`}>\n      <div className=\"animate-pulse\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n          <div className=\"flex-1\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\" />\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"h-3 bg-gray-200 rounded\" />\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n          <div className=\"h-3 bg-gray-200 rounded w-4/6\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Button Loading State\ninterface ButtonLoadingProps {\n  loading?: boolean;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n}\n\nexport const ButtonLoading: React.FC<ButtonLoadingProps> = ({\n  loading = false,\n  children,\n  className = '',\n  onClick,\n  disabled = false\n}) => {\n  return (\n    <button\n      className={`btn-primary relative ${className} ${\n        loading || disabled ? 'opacity-70 cursor-not-allowed' : ''\n      }`}\n      onClick={onClick}\n      disabled={loading || disabled}\n    >\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      <span className={loading ? 'opacity-0' : 'opacity-100'}>\n        {children}\n      </span>\n    </button>\n  );\n};\n\nexport default Loading;\n"], "names": [], "mappings": ";;;;;;;AAGA;AAHA;;;AAYA,MAAM,UAAkC;QAAC,EACvC,OAAO,IAAI,EACX,OAAO,YAAY,EACnB,aAAa,KAAK,EAClB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,GAAoB,OAAlB,WAAW,CAAC,KAAK,EAAC;oBAChC,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,oBAAqC,OAAlB,WAAW,CAAC,KAAK,EAAC;oBACjD,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,cAAc,kBAClB,6LAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,QAAQ;oBACjB;mBAVK;;;;;;;;;;IAgBb,MAAM,wBACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,mDAA4D,OAAV;QAC9D,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;;;;;YAEA,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;;;;;;;;;;;;;;;;;IAMT,IAAI,YAAY;QACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;sBAElB;;;;;;IAGP;IAEA,OAAO;AACT;KA/FM;AAwGC,MAAM,WAAoC;QAAC,EAChD,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,SAAS,KAAK,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,iBAA0B,OAAV;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;gBACZ,wBACC,6LAAC;oBAAI,WAAU;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;4BAEC,WAAW,AAAC,2BAEX,OADC,UAAU,QAAQ,IAAI,UAAU;2BAF7B;;;;;;;;;;;;;;;;;;;;;AAUnB;MAxBa;AA2BN,MAAM,eAAiD;QAAC,EAAE,YAAY,EAAE,EAAE;IAC/E,qBACE,6LAAC;QAAI,WAAW,AAAC,oBAA6B,OAAV;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAGnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;MAnBa;AA8BN,MAAM,gBAA8C;QAAC,EAC1D,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EACjB;IACC,qBACE,6LAAC;QACC,WAAW,AAAC,wBACV,OADiC,WAAU,KAE5C,OADC,WAAW,WAAW,kCAAkC;QAE1D,SAAS;QACT,UAAU,WAAW;;YAEpB,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGnB,6LAAC;gBAAK,WAAW,UAAU,cAAc;0BACtC;;;;;;;;;;;;AAIT;MAzBa;uCA2BE", "debugId": null}}, {"offset": {"line": 913, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/auth/LoginPage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { EyeIcon, EyeSlashIcon, LockClosedIcon, UserIcon } from '@heroicons/react/24/outline';\nimport { toast } from 'react-hot-toast';\nimport Logo from '../ui/Logo';\nimport { ButtonLoading } from '../ui/Loading';\nimport { useAuthStore } from '@/store';\n\n// Validation schema\nconst loginSchema = z.object({\n  email: z.string().email('Please enter a valid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nconst LoginPage: React.FC = () => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { login } = useAuthStore();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    try {\n      // Simulate API call - replace with actual authentication\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      const success = await login(data);\n      if (success) {\n        toast.success('Welcome back!');\n        // Navigation will be handled by the parent component\n      } else {\n        toast.error('Invalid credentials. Please try again.');\n      }\n    } catch (error) {\n      toast.error('Login failed. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 via-white to-teal-50 flex items-center justify-center p-4\">\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <div className=\"absolute -top-40 -right-40 w-80 h-80 bg-blue-100 rounded-full opacity-20 blur-3xl\" />\n        <div className=\"absolute -bottom-40 -left-40 w-80 h-80 bg-teal-100 rounded-full opacity-20 blur-3xl\" />\n        <div className=\"absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-blue-50 rounded-full opacity-30 blur-3xl\" />\n      </div>\n\n      <motion.div\n        className=\"relative w-full max-w-md\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        {/* Login Card */}\n        <motion.div\n          className=\"medical-card p-8 backdrop-blur-sm bg-white/90\"\n          variants={itemVariants}\n          whileHover={{ y: -2 }}\n        >\n          {/* Logo and Header */}\n          <motion.div className=\"text-center mb-8\" variants={itemVariants}>\n            <div className=\"flex justify-center mb-6\">\n              <Logo size=\"lg\" showText />\n            </div>\n            <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              Welcome Back\n            </h1>\n            <p className=\"text-gray-600\">\n              Sign in to access your medical dashboard\n            </p>\n          </motion.div>\n\n          {/* Login Form */}\n          <motion.form onSubmit={handleSubmit(onSubmit)} variants={itemVariants}>\n            <div className=\"space-y-6\">\n              {/* Email Field */}\n              <div>\n                <label className=\"form-label\">Email Address</label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <UserIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    {...register('email')}\n                    type=\"email\"\n                    className={`form-input pl-10 ${errors.email ? 'error' : ''}`}\n                    placeholder=\"<EMAIL>\"\n                    autoComplete=\"email\"\n                  />\n                </div>\n                {errors.email && (\n                  <motion.p\n                    className=\"mt-1 text-sm text-red-600\"\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                  >\n                    {errors.email.message}\n                  </motion.p>\n                )}\n              </div>\n\n              {/* Password Field */}\n              <div>\n                <label className=\"form-label\">Password</label>\n                <div className=\"relative\">\n                  <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                    <LockClosedIcon className=\"h-5 w-5 text-gray-400\" />\n                  </div>\n                  <input\n                    {...register('password')}\n                    type={showPassword ? 'text' : 'password'}\n                    className={`form-input pl-10 pr-10 ${errors.password ? 'error' : ''}`}\n                    placeholder=\"Enter your password\"\n                    autoComplete=\"current-password\"\n                  />\n                  <button\n                    type=\"button\"\n                    className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n                    onClick={() => setShowPassword(!showPassword)}\n                  >\n                    {showPassword ? (\n                      <EyeSlashIcon className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                    ) : (\n                      <EyeIcon className=\"h-5 w-5 text-gray-400 hover:text-gray-600\" />\n                    )}\n                  </button>\n                </div>\n                {errors.password && (\n                  <motion.p\n                    className=\"mt-1 text-sm text-red-600\"\n                    initial={{ opacity: 0, y: -10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                  >\n                    {errors.password.message}\n                  </motion.p>\n                )}\n              </div>\n\n              {/* Remember Me & Forgot Password */}\n              <div className=\"flex items-center justify-between\">\n                <label className=\"flex items-center\">\n                  <input\n                    type=\"checkbox\"\n                    className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded\"\n                  />\n                  <span className=\"ml-2 text-sm text-gray-600\">Remember me</span>\n                </label>\n                <a\n                  href=\"#\"\n                  className=\"text-sm text-blue-600 hover:text-blue-500 font-medium\"\n                >\n                  Forgot password?\n                </a>\n              </div>\n\n              {/* Submit Button */}\n              <ButtonLoading\n                loading={isLoading}\n                className=\"w-full\"\n                disabled={isLoading}\n              >\n                {isLoading ? 'Signing in...' : 'Sign In'}\n              </ButtonLoading>\n            </div>\n          </motion.form>\n\n          {/* Demo Credentials */}\n          <motion.div\n            className=\"mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200\"\n            variants={itemVariants}\n          >\n            <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials</h3>\n            <div className=\"text-xs text-blue-700 space-y-1\">\n              <p><strong>Email:</strong> <EMAIL></p>\n              <p><strong>Password:</strong> demo123</p>\n            </div>\n          </motion.div>\n        </motion.div>\n\n        {/* Footer */}\n        <motion.div\n          className=\"text-center mt-6\"\n          variants={itemVariants}\n        >\n          <p className=\"text-sm text-gray-500\">\n            © 2024 Dr Satya's Liver & Gastro Care. All rights reserved.\n          </p>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;;;AAXA;;;;;;;;;;;AAaA,oBAAoB;AACpB,MAAM,cAAc,gLAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,gLAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIA,MAAM,YAAsB;;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,iKAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,yDAAyD;YACzD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAU,MAAM,MAAM;YAC5B,IAAI,SAAS;gBACX,0JAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,qDAAqD;YACvD,OAAO;gBACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;0BAGjB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;kCAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;wBACV,YAAY;4BAAE,GAAG,CAAC;wBAAE;;0CAGpB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCAAC,WAAU;gCAAmB,UAAU;;kDACjD,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,mIAAA,CAAA,UAAI;4CAAC,MAAK;4CAAK,QAAQ;;;;;;;;;;;kDAE1B,6LAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,6LAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;0CAM/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gCAAC,UAAU,aAAa;gCAAW,UAAU;0CACvD,cAAA,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAa;;;;;;8DAC9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,kNAAA,CAAA,WAAQ;gEAAC,WAAU;;;;;;;;;;;sEAEtB,6LAAC;4DACE,GAAG,SAAS,QAAQ;4DACrB,MAAK;4DACL,WAAW,AAAC,oBAA+C,OAA5B,OAAO,KAAK,GAAG,UAAU;4DACxD,aAAY;4DACZ,cAAa;;;;;;;;;;;;gDAGhB,OAAO,KAAK,kBACX,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;8DAE3B,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sDAM3B,6LAAC;;8DACC,6LAAC;oDAAM,WAAU;8DAAa;;;;;;8DAC9B,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACb,cAAA,6LAAC,8NAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;sEAE5B,6LAAC;4DACE,GAAG,SAAS,WAAW;4DACxB,MAAM,eAAe,SAAS;4DAC9B,WAAW,AAAC,0BAAwD,OAA/B,OAAO,QAAQ,GAAG,UAAU;4DACjE,aAAY;4DACZ,cAAa;;;;;;sEAEf,6LAAC;4DACC,MAAK;4DACL,WAAU;4DACV,SAAS,IAAM,gBAAgB,CAAC;sEAE/B,6BACC,6LAAC,0NAAA,CAAA,eAAY;gEAAC,WAAU;;;;;yHAExB,6LAAC,gNAAA,CAAA,UAAO;gEAAC,WAAU;;;;;;;;;;;;;;;;;gDAIxB,OAAO,QAAQ,kBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG,CAAC;oDAAG;oDAC9B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;8DAE3B,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;sDAM9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAM,WAAU;;sEACf,6LAAC;4DACC,MAAK;4DACL,WAAU;;;;;;sEAEZ,6LAAC;4DAAK,WAAU;sEAA6B;;;;;;;;;;;;8DAE/C,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;sDAMH,6LAAC,sIAAA,CAAA,gBAAa;4CACZ,SAAS;4CACT,WAAU;4CACV,UAAU;sDAET,YAAY,kBAAkB;;;;;;;;;;;;;;;;;0CAMrC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,UAAU;;kDAEV,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAe;;;;;;;0DAC1B,6LAAC;;kEAAE,6LAAC;kEAAO;;;;;;oDAAkB;;;;;;;;;;;;;;;;;;;;;;;;;kCAMnC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;kCAEV,cAAA,6LAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;;;;;;;AAO/C;GA3MM;;QAGc,wHAAA,CAAA,eAAY;QAM1B,iKAAA,CAAA,UAAO;;;KATP;uCA6MS", "debugId": null}}, {"offset": {"line": 1422, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store';\nimport LoginPage from '@/components/auth/LoginPage';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated } = useAuthStore();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  if (isAuthenticated) {\n    return null; // Will redirect to dashboard\n  }\n\n  return <LoginPage />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,IAAI,iBAAiB;gBACnB,OAAO,IAAI,CAAC;YACd;QACF;yBAAG;QAAC;QAAiB;KAAO;IAE5B,IAAI,iBAAiB;QACnB,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBAAO,6LAAC,0IAAA,CAAA,UAAS;;;;;AACnB;GAfwB;;QACP,qIAAA,CAAA,YAAS;QACI,wHAAA,CAAA,eAAY;;;KAFlB", "debugId": null}}]}