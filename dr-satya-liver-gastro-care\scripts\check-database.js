const { Pool } = require('pg');
const bcrypt = require('bcryptjs');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function checkDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🔗 Connected to NeonDB');
    
    // Check existing tables
    const tables = await client.query(`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public' 
      ORDER BY table_name;
    `);
    
    console.log('📊 Existing tables:');
    tables.rows.forEach(row => {
      console.log(`  - ${row.table_name}`);
    });
    
    // Check if test user exists
    const userCheck = await client.query(`
      SELECT id, email, full_name, role 
      FROM users 
      WHERE email = $1;
    `, ['<EMAIL>']);
    
    if (userCheck.rows.length === 0) {
      // Create test user with hashed password
      const hashedPassword = await bcrypt.hash('demo123', 10);
      
      const newUser = await client.query(`
        INSERT INTO users (email, password_hash, full_name, role) 
        VALUES ($1, $2, $3, $4) 
        RETURNING id, email, full_name, role;
      `, [
        '<EMAIL>',
        hashedPassword,
        'Dr. Sarah Johnson',
        'doctor'
      ]);
      
      console.log('👤 Test user created:', newUser.rows[0]);
    } else {
      console.log('👤 Test user exists:', userCheck.rows[0]);
    }
    
    // Check comorbidities
    const comorbiditiesCount = await client.query('SELECT COUNT(*) FROM comorbidities;');
    console.log(`🏥 Comorbidities in database: ${comorbiditiesCount.rows[0].count}`);
    
    // Check patients
    const patientsCount = await client.query('SELECT COUNT(*) FROM patients;');
    console.log(`👥 Patients in database: ${patientsCount.rows[0].count}`);
    
    // Insert sample patients if none exist
    if (parseInt(patientsCount.rows[0].count) === 0) {
      console.log('📝 Creating sample patients...');
      
      const samplePatients = [
        {
          fullName: 'John Doe',
          dateOfBirth: '1978-05-15',
          gender: 'Male',
          aadharNumber: '123456789012',
          mobileNumber: '**********',
          email: '<EMAIL>',
          address: '123 Main Street, Mumbai, Maharashtra 400001',
          heightCm: 175,
          weightKg: 75,
          bloodGroup: 'O+',
          charlsonIndex: 2,
          asaGrade: 2,
          ecogGrade: 0
        },
        {
          fullName: 'Sarah Smith',
          dateOfBirth: '1992-08-22',
          gender: 'Female',
          aadharNumber: '123456789013',
          mobileNumber: '**********',
          email: '<EMAIL>',
          address: '456 Park Avenue, Delhi, Delhi 110001',
          heightCm: 165,
          weightKg: 60,
          bloodGroup: 'A+',
          charlsonIndex: 0,
          asaGrade: 1,
          ecogGrade: 0
        },
        {
          fullName: 'Mike Johnson',
          dateOfBirth: '1965-12-10',
          gender: 'Male',
          aadharNumber: '123456789014',
          mobileNumber: '**********',
          email: '<EMAIL>',
          address: '789 Oak Street, Bangalore, Karnataka 560001',
          heightCm: 180,
          weightKg: 85,
          bloodGroup: 'B+',
          charlsonIndex: 4,
          asaGrade: 3,
          ecogGrade: 1
        }
      ];
      
      const userId = userCheck.rows[0]?.id || (await client.query('SELECT id FROM users LIMIT 1')).rows[0].id;
      
      for (const patient of samplePatients) {
        const result = await client.query(`
          INSERT INTO patients (
            full_name, date_of_birth, gender, aadhar_number, mobile_number, 
            email, address, height_cm, weight_kg, blood_group, 
            charlson_index, asa_grade, ecog_grade, created_by
          ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14)
          RETURNING patient_id, full_name;
        `, [
          patient.fullName, patient.dateOfBirth, patient.gender, patient.aadharNumber,
          patient.mobileNumber, patient.email, patient.address, patient.heightCm,
          patient.weightKg, patient.bloodGroup, patient.charlsonIndex, patient.asaGrade,
          patient.ecogGrade, userId
        ]);
        
        console.log(`  ✅ Created patient: ${result.rows[0].full_name} (${result.rows[0].patient_id})`);
      }
    }
    
    // Final counts
    const finalPatients = await client.query('SELECT COUNT(*) FROM patients;');
    const finalComorbidities = await client.query('SELECT COUNT(*) FROM comorbidities;');
    
    console.log('\n🎉 Database Status:');
    console.log(`  👥 Total Patients: ${finalPatients.rows[0].count}`);
    console.log(`  🏥 Total Comorbidities: ${finalComorbidities.rows[0].count}`);
    console.log(`  👤 Total Users: ${(await client.query('SELECT COUNT(*) FROM users;')).rows[0].count}`);
    
  } catch (error) {
    console.error('❌ Error checking database:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the check
checkDatabase().catch(console.error);
