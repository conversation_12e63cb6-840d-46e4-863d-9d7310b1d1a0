'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  HomeIcon, 
  UserGroupIcon, 
  UserPlusIcon, 
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import Logo from '../ui/Logo';
import { useUIStore, useAuthStore } from '@/store';

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {
  const pathname = usePathname();
  const { sidebarOpen, toggleSidebar } = useUIStore();
  const { user, logout } = useAuthStore();

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      description: 'Overview and statistics'
    },
    {
      name: 'Patients',
      href: '/patients',
      icon: UserGroupIcon,
      description: 'View all patients'
    },
    {
      name: 'Add Patient',
      href: '/patients/new',
      icon: UserPlusIcon,
      description: 'Register new patient'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartBarIcon,
      description: 'Medical reports & insights'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      description: 'Application settings'
    }
  ];

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const itemVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      opacity: 0,
      x: -20
    }
  };

  const handleLogout = () => {
    logout();
    // Redirect to login page
    window.location.href = '/login';
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Premium Sidebar */}
      <motion.aside
        className={`fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-white via-blue-50/30 to-teal-50/30 backdrop-blur-xl border-r border-white/20 z-50 lg:relative lg:translate-x-0 ${className}`}
        variants={sidebarVariants}
        animate={sidebarOpen ? "open" : "closed"}
        initial="closed"
        style={{
          boxShadow: '0 0 50px rgba(0, 102, 204, 0.1), inset 0 1px 0 rgba(255, 255, 255, 0.6)'
        }}
      >
        {/* Sidebar Background Effects */}
        <div className="absolute inset-0 overflow-hidden">
          {/* Animated Background Pattern */}
          <div className="absolute inset-0 opacity-5">
            <svg className="w-full h-full" viewBox="0 0 100 100">
              <defs>
                <pattern id="sidebar-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                  <circle cx="10" cy="10" r="1" fill="url(#premiumGradient)" />
                </pattern>
              </defs>
              <rect width="100" height="100" fill="url(#sidebar-pattern)" />
            </svg>
          </div>

          {/* Floating Elements */}
          {[...Array(3)].map((_, i) => (
            <motion.div
              key={i}
              className="absolute w-32 h-32 rounded-full opacity-5"
              style={{
                background: 'linear-gradient(135deg, #0066cc, #00a693)',
                left: `${-20 + i * 30}%`,
                top: `${20 + i * 25}%`,
              }}
              animate={{
                y: [-10, 10, -10],
                x: [-5, 5, -5],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: 8 + i * 2,
                repeat: Infinity,
                ease: "easeInOut",
              }}
            />
          ))}
        </div>

        <div className="flex flex-col h-full relative z-10">
          {/* Premium Header */}
          <motion.div
            className="flex items-center justify-between p-6 border-b border-white/20 bg-white/40 backdrop-blur-sm"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <Logo size="lg" showText variant="premium" />
            <motion.button
              onClick={toggleSidebar}
              className="p-2 rounded-xl hover:bg-white/60 lg:hidden transition-all duration-200 backdrop-blur-sm"
              whileHover={{ scale: 1.1, rotate: 90 }}
              whileTap={{ scale: 0.9 }}
            >
              <XMarkIcon className="w-6 h-6 text-gray-600" />
            </motion.button>
          </motion.div>

          {/* Premium User Info */}
          {user && (
            <motion.div
              className="p-6 border-b border-white/20 bg-white/30 backdrop-blur-sm mx-4 my-4 rounded-2xl"
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <div className="flex items-center space-x-4">
                <motion.div
                  className="relative"
                  whileHover={{ scale: 1.1 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="w-14 h-14 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg">
                    {user?.firstName?.[0]?.toUpperCase() || 'D'}{user?.lastName?.[0]?.toUpperCase() || 'R'}
                  </div>
                  {/* Online Status Indicator */}
                  <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg">
                    <div className="w-full h-full bg-green-400 rounded-full animate-ping"></div>
                  </div>
                </motion.div>
                <div className="flex-1">
                  <motion.p
                    className="font-bold text-gray-800 text-lg"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.1 }}
                  >
                    {user?.firstName || 'Doctor'} {user?.lastName || ''}
                  </motion.p>
                  <motion.div
                    className="flex items-center gap-2 mt-1"
                    initial={{ opacity: 0, x: -10 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.2 }}
                  >
                    <span className="px-2 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-semibold rounded-full capitalize">
                      {user?.role || 'doctor'}
                    </span>
                    <div className="flex items-center gap-1">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-xs text-gray-600 font-medium">Online</span>
                    </div>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          )}

          {/* Premium Navigation */}
          <nav className="flex-1 p-6">
            <motion.div
              className="mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              <h3 className="text-xs font-bold text-gray-500 uppercase tracking-wider mb-3 px-4">
                Navigation
              </h3>
            </motion.div>

            <motion.ul className="space-y-3">
              {navigationItems.map((item, index) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;

                return (
                  <motion.li
                    key={item.name}
                    variants={itemVariants}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ x: 4 }}
                  >
                    <Link
                      href={item.href}
                      className={`group relative flex items-center px-4 py-4 rounded-2xl transition-all duration-300 overflow-hidden ${
                        isActive
                          ? 'bg-gradient-to-r from-blue-500/20 via-blue-600/20 to-teal-500/20 text-blue-700 shadow-lg backdrop-blur-sm border border-blue-200/50'
                          : 'text-gray-700 hover:bg-white/60 hover:text-blue-600 hover:shadow-md backdrop-blur-sm'
                      }`}
                    >
                      {/* Active Background Effect */}
                      {isActive && (
                        <motion.div
                          className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-teal-500/10"
                          layoutId="activeBackground"
                          transition={{ type: "spring", stiffness: 300, damping: 30 }}
                        />
                      )}

                      {/* Icon Container */}
                      <motion.div
                        className={`relative z-10 p-2 rounded-xl mr-4 transition-all duration-300 ${
                          isActive
                            ? 'bg-gradient-to-r from-blue-500 to-teal-500 text-white shadow-lg'
                            : 'bg-gray-100 text-gray-400 group-hover:bg-blue-100 group-hover:text-blue-600'
                        }`}
                        whileHover={{ scale: 1.1, rotate: 5 }}
                        transition={{ duration: 0.2 }}
                      >
                        <Icon className="w-5 h-5" />
                      </motion.div>

                      {/* Content */}
                      <div className="flex-1 relative z-10">
                        <motion.p
                          className={`font-semibold text-sm ${isActive ? 'text-blue-700' : 'group-hover:text-blue-600'}`}
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 }}
                        >
                          {item.name}
                        </motion.p>
                        <motion.p
                          className="text-xs text-gray-500 mt-0.5"
                          initial={{ opacity: 0, y: 5 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.05 + 0.1 }}
                        >
                          {item.description}
                        </motion.p>
                      </div>

                      {/* Active Indicator */}
                      {isActive && (
                        <motion.div
                          className="relative z-10 w-3 h-3 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full shadow-lg"
                          layoutId="activeIndicator"
                          transition={{ type: "spring", stiffness: 300, damping: 30 }}
                        >
                          <div className="absolute inset-0 bg-gradient-to-r from-blue-400 to-teal-400 rounded-full animate-ping opacity-75"></div>
                        </motion.div>
                      )}

                      {/* Hover Arrow */}
                      <motion.div
                        className={`relative z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 ${
                          isActive ? 'hidden' : ''
                        }`}
                      >
                        <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                        </svg>
                      </motion.div>
                    </Link>
                  </motion.li>
                );
              })}
            </motion.ul>
          </nav>

          {/* Premium Footer */}
          <motion.div
            className="p-6 border-t border-white/20 bg-white/30 backdrop-blur-sm mx-4 mb-4 rounded-2xl"
            variants={itemVariants}
          >
            <motion.button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-4 text-gray-700 hover:bg-gradient-to-r hover:from-red-50 hover:to-pink-50 hover:text-red-600 rounded-2xl transition-all duration-300 group relative overflow-hidden"
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              {/* Hover Background Effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-red-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                initial={false}
              />

              <motion.div
                className="relative z-10 p-2 rounded-xl mr-4 bg-gray-100 group-hover:bg-red-100 transition-all duration-300"
                whileHover={{ scale: 1.1, rotate: -5 }}
              >
                <ArrowRightOnRectangleIcon className="w-5 h-5 text-gray-400 group-hover:text-red-600 transition-colors duration-300" />
              </motion.div>

              <span className="font-semibold relative z-10">Sign Out</span>

              {/* Logout Arrow */}
              <motion.div
                className="ml-auto relative z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                initial={{ x: -10 }}
                whileHover={{ x: 0 }}
              >
                <svg className="w-4 h-4 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </motion.div>
            </motion.button>

            {/* App Info */}
            <motion.div
              className="mt-6 text-center space-y-2"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
            >
              <div className="flex items-center justify-center gap-2">
                <div className="w-6 h-6 bg-gradient-to-r from-blue-500 to-teal-500 rounded-lg flex items-center justify-center">
                  <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                </div>
                <p className="text-xs font-bold bg-gradient-to-r from-blue-600 to-teal-600 bg-clip-text text-transparent">
                  Dr Satya's Medical Suite
                </p>
              </div>

              <div className="flex items-center justify-center gap-4 text-xs text-gray-500">
                <span className="flex items-center gap-1">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  v1.0.0
                </span>
                <span>•</span>
                <span>Premium Edition</span>
              </div>

              <motion.div
                className="pt-2 border-t border-white/20"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.8 }}
              >
                <p className="text-xs text-gray-400 font-medium">
                  Excellence in Digital Healthcare
                </p>
              </motion.div>
            </motion.div>
          </motion.div>
        </div>
      </motion.aside>

      {/* Premium Mobile Menu Button */}
      <motion.button
        className="fixed top-6 left-6 z-50 p-4 bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl lg:hidden border border-white/20"
        onClick={toggleSidebar}
        whileHover={{
          scale: 1.1,
          rotate: 5,
          boxShadow: '0 20px 40px rgba(0, 102, 204, 0.3)'
        }}
        whileTap={{ scale: 0.9 }}
        style={{
          background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(240, 249, 255, 0.9) 100%)'
        }}
      >
        <motion.div
          animate={{ rotate: sidebarOpen ? 90 : 0 }}
          transition={{ duration: 0.3 }}
        >
          <Bars3Icon className="w-6 h-6 text-blue-600" />
        </motion.div>

        {/* Pulse Effect */}
        <motion.div
          className="absolute inset-0 rounded-2xl bg-blue-500/20"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.5, 0, 0.5],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut",
          }}
        />
      </motion.button>
    </>
  );
};

export default Sidebar;
