// User and Authentication Types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: 'doctor' | 'nurse' | 'admin';
  profileImageUrl?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

// Patient Types
export interface Patient {
  id: string;

  // Basic Information
  firstName: string;
  middleName?: string;
  lastName: string;
  dateOfBirth: string;
  gender: string;
  aadharNumber: string;
  mobileNumber: string;
  alternatePhone?: string;
  email?: string;
  instagramId?: string;
  facebookId?: string;
  referredBy?: string;

  // Address Information
  houseNumber?: string;
  village?: string;
  postOffice?: string;
  address: string;
  city?: string;
  state?: string;
  pincode?: string;
  country?: string;

  // Personal Information
  maritalStatus?: string;
  occupation?: string;
  education?: string;
  religion?: string;
  nationality?: string;

  // Physical Information
  height?: number;
  weight?: number;
  bmi?: number;
  heightUnit?: string;
  weightUnit?: string;
  bloodGroup?: string;

  // Medical Scores
  charlsonIndex?: number;
  asaGrade?: string;
  ecogGrade?: string;

  // Medical Conditions
  dmStatus?: string;
  htnStatus?: string;
  hyperlipidemiaStatus?: string;
  hypothyroidStatus?: string;
  cardiacDiseaseStatus?: string;
  pulmonaryDiseaseStatus?: string;
  neurologicalDiseaseStatus?: string;
  rheumatologicalDiseaseStatus?: string;
  otherDiseases?: string;

  // Visit Information
  dateOfVisit?: string;
  primaryDisease?: string;
  symptoms?: string;
  clinicalExamination?: string;
  investigations?: string;
  finalDiagnosis?: string;

  // Investigations
  ultrasonographyDate?: string;
  ultrasonographyFindings?: string;
  cectDate?: string;
  cectFindings?: string;
  endoscopyDate?: string;
  endoscopyFindings?: string;
  biopsyDate?: string;
  biopsyFindings?: string;
  colonoscopyDate?: string;
  colonoscopyFindings?: string;
  colonoscopicBiopsyDate?: string;
  colonoscopicBiopsyFindings?: string;
  petCtFindings?: string;
  otherBiopsyDate?: string;
  otherBiopsyFindings?: string;

  // Treatment
  medications?: string;
  primaryTreatmentPlan?: string;
  admissionDate?: string;
  surgeryPlanDate?: string;
  surgeryName?: string;
  surgeryRisks?: string;
  consentObtained?: boolean;
  surgeryDate?: string;
  surgeon?: string;
  assistantSurgeon?: string;
  otFindings?: string;
  otProcedure?: string;
  hospitalCourse?: string;
  complications?: string;
  clavienDindoGrade?: string;
  dischargeDate?: string;
  dischargeMedications?: string;
  dischargeAdvice?: string;
  nextFollowUpDate?: string;
  conservativeTreatment?: string;
  icuStay?: string;
  hospitalStay?: string;
  planForSurgery?: boolean;
  surgeryPlanDateConservative?: string;
  finalBiopsy?: string;
  diseaseStage?: string;
  chemotherapyRadiotherapy?: string;
  furtherManagementPlan?: string;

  // Medications
  medication1?: string;
  medication2?: string;
  medication3?: string;
  medication4?: string;
  medication5?: string;

  // JSON Fields
  comorbidities?: any;
  allergies?: any;
  habits?: any;

  // Additional Information
  currentMedications?: string;
  familyHistory?: string;
  socialHistory?: string;
  notes?: string;
  isPregnant?: boolean;
  isBreastfeeding?: boolean;
  hasInsurance?: boolean;
  insuranceProvider?: string;
  policyNumber?: string;

  // Profile Image
  profileImageUrl?: string;

  // Metadata
  createdBy: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface PatientFormData {
  // Basic Information
  fullName: string;
  dateOfBirth: string;
  gender: 'Male' | 'Female' | 'Other';
  aadharNumber: string;
  mobileNumber: string;
  email: string;
  address: string;
  
  // Physical Information
  heightCm: number;
  weightKg: number;
  bloodGroup: 'A+' | 'A-' | 'B+' | 'B-' | 'AB+' | 'AB-' | 'O+' | 'O-' | '';
  
  // Medical Scores
  charlsonIndex: number;
  asaGrade: number;
  ecogGrade: number;
  
  // Comorbidities
  comorbidities: string[]; // Array of comorbidity IDs
  
  // Profile Image
  profileImage?: File;
}

// Comorbidity Types
export interface Comorbidity {
  id: string;
  name: string;
  description?: string;
  category: string;
  isActive: boolean;
  createdAt: string;
}

export interface PatientComorbidity {
  id: string;
  patientId: string;
  comorbidity: Comorbidity;
  severity: 'Mild' | 'Moderate' | 'Severe';
  diagnosedDate?: string;
  notes?: string;
  createdAt: string;
}

// Search and Filter Types
export interface PatientSearchParams {
  query?: string;
  gender?: string;
  bloodGroup?: string;
  ageRange?: {
    min: number;
    max: number;
  };
  comorbidities?: string[];
  sortBy?: 'name' | 'createdAt' | 'patientId';
  sortOrder?: 'asc' | 'desc';
  page?: number;
  limit?: number;
}

export interface SearchResult<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}

// Dashboard Types
export interface DashboardStats {
  totalPatients: number;
  newPatientsThisMonth: number;
  averageAge: number;
  genderDistribution: {
    male: number;
    female: number;
    other: number;
  };
  commonComorbidities: {
    name: string;
    count: number;
    percentage: number;
  }[];
  bloodGroupDistribution: {
    [key: string]: number;
  };
  bmiDistribution: {
    underweight: number;
    normal: number;
    overweight: number;
    obese: number;
  };
}

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form Validation Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormState {
  isLoading: boolean;
  errors: ValidationError[];
  isValid: boolean;
}

// File Upload Types
export interface FileUploadResponse {
  url: string;
  key: string;
  size: number;
  type: string;
}

// Medical Reference Types
export interface MedicalReference {
  charlsonIndexItems: {
    condition: string;
    score: number;
  }[];
  asaGrades: {
    grade: number;
    description: string;
  }[];
  ecogGrades: {
    grade: number;
    description: string;
  }[];
  bmiCategories: {
    category: string;
    range: string;
    color: string;
  }[];
}
