{"name": "@types/uuid", "version": "9.0.8", "description": "TypeScript definitions for uuid", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/uuid", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/iamolivinius"}, {"name": "<PERSON>", "githubUsername": "f<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/felipeochoa"}, {"name": "<PERSON>", "githubUsername": "cj<PERSON><PERSON>", "url": "https://github.com/cjbarth"}, {"name": "<PERSON><PERSON>", "githubUsername": "LinusU", "url": "https://github.com/LinusU"}, {"name": "<PERSON>", "githubUsername": "ctavan", "url": "https://github.com/ctavan"}], "main": "", "types": "index.d.ts", "exports": {"./package.json": "./package.json", ".": {"types": {"import": "./index.d.mts", "default": "./index.d.ts"}}}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/uuid"}, "scripts": {}, "dependencies": {}, "typesPublisherContentHash": "ee6ba7ad17fbbead7a508faf213a9ad0f49c12929e8c6b0f05fb35129bc72d61", "typeScriptVersion": "4.6"}