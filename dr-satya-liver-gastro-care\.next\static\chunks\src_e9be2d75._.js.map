{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: {\n      gradient: 'from-blue-500 via-blue-600 to-blue-700',\n      bg: 'from-blue-50 to-blue-100',\n      text: 'text-blue-700',\n      border: 'border-blue-200'\n    },\n    green: {\n      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',\n      bg: 'from-emerald-50 to-emerald-100',\n      text: 'text-emerald-700',\n      border: 'border-emerald-200'\n    },\n    amber: {\n      gradient: 'from-amber-500 via-amber-600 to-amber-700',\n      bg: 'from-amber-50 to-amber-100',\n      text: 'text-amber-700',\n      border: 'border-amber-200'\n    },\n    red: {\n      gradient: 'from-red-500 via-red-600 to-red-700',\n      bg: 'from-red-50 to-red-100',\n      text: 'text-red-700',\n      border: 'border-red-200'\n    },\n    teal: {\n      gradient: 'from-teal-500 via-teal-600 to-teal-700',\n      bg: 'from-teal-50 to-teal-100',\n      text: 'text-teal-700',\n      border: 'border-teal-200'\n    }\n  };\n\n  const currentColor = colorClasses[color];\n\n  return (\n    <motion.div\n      className={`medical-card-glass relative overflow-hidden border ${currentColor.border} ${className}`}\n      whileHover={{\n        y: -8,\n        scale: 1.02,\n        transition: { duration: 0.3 }\n      }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className={`absolute inset-0 bg-gradient-to-br ${currentColor.bg} opacity-50`} />\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/20\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-white/10\"\n          animate={{\n            scale: [1, 1.3, 1],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <motion.p\n              className=\"text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              {title}\n            </motion.p>\n\n            <motion.p\n              className=\"text-4xl font-bold text-gray-900 mb-2\"\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            >\n              {value}\n            </motion.p>\n\n            {subtitle && (\n              <motion.p\n                className=\"text-sm text-gray-600 font-medium\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                {subtitle}\n              </motion.p>\n            )}\n\n            {trend && (\n              <motion.div\n                className=\"flex items-center mt-3\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n              >\n                <motion.span\n                  className={`inline-flex items-center gap-1 text-sm font-bold px-2 py-1 rounded-full ${\n                    trend.isPositive\n                      ? 'text-emerald-700 bg-emerald-100'\n                      : 'text-red-700 bg-red-100'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                >\n                  <motion.span\n                    animate={{ rotate: trend.isPositive ? 0 : 180 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    ↗\n                  </motion.span>\n                  {Math.abs(trend.value)}%\n                </motion.span>\n                <span className=\"text-xs text-gray-500 ml-2 font-medium\">vs last month</span>\n              </motion.div>\n            )}\n          </div>\n\n          {icon && (\n            <motion.div\n              className={`p-4 rounded-2xl bg-gradient-to-br ${currentColor.gradient} text-white shadow-lg`}\n              whileHover={{\n                scale: 1.1,\n                rotate: 5,\n                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)'\n              }}\n              initial={{ opacity: 0, scale: 0, rotate: -90 }}\n              animate={{ opacity: 1, scale: 1, rotate: 0 }}\n              transition={{ delay: 0.3, type: \"spring\", stiffness: 200 }}\n            >\n              {icon}\n            </motion.div>\n          )}\n        </div>\n      </div>\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className={`absolute inset-0 bg-gradient-to-r ${currentColor.gradient} opacity-0 rounded-2xl`}\n        whileHover={{ opacity: 0.1 }}\n        transition={{ duration: 0.3 }}\n      />\n    </motion.div>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <motion.div\n      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}\n      onClick={() => onClick?.(patient)}\n      whileHover={{\n        y: -6,\n        scale: 1.02,\n        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',\n        transition: { duration: 0.3 }\n      }}\n      whileTap={{ scale: 0.98 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50\" />\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n      />\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Premium Avatar */}\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ duration: 0.2 }}\n          >\n            {patient.profileImageUrl ? (\n              <img\n                src={patient.profileImageUrl}\n                alt={patient.fullName}\n                className=\"w-16 h-16 rounded-2xl object-cover shadow-lg\"\n              />\n            ) : (\n              <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                {getInitials(patient.fullName)}\n              </div>\n            )}\n            {/* Status Indicator */}\n            <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n              <div className=\"w-full h-full bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n          </motion.div>\n\n          {/* Patient Info */}\n          <div className=\"flex-1 min-w-0\">\n            <motion.div\n              className=\"flex items-center justify-between mb-2\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h3 className=\"font-bold text-gray-900 text-lg truncate\">\n                {patient.fullName}\n              </h3>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200\">\n                {patient.patientId}\n              </span>\n            </motion.div>\n\n            <motion.div\n              className=\"flex items-center space-x-3 text-sm\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  patient.gender === 'Male' ? 'bg-blue-500' :\n                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'\n                }`}></div>\n                <span className=\"text-gray-600 font-medium\">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n              </div>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <span className=\"text-gray-600 font-medium\">{patient.gender}</span>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <div className=\"flex items-center gap-1\">\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span className=\"text-gray-600 font-medium\">{patient.mobileNumber}</span>\n              </div>\n            </motion.div>\n\n            {patient.lastVisit && (\n              <motion.div\n                className=\"flex items-center gap-1 mt-2\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-xs text-gray-500 font-medium\">\n                  Last visit: {patient.lastVisit}\n                </p>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Arrow Indicator */}\n          <motion.div\n            className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-200\"\n            whileHover={{ x: 5 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.div>\n        </div>\n\n        {/* Bottom Border Animation */}\n        <motion.div\n          className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full\"\n          initial={{ width: 0 }}\n          whileHover={{ width: '100%' }}\n          transition={{ duration: 0.3 }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,AAAC,4BAGjB,OADA,cAAc,CAAC,QAAQ,EAAC,WAExB,OADA,aAAa,CAAC,OAAO,EAAC,UAEtB,OADA,WAAW,4CAA4C,YAAW,UAElE,OADA,UAAU,mBAAmB,IAAG,UACtB,OAAV,WAAU;IAGd,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;KApDM;AAqEC,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,KAAK;YACH,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,MAAM;IAExC,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,sDAA4E,OAAvB,aAAa,MAAM,EAAC,KAAa,OAAV;QACxF,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAW,AAAC,sCAAqD,OAAhB,aAAa,EAAE,EAAC;;;;;;0BAGtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG;gCAAK;6BAAI;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAK;gCAAK;6BAAE;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;8CAGH,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;8CAExD;;;;;;gCAGF,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;gCAIJ,uBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;;sDAEzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAW,AAAC,2EAIX,OAHC,MAAM,UAAU,GACZ,oCACA;4CAEN,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,QAAQ,MAAM,UAAU,GAAG,IAAI;oDAAI;oDAC9C,YAAY;wDAAE,UAAU;oDAAI;8DAC7B;;;;;;gDAGA,KAAK,GAAG,CAAC,MAAM,KAAK;gDAAE;;;;;;;sDAEzB,6LAAC;4CAAK,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;wBAK9D,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,AAAC,qCAA0D,OAAtB,aAAa,QAAQ,EAAC;4BACtE,YAAY;gCACV,OAAO;gCACP,QAAQ;gCACR,WAAW;4BACb;4BACA,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ,CAAC;4BAAG;4BAC7C,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ;4BAAE;4BAC3C,YAAY;gCAAE,OAAO;gCAAK,MAAM;gCAAU,WAAW;4BAAI;sCAExD;;;;;;;;;;;;;;;;;0BAOT,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,qCAA0D,OAAtB,aAAa,QAAQ,EAAC;gBACtE,YAAY;oBAAE,SAAS;gBAAI;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;MA7Ka;AA8LN,MAAM,cAA0C;QAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,2FAAoG,OAAV;QACtG,SAAS,IAAM,oBAAA,8BAAA,QAAU;QACzB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,6LAAC;gBAAI,WAAU;;;;;;0BAGf,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;;;;;;0BAGZ,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,UAAU;gCAAI;;oCAE3B,QAAQ,eAAe,iBACtB,6LAAC;wCACC,KAAK,QAAQ,eAAe;wCAC5B,KAAK,QAAQ,QAAQ;wCACrB,WAAU;;;;;iGAGZ,6LAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,QAAQ;;;;;;kDAIjC,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ;;;;;;0DAEnB,6LAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS;;;;;;;;;;;;kDAItB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAW,AAAC,wBAGhB,OAFC,QAAQ,MAAM,KAAK,SAAS,gBAC5B,QAAQ,MAAM,KAAK,WAAW,gBAAgB;;;;;;kEAEhD,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,GAAG,GAAG,AAAC,GAAc,OAAZ,QAAQ,GAAG,EAAC,YAAU;;;;;;;;;;;;0DAGtF,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAK,WAAU;0DAA6B,QAAQ,MAAM;;;;;;0DAE3D,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,6LAAC;wDAAK,WAAU;kEAA6B,QAAQ,YAAY;;;;;;;;;;;;;;;;;;oCAIpE,QAAQ,SAAS,kBAChB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,6LAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,6LAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,6LAAC;gDAAE,WAAU;;oDAAoC;oDAClC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0CAOtC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG;gCAAE;0CAEnB,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAO;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;AAKtC;MAhJa;AA2JN,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAK,WAAW;;0BACf,6LAAC;gBACC,WAAW,AAAC,qCAAwE,OAApC,cAAc,mBAAmB;gBACjF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GAnDa;MAAA;uCAqDE", "debugId": null}}, {"offset": {"line": 862, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n  fullScreen?: boolean;\n  className?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  size = 'md', \n  text = 'Loading...', \n  fullScreen = false,\n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-10 h-10',\n    lg: 'w-16 h-16'\n  };\n\n  const LoadingSpinner = () => (\n    <div className=\"relative\">\n      {/* Outer ring */}\n      <motion.div\n        className={`${sizeClasses[size]} border-4 border-blue-100 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Inner spinning ring */}\n      <motion.div\n        className={`absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-blue-600 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Medical cross in center */}\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <div className=\"w-3 h-3 relative\">\n          <div className=\"absolute inset-x-1/2 inset-y-0 w-0.5 bg-blue-600 transform -translate-x-1/2\" />\n          <div className=\"absolute inset-y-1/2 inset-x-0 h-0.5 bg-blue-600 transform -translate-y-1/2\" />\n        </div>\n      </div>\n    </div>\n  );\n\n  const LoadingDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-2 h-2 bg-blue-600 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const content = (\n    <motion.div\n      className={`flex flex-col items-center justify-center gap-4 ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <LoadingSpinner />\n      \n      {text && (\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <p className=\"text-gray-600 font-medium\">{text}</p>\n          <LoadingDots />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n\n  if (fullScreen) {\n    return (\n      <motion.div\n        className=\"fixed inset-0 bg-white bg-opacity-90 backdrop-blur-sm z-50 flex items-center justify-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n      >\n        {content}\n      </motion.div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton Loading Component\ninterface SkeletonProps {\n  className?: string;\n  lines?: number;\n  avatar?: boolean;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({ \n  className = '', \n  lines = 3, \n  avatar = false \n}) => {\n  return (\n    <div className={`animate-pulse ${className}`}>\n      <div className=\"flex items-start space-x-4\">\n        {avatar && (\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n        )}\n        <div className=\"flex-1 space-y-2\">\n          {Array.from({ length: lines }).map((_, index) => (\n            <div\n              key={index}\n              className={`h-4 bg-gray-200 rounded ${\n                index === lines - 1 ? 'w-3/4' : 'w-full'\n              }`}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Card Skeleton\nexport const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {\n  return (\n    <div className={`medical-card p-6 ${className}`}>\n      <div className=\"animate-pulse\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n          <div className=\"flex-1\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\" />\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"h-3 bg-gray-200 rounded\" />\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n          <div className=\"h-3 bg-gray-200 rounded w-4/6\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Button Loading State\ninterface ButtonLoadingProps {\n  loading?: boolean;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n}\n\nexport const ButtonLoading: React.FC<ButtonLoadingProps> = ({\n  loading = false,\n  children,\n  className = '',\n  onClick,\n  disabled = false\n}) => {\n  return (\n    <button\n      className={`btn-primary relative ${className} ${\n        loading || disabled ? 'opacity-70 cursor-not-allowed' : ''\n      }`}\n      onClick={onClick}\n      disabled={loading || disabled}\n    >\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      <span className={loading ? 'opacity-0' : 'opacity-100'}>\n        {children}\n      </span>\n    </button>\n  );\n};\n\nexport default Loading;\n"], "names": [], "mappings": ";;;;;;;AAGA;AAHA;;;AAYA,MAAM,UAAkC;QAAC,EACvC,OAAO,IAAI,EACX,OAAO,YAAY,EACnB,aAAa,KAAK,EAClB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,6LAAC;YAAI,WAAU;;8BAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,GAAoB,OAAlB,WAAW,CAAC,KAAK,EAAC;oBAChC,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,AAAC,oBAAqC,OAAlB,WAAW,CAAC,KAAK,EAAC;oBACjD,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,cAAc,kBAClB,6LAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,sBACd,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,QAAQ;oBACjB;mBAVK;;;;;;;;;;IAgBb,MAAM,wBACJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,mDAA4D,OAAV;QAC9D,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,6LAAC;;;;;YAEA,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,6LAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,6LAAC;;;;;;;;;;;;;;;;;IAMT,IAAI,YAAY;QACd,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;sBAElB;;;;;;IAGP;IAEA,OAAO;AACT;KA/FM;AAwGC,MAAM,WAAoC;QAAC,EAChD,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,SAAS,KAAK,EACf;IACC,qBACE,6LAAC;QAAI,WAAW,AAAC,iBAA0B,OAAV;kBAC/B,cAAA,6LAAC;YAAI,WAAU;;gBACZ,wBACC,6LAAC;oBAAI,WAAU;;;;;;8BAEjB,6LAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,6LAAC;4BAEC,WAAW,AAAC,2BAEX,OADC,UAAU,QAAQ,IAAI,UAAU;2BAF7B;;;;;;;;;;;;;;;;;;;;;AAUnB;MAxBa;AA2BN,MAAM,eAAiD;QAAC,EAAE,YAAY,EAAE,EAAE;IAC/E,qBACE,6LAAC;QAAI,WAAW,AAAC,oBAA6B,OAAV;kBAClC,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;;;;;8CACf,6LAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAGnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;sCACf,6LAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;MAnBa;AA8BN,MAAM,gBAA8C;QAAC,EAC1D,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EACjB;IACC,qBACE,6LAAC;QACC,WAAW,AAAC,wBACV,OADiC,WAAU,KAE5C,OADC,WAAW,WAAW,kCAAkC;QAE1D,SAAS;QACT,UAAU,WAAW;;YAEpB,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;0BAGnB,6LAAC;gBAAK,WAAW,UAAU,cAAc;0BACtC;;;;;;;;;;;;AAIT;MAzBa;uCA2BE", "debugId": null}}, {"offset": {"line": 1248, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/analytics/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport { \n  ChartBarIcon, \n  UserGroupIcon, \n  HeartIcon,\n  CalendarIcon \n} from '@heroicons/react/24/outline';\nimport { InfoCard } from '@/components/ui/Card';\nimport Loading from '@/components/ui/Loading';\nimport { toast } from 'react-hot-toast';\n\nconst AnalyticsPage: React.FC = () => {\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/dashboard/stats');\n        const data = await response.json();\n        \n        if (data.success) {\n          setStats(data.data);\n        } else {\n          toast.error('Failed to fetch analytics data');\n        }\n      } catch (error) {\n        console.error('Error fetching analytics:', error);\n        toast.error('Failed to fetch analytics data');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  if (loading) {\n    return <Loading text=\"Loading analytics...\" fullScreen />;\n  }\n\n  return (\n    <motion.div\n      className=\"space-y-8\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Header */}\n      <motion.div variants={itemVariants}>\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Medical Analytics\n        </h1>\n        <p className=\"text-gray-600\">\n          Comprehensive insights and reports for patient care management.\n        </p>\n      </motion.div>\n\n      {/* Overview Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <UserGroupIcon className=\"w-12 h-12 text-blue-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.totalPatients || 0}</h3>\n            <p className=\"text-gray-600\">Total Patients</p>\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <CalendarIcon className=\"w-12 h-12 text-green-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.newPatientsThisMonth || 0}</h3>\n            <p className=\"text-gray-600\">New This Month</p>\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <ChartBarIcon className=\"w-12 h-12 text-purple-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.averageAge || 0}</h3>\n            <p className=\"text-gray-600\">Average Age</p>\n          </div>\n        </motion.div>\n\n        <motion.div variants={itemVariants}>\n          <div className=\"medical-card p-6 text-center\">\n            <HeartIcon className=\"w-12 h-12 text-red-600 mx-auto mb-4\" />\n            <h3 className=\"text-2xl font-bold text-gray-900\">{stats?.criticalCases || 0}</h3>\n            <p className=\"text-gray-600\">Critical Cases</p>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Detailed Analytics */}\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n        {/* Gender Distribution */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"Gender Distribution\" icon={<UserGroupIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-4\">\n              {stats?.genderDistribution && Object.entries(stats.genderDistribution).map(([gender, count]) => (\n                <div key={gender} className=\"flex items-center justify-between\">\n                  <span className=\"capitalize text-gray-700\">{gender}</span>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-blue-600 h-2 rounded-full\" \n                        style={{ \n                          width: `${(count as number / stats.totalPatients) * 100}%` \n                        }}\n                      />\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 w-8\">{count as number}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </InfoCard>\n        </motion.div>\n\n        {/* Blood Group Distribution */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"Blood Group Distribution\" icon={<HeartIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-3\">\n              {stats?.bloodGroupDistribution && Object.entries(stats.bloodGroupDistribution)\n                .sort(([,a], [,b]) => (b as number) - (a as number))\n                .slice(0, 6)\n                .map(([bloodGroup, count]) => (\n                <div key={bloodGroup} className=\"flex items-center justify-between\">\n                  <span className=\"text-gray-700 font-medium\">{bloodGroup}</span>\n                  <div className=\"flex items-center space-x-3\">\n                    <div className=\"w-24 bg-gray-200 rounded-full h-2\">\n                      <div \n                        className=\"bg-red-500 h-2 rounded-full\" \n                        style={{ \n                          width: `${(count as number / Math.max(...Object.values(stats.bloodGroupDistribution))) * 100}%` \n                        }}\n                      />\n                    </div>\n                    <span className=\"text-sm font-medium text-gray-900 w-6\">{count as number}</span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </InfoCard>\n        </motion.div>\n\n        {/* BMI Distribution */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"BMI Distribution\" icon={<ChartBarIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-4\">\n              {stats?.bmiDistribution && Object.entries(stats.bmiDistribution).map(([category, count]) => {\n                const colors = {\n                  underweight: 'bg-blue-500',\n                  normal: 'bg-green-500',\n                  overweight: 'bg-yellow-500',\n                  obese: 'bg-red-500'\n                };\n                const total = Object.values(stats.bmiDistribution).reduce((a: number, b: number) => a + b, 0);\n                \n                return (\n                  <div key={category} className=\"flex items-center justify-between\">\n                    <span className=\"capitalize text-gray-700\">{category}</span>\n                    <div className=\"flex items-center space-x-3\">\n                      <div className=\"w-32 bg-gray-200 rounded-full h-2\">\n                        <div \n                          className={`h-2 rounded-full ${colors[category as keyof typeof colors]}`}\n                          style={{ \n                            width: total > 0 ? `${((count as number) / total) * 100}%` : '0%'\n                          }}\n                        />\n                      </div>\n                      <span className=\"text-sm font-medium text-gray-900 w-8\">{count as number}</span>\n                    </div>\n                  </div>\n                );\n              })}\n            </div>\n          </InfoCard>\n        </motion.div>\n\n        {/* Recent Activity */}\n        <motion.div variants={itemVariants}>\n          <InfoCard title=\"Recent Activity\" icon={<CalendarIcon className=\"w-5 h-5\" />}>\n            <div className=\"space-y-4\">\n              {stats?.recentActivity?.length > 0 ? (\n                stats.recentActivity.map((activity: any, index: number) => (\n                  <div key={index} className=\"flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\">\n                    <div className=\"w-2 h-2 bg-blue-600 rounded-full mt-2\" />\n                    <div className=\"flex-1\">\n                      <p className=\"text-sm font-medium text-gray-900\">{activity.action}</p>\n                      <p className=\"text-xs text-gray-600\">Patient: {activity.patient}</p>\n                      <p className=\"text-xs text-gray-500\">{activity.time}</p>\n                    </div>\n                  </div>\n                ))\n              ) : (\n                <p className=\"text-gray-500 text-center py-4\">No recent activity</p>\n              )}\n            </div>\n          </InfoCard>\n        </motion.div>\n      </div>\n\n      {/* Summary */}\n      <motion.div variants={itemVariants}>\n        <div className=\"medical-card p-6 bg-gradient-to-r from-blue-50 to-teal-50 border border-blue-200\">\n          <h3 className=\"text-lg font-semibold text-blue-900 mb-2\">Analytics Summary</h3>\n          <p className=\"text-blue-700 text-sm\">\n            Your medical practice is serving <strong>{stats?.totalPatients || 0} patients</strong> with \n            an average age of <strong>{stats?.averageAge || 0} years</strong>. \n            This month, you've registered <strong>{stats?.newPatientsThisMonth || 0} new patients</strong>.\n            {stats?.criticalCases > 0 && (\n              <span className=\"text-red-700 font-medium\">\n                {' '}Please note: {stats.criticalCases} patients require immediate attention.\n              </span>\n            )}\n          </p>\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default AnalyticsPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;;;AAZA;;;;;;;AAcA,MAAM,gBAA0B;QA4LjB;;IA3Lb,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;sDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,KAAK,OAAO,EAAE;4BAChB,SAAS,KAAK,IAAI;wBACpB,OAAO;4BACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,6BAA6B;wBAC3C,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,IAAI,SAAS;QACX,qBAAO,6LAAC,sIAAA,CAAA,UAAO;YAAC,MAAK;YAAuB,UAAU;;;;;;IACxD;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCAGtD,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,4NAAA,CAAA,gBAAa;oCAAC,WAAU;;;;;;8CACzB,6LAAC;oCAAG,WAAU;8CAAoC,CAAA,kBAAA,4BAAA,MAAO,aAAa,KAAI;;;;;;8CAC1E,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAG,WAAU;8CAAoC,CAAA,kBAAA,4BAAA,MAAO,oBAAoB,KAAI;;;;;;8CACjF,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,0NAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;8CACxB,6LAAC;oCAAG,WAAU;8CAAoC,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;;;;;;8CACvE,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAIjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,oNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,6LAAC;oCAAG,WAAU;8CAAoC,CAAA,kBAAA,4BAAA,MAAO,aAAa,KAAI;;;;;;8CAC1E,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;0BAMnC,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAsB,oBAAM,6LAAC,4NAAA,CAAA,gBAAa;gCAAC,WAAU;;;;;;sCACnE,cAAA,6LAAC;gCAAI,WAAU;0CACZ,CAAA,kBAAA,4BAAA,MAAO,kBAAkB,KAAI,OAAO,OAAO,CAAC,MAAM,kBAAkB,EAAE,GAAG,CAAC;wCAAC,CAAC,QAAQ,MAAM;yDACzF,6LAAC;wCAAiB,WAAU;;0DAC1B,6LAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,AAAC,GAAgD,OAA9C,AAAC,QAAkB,MAAM,aAAa,GAAI,KAAI;4DAC1D;;;;;;;;;;;kEAGJ,6LAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;;uCAXnD;;;;;;;;;;;;;;;;;;;;;kCAoBlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAA2B,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;sCACpE,cAAA,6LAAC;gCAAI,WAAU;0CACZ,CAAA,kBAAA,4BAAA,MAAO,sBAAsB,KAAI,OAAO,OAAO,CAAC,MAAM,sBAAsB,EAC1E,IAAI,CAAC;wCAAC,GAAE,EAAE,UAAE,GAAE,EAAE;2CAAK,AAAC,IAAgB;mCACtC,KAAK,CAAC,GAAG,GACT,GAAG,CAAC;wCAAC,CAAC,YAAY,MAAM;yDACzB,6LAAC;wCAAqB,WAAU;;0DAC9B,6LAAC;gDAAK,WAAU;0DAA6B;;;;;;0DAC7C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAU;4DACV,OAAO;gEACL,OAAO,AAAC,GAAqF,OAAnF,AAAC,QAAkB,KAAK,GAAG,IAAI,OAAO,MAAM,CAAC,MAAM,sBAAsB,KAAM,KAAI;4DAC/F;;;;;;;;;;;kEAGJ,6LAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;;uCAXnD;;;;;;;;;;;;;;;;;;;;;kCAoBlB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAmB,oBAAM,6LAAC,0NAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;sCAC/D,cAAA,6LAAC;gCAAI,WAAU;0CACZ,CAAA,kBAAA,4BAAA,MAAO,eAAe,KAAI,OAAO,OAAO,CAAC,MAAM,eAAe,EAAE,GAAG,CAAC;wCAAC,CAAC,UAAU,MAAM;oCACrF,MAAM,SAAS;wCACb,aAAa;wCACb,QAAQ;wCACR,YAAY;wCACZ,OAAO;oCACT;oCACA,MAAM,QAAQ,OAAO,MAAM,CAAC,MAAM,eAAe,EAAE,MAAM,CAAC,CAAC,GAAW,IAAc,IAAI,GAAG;oCAE3F,qBACE,6LAAC;wCAAmB,WAAU;;0DAC5B,6LAAC;gDAAK,WAAU;0DAA4B;;;;;;0DAC5C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DACC,WAAW,AAAC,oBAA2D,OAAxC,MAAM,CAAC,SAAgC;4DACtE,OAAO;gEACL,OAAO,QAAQ,IAAI,AAAC,GAAoC,OAAlC,AAAE,QAAmB,QAAS,KAAI,OAAK;4DAC/D;;;;;;;;;;;kEAGJ,6LAAC;wDAAK,WAAU;kEAAyC;;;;;;;;;;;;;uCAXnD;;;;;gCAed;;;;;;;;;;;;;;;;kCAMN,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;kCACpB,cAAA,6LAAC,mIAAA,CAAA,WAAQ;4BAAC,OAAM;4BAAkB,oBAAM,6LAAC,0NAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;sCAC9D,cAAA,6LAAC;gCAAI,WAAU;0CACZ,CAAA,kBAAA,6BAAA,wBAAA,MAAO,cAAc,cAArB,4CAAA,sBAAuB,MAAM,IAAG,IAC/B,MAAM,cAAc,CAAC,GAAG,CAAC,CAAC,UAAe,sBACvC,6LAAC;wCAAgB,WAAU;;0DACzB,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAqC,SAAS,MAAM;;;;;;kEACjE,6LAAC;wDAAE,WAAU;;4DAAwB;4DAAU,SAAS,OAAO;;;;;;;kEAC/D,6LAAC;wDAAE,WAAU;kEAAyB,SAAS,IAAI;;;;;;;;;;;;;uCAL7C;;;;kGAUZ,6LAAC;oCAAE,WAAU;8CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQxD,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;0BACpB,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA2C;;;;;;sCACzD,6LAAC;4BAAE,WAAU;;gCAAwB;8CACF,6LAAC;;wCAAQ,CAAA,kBAAA,4BAAA,MAAO,aAAa,KAAI;wCAAE;;;;;;;gCAAkB;8CACpE,6LAAC;;wCAAQ,CAAA,kBAAA,4BAAA,MAAO,UAAU,KAAI;wCAAE;;;;;;;gCAAe;8CACnC,6LAAC;;wCAAQ,CAAA,kBAAA,4BAAA,MAAO,oBAAoB,KAAI;wCAAE;;;;;;;gCAAsB;gCAC7F,CAAA,kBAAA,4BAAA,MAAO,aAAa,IAAG,mBACtB,6LAAC;oCAAK,WAAU;;wCACb;wCAAI;wCAAc,MAAM,aAAa;wCAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQvD;GAjOM;KAAA;uCAmOS", "debugId": null}}]}