'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { EyeIcon, EyeSlashIcon, LockClosedIcon, UserIcon } from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import Logo from '../ui/Logo';
import { ButtonLoading } from '../ui/Loading';
import { useAuthStore } from '@/store';

// Validation schema
const loginSchema = z.object({
  email: z.string().email('Please enter a valid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
});

type LoginFormData = z.infer<typeof loginSchema>;

const LoginPage: React.FC = () => {
  const [showPassword, setShowPassword] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const { login } = useAuthStore();

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  const onSubmit = async (data: LoginFormData) => {
    setIsLoading(true);
    try {
      // Simulate API call - replace with actual authentication
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      const success = await login(data);
      if (success) {
        toast.success('Welcome back!');
        // Navigation will be handled by the parent component
      } else {
        toast.error('Invalid credentials. Please try again.');
      }
    } catch (error) {
      toast.error('Login failed. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="absolute inset-0 gradient-animate"></div>

      {/* Floating Medical Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Animated DNA Helixes */}
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-32 opacity-10"
            style={{
              left: `${20 + i * 20}%`,
              top: `${10 + i * 15}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              rotate: [0, 360],
            }}
            transition={{
              duration: 8 + i * 2,
              repeat: Infinity,
              ease: "easeInOut",
            }}
          >
            <div className="w-full h-full bg-gradient-to-b from-blue-400 to-teal-400 rounded-full blur-sm"></div>
          </motion.div>
        ))}

        {/* Floating Medical Icons */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={`icon-${i}`}
            className="absolute w-8 h-8 opacity-5"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-10, 10, -10],
              x: [-5, 5, -5],
              rotate: [0, 180, 360],
            }}
            transition={{
              duration: 6 + Math.random() * 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 2,
            }}
          >
            <svg viewBox="0 0 24 24" fill="currentColor" className="w-full h-full text-white">
              <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
            </svg>
          </motion.div>
        ))}

        {/* Geometric Shapes */}
        <motion.div
          className="absolute top-20 right-20 w-32 h-32 border-2 border-white/20 rounded-full"
          animate={{ rotate: 360 }}
          transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute bottom-20 left-20 w-24 h-24 border-2 border-white/20 rounded-lg"
          animate={{ rotate: -360 }}
          transition={{ duration: 15, repeat: Infinity, ease: "linear" }}
        />
      </div>

      {/* Main Content */}
      <div className="relative z-10 min-h-screen flex items-center justify-center p-4">
        <motion.div
          className="w-full max-w-lg"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Premium Login Card */}
          <motion.div
            className="medical-card-glass p-10 relative overflow-hidden"
            variants={itemVariants}
            whileHover={{
              y: -8,
              transition: { duration: 0.3 }
            }}
          >
            {/* Card Glow Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-teal-500/10 rounded-2xl blur-xl"></div>

            {/* Content */}
            <div className="relative z-10">
            {/* Logo and Header */}
            <motion.div className="text-center mb-10" variants={itemVariants}>
              <div className="flex justify-center mb-8">
                <Logo size="xl" showText variant="premium" />
              </div>
              <motion.h1
                className="text-3xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-teal-800 bg-clip-text text-transparent mb-3"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3, duration: 0.6 }}
              >
                Welcome Back
              </motion.h1>
              <motion.p
                className="text-gray-600 text-lg font-medium"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4, duration: 0.6 }}
              >
                Sign in to access your medical dashboard
              </motion.p>

              {/* Decorative Line */}
              <motion.div
                className="w-24 h-1 bg-gradient-to-r from-blue-500 to-teal-500 mx-auto mt-4 rounded-full"
                initial={{ width: 0 }}
                animate={{ width: 96 }}
                transition={{ delay: 0.6, duration: 0.8 }}
              />
            </motion.div>

            {/* Premium Login Form */}
            <motion.form onSubmit={handleSubmit(onSubmit)} variants={itemVariants}>
              <div className="space-y-8">
                {/* Email Field with Floating Label */}
                <motion.div
                  className="form-group"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                      <UserIcon className="h-5 w-5 text-gray-400 transition-colors duration-300" />
                    </div>
                    <input
                      {...register('email')}
                      type="email"
                      className={`form-input-floating pl-12 ${errors.email ? 'error' : ''}`}
                      placeholder=" "
                      autoComplete="email"
                    />
                    <label className="form-label-floating">Email Address</label>
                  </div>
                  {errors.email && (
                    <motion.p
                      className="mt-2 text-sm text-red-500 flex items-center gap-1"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.email.message}
                    </motion.p>
                  )}
                </motion.div>

                {/* Password Field with Floating Label */}
                <motion.div
                  className="form-group"
                  whileHover={{ scale: 1.02 }}
                  transition={{ duration: 0.2 }}
                >
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10">
                      <LockClosedIcon className="h-5 w-5 text-gray-400 transition-colors duration-300" />
                    </div>
                    <input
                      {...register('password')}
                      type={showPassword ? 'text' : 'password'}
                      className={`form-input-floating pl-12 pr-12 ${errors.password ? 'error' : ''}`}
                      placeholder=" "
                      autoComplete="current-password"
                    />
                    <label className="form-label-floating">Password</label>
                    <motion.button
                      type="button"
                      className="absolute inset-y-0 right-0 pr-4 flex items-center z-10"
                      onClick={() => setShowPassword(!showPassword)}
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                    >
                      {showPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-400 hover:text-blue-500 transition-colors duration-200" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-400 hover:text-blue-500 transition-colors duration-200" />
                      )}
                    </motion.button>
                  </div>
                  {errors.password && (
                    <motion.p
                      className="mt-2 text-sm text-red-500 flex items-center gap-1"
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                    >
                      <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                      </svg>
                      {errors.password.message}
                    </motion.p>
                  )}
                </motion.div>

                {/* Remember Me & Forgot Password */}
                <div className="flex items-center justify-between">
                  <motion.label
                    className="flex items-center cursor-pointer"
                    whileHover={{ scale: 1.02 }}
                  >
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200"
                    />
                    <span className="ml-3 text-sm text-gray-600 font-medium">Remember me</span>
                  </motion.label>
                  <motion.a
                    href="#"
                    className="text-sm text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200"
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    Forgot password?
                  </motion.a>
                </div>

                {/* Premium Submit Button */}
                <motion.div
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                >
                  <ButtonLoading
                    loading={isLoading}
                    className="w-full btn-primary text-lg py-4 relative overflow-hidden"
                    disabled={isLoading}
                  >
                    <motion.span
                      className="relative z-10"
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ delay: 0.2 }}
                    >
                      {isLoading ? (
                        <div className="flex items-center justify-center gap-3">
                          <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                          Signing in...
                        </div>
                      ) : (
                        <div className="flex items-center justify-center gap-2">
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                          </svg>
                          Sign In to Dashboard
                        </div>
                      )}
                    </motion.span>
                  </ButtonLoading>
                </motion.div>
              </div>
            </motion.form>

            {/* Premium Demo Credentials */}
            <motion.div
              className="mt-10 p-6 bg-gradient-to-r from-blue-50 via-indigo-50 to-teal-50 rounded-2xl border border-blue-100 relative overflow-hidden"
              variants={itemVariants}
              whileHover={{ scale: 1.02 }}
            >
              {/* Background Pattern */}
              <div className="absolute inset-0 opacity-5">
                <svg className="w-full h-full" viewBox="0 0 100 100">
                  <pattern id="demo-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                    <circle cx="10" cy="10" r="2" fill="currentColor" />
                  </pattern>
                  <rect width="100" height="100" fill="url(#demo-pattern)" />
                </svg>
              </div>

              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-teal-500 rounded-lg flex items-center justify-center">
                    <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z" clipRule="evenodd" />
                    </svg>
                  </div>
                  <h3 className="text-lg font-bold bg-gradient-to-r from-blue-700 to-teal-700 bg-clip-text text-transparent">
                    Demo Access
                  </h3>
                </div>

                <div className="grid grid-cols-1 gap-3">
                  <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg backdrop-blur-sm">
                    <span className="text-sm font-medium text-gray-600">Email:</span>
                    <code className="text-sm font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded">
                      <EMAIL>
                    </code>
                  </div>
                  <div className="flex items-center justify-between p-3 bg-white/60 rounded-lg backdrop-blur-sm">
                    <span className="text-sm font-medium text-gray-600">Password:</span>
                    <code className="text-sm font-bold text-teal-700 bg-teal-100 px-2 py-1 rounded">
                      demo123
                    </code>
                  </div>
                </div>

                <motion.p
                  className="text-xs text-gray-500 mt-3 text-center"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 1 }}
                >
                  Use these credentials to explore the medical dashboard
                </motion.p>
              </div>
            </motion.div>
            </div>
          </motion.div>

          {/* Premium Footer */}
          <motion.div
            className="text-center mt-8"
            variants={itemVariants}
          >
            <motion.p
              className="text-sm text-white/80 font-medium"
              whileHover={{ scale: 1.05 }}
            >
              © 2024 Dr Satya&apos;s Liver & Gastro Care. All rights reserved.
            </motion.p>
            <motion.div
              className="flex items-center justify-center gap-4 mt-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 1.2 }}
            >
              <span className="text-xs text-white/60">Powered by</span>
              <div className="flex items-center gap-1">
                <div className="w-4 h-4 bg-gradient-to-r from-blue-400 to-teal-400 rounded-full"></div>
                <span className="text-xs font-semibold text-white/80">Medical AI</span>
              </div>
            </motion.div>
          </motion.div>
        </motion.div>
      </div>
    </div>
  );
};

export default LoginPage;
