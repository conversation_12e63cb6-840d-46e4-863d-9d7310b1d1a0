module.exports = {

"[project]/.next-internal/server/app/api/patients/[id]/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// Database connection using direct PostgreSQL connection
__turbopack_context__.s({
    "db": ()=>db,
    "pool": ()=>pool,
    "testConnection": ()=>testConnection
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
const pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"]({
    connectionString: process.env.DATABASE_URL,
    ssl: {
        rejectUnauthorized: false
    }
});
;
const db = {
    query: async (text, params)=>{
        const client = await pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        } finally{
            client.release();
        }
    },
    getClient: async ()=>{
        return await pool.connect();
    }
};
const testConnection = async ()=>{
    try {
        const result = await db.query('SELECT NOW()');
        console.log('Database connected successfully:', result.rows[0]);
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
};
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/app/api/patients/[id]/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "DELETE": ()=>DELETE,
    "GET": ()=>GET,
    "PUT": ()=>PUT
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function GET(request, { params }) {
    try {
        const { id } = params;
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Patient ID is required'
            }, {
                status: 400
            });
        }
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pool"].connect();
        try {
            const query = `
        SELECT 
          id,
          first_name,
          middle_name,
          last_name,
          date_of_birth,
          gender,
          aadhar_number,
          mobile_number,
          alternate_phone,
          email,
          instagram_id,
          facebook_id,
          referred_by,
          house_number,
          village,
          post_office,
          address,
          city,
          state,
          pincode,
          country,
          marital_status,
          occupation,
          education,
          religion,
          nationality,
          height,
          weight,
          bmi,
          height_unit,
          weight_unit,
          blood_group,
          charlson_index,
          asa_grade,
          ecog_grade,
          dm_status,
          htn_status,
          hyperlipidemia_status,
          hypothyroid_status,
          cardiac_disease_status,
          pulmonary_disease_status,
          neurological_disease_status,
          rheumatological_disease_status,
          other_diseases,
          date_of_visit,
          primary_disease,
          symptoms,
          clinical_examination,
          investigations,
          final_diagnosis,
          ultrasonography_date,
          ultrasonography_findings,
          cect_date,
          cect_findings,
          endoscopy_date,
          endoscopy_findings,
          biopsy_date,
          biopsy_findings,
          colonoscopy_date,
          colonoscopy_findings,
          colonoscopic_biopsy_date,
          colonoscopic_biopsy_findings,
          pet_ct_findings,
          other_biopsy_date,
          other_biopsy_findings,
          medications,
          primary_treatment_plan,
          admission_date,
          surgery_plan_date,
          surgery_name,
          surgery_risks,
          consent_obtained,
          surgery_date,
          surgeon,
          assistant_surgeon,
          ot_findings,
          ot_procedure,
          hospital_course,
          complications,
          clavien_dindo_grade,
          discharge_date,
          discharge_medications,
          discharge_advice,
          next_follow_up_date,
          conservative_treatment,
          icu_stay,
          hospital_stay,
          plan_for_surgery,
          surgery_plan_date_conservative,
          final_biopsy,
          disease_stage,
          chemotherapy_radiotherapy,
          further_management_plan,
          medication_1,
          medication_2,
          medication_3,
          medication_4,
          medication_5,
          comorbidities,
          allergies,
          habits,
          current_medications,
          family_history,
          social_history,
          notes,
          is_pregnant,
          is_breastfeeding,
          has_insurance,
          insurance_provider,
          policy_number,
          profile_image_url,
          created_by,
          created_at,
          updated_at
        FROM patients 
        WHERE id = $1
      `;
            const result = await client.query(query, [
                id
            ]);
            if (result.rows.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Patient not found'
                }, {
                    status: 404
                });
            }
            const row = result.rows[0];
            // Transform data to match frontend expectations
            const patient = {
                id: row.id,
                fullName: `${row.first_name} ${row.middle_name ? row.middle_name + ' ' : ''}${row.last_name}`.trim(),
                patientId: row.id.substring(0, 8).toUpperCase(),
                firstName: row.first_name,
                middleName: row.middle_name,
                lastName: row.last_name,
                dateOfBirth: row.date_of_birth,
                age: row.date_of_birth ? new Date().getFullYear() - new Date(row.date_of_birth).getFullYear() : null,
                gender: row.gender,
                aadharNumber: row.aadhar_number,
                mobileNumber: row.mobile_number,
                alternatePhone: row.alternate_phone,
                email: row.email,
                instagramId: row.instagram_id,
                facebookId: row.facebook_id,
                referredBy: row.referred_by,
                houseNumber: row.house_number,
                village: row.village,
                postOffice: row.post_office,
                address: row.address,
                city: row.city,
                state: row.state,
                pincode: row.pincode,
                country: row.country,
                maritalStatus: row.marital_status,
                occupation: row.occupation,
                education: row.education,
                religion: row.religion,
                nationality: row.nationality,
                height: row.height,
                weight: row.weight,
                bmi: row.bmi,
                heightUnit: row.height_unit,
                weightUnit: row.weight_unit,
                bloodGroup: row.blood_group,
                charlsonIndex: row.charlson_index,
                asaGrade: row.asa_grade,
                ecogGrade: row.ecog_grade,
                dmStatus: row.dm_status,
                htnStatus: row.htn_status,
                hyperlipidemiaStatus: row.hyperlipidemia_status,
                hypothyroidStatus: row.hypothyroid_status,
                cardiacDiseaseStatus: row.cardiac_disease_status,
                pulmonaryDiseaseStatus: row.pulmonary_disease_status,
                neurologicalDiseaseStatus: row.neurological_disease_status,
                rheumatologicalDiseaseStatus: row.rheumatological_disease_status,
                otherDiseases: row.other_diseases,
                dateOfVisit: row.date_of_visit,
                primaryDisease: row.primary_disease,
                symptoms: row.symptoms,
                clinicalExamination: row.clinical_examination,
                investigations: row.investigations,
                finalDiagnosis: row.final_diagnosis,
                medications: row.medications,
                primaryTreatmentPlan: row.primary_treatment_plan,
                comorbidities: row.comorbidities,
                allergies: row.allergies,
                habits: row.habits,
                currentMedications: row.current_medications,
                familyHistory: row.family_history,
                socialHistory: row.social_history,
                notes: row.notes,
                isPregnant: row.is_pregnant,
                isBreastfeeding: row.is_breastfeeding,
                hasInsurance: row.has_insurance,
                insuranceProvider: row.insurance_provider,
                policyNumber: row.policy_number,
                profileImageUrl: row.profile_image_url,
                createdBy: row.created_by,
                createdAt: row.created_at,
                updatedAt: row.updated_at
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: patient
            });
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error fetching patient:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch patient'
        }, {
            status: 500
        });
    }
}
async function PUT(request, { params }) {
    try {
        const { id } = params;
        const patientData = await request.json();
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Patient ID is required'
            }, {
                status: 400
            });
        }
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pool"].connect();
        try {
            // Build dynamic update query based on provided fields
            const updateFields = [];
            const values = [];
            let paramCount = 1;
            // Add fields that can be updated
            const updatableFields = [
                'first_name',
                'middle_name',
                'last_name',
                'date_of_birth',
                'gender',
                'mobile_number',
                'email',
                'address',
                'height',
                'weight',
                'blood_group',
                'charlson_index',
                'asa_grade',
                'ecog_grade',
                'occupation',
                'marital_status',
                'city',
                'state',
                'pincode',
                'notes'
            ];
            for (const field of updatableFields){
                const camelCaseField = field.replace(/_([a-z])/g, (g)=>g[1].toUpperCase());
                if (patientData[camelCaseField] !== undefined) {
                    updateFields.push(`${field} = $${paramCount}`);
                    values.push(patientData[camelCaseField]);
                    paramCount++;
                }
            }
            if (updateFields.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'No valid fields to update'
                }, {
                    status: 400
                });
            }
            // Add updated_at
            updateFields.push(`updated_at = $${paramCount}`);
            values.push(new Date().toISOString());
            paramCount++;
            // Add ID for WHERE clause
            values.push(id);
            const query = `
        UPDATE patients 
        SET ${updateFields.join(', ')}
        WHERE id = $${paramCount}
        RETURNING *
      `;
            const result = await client.query(query, values);
            if (result.rows.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Patient not found'
                }, {
                    status: 404
                });
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: result.rows[0],
                message: 'Patient updated successfully'
            });
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error updating patient:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to update patient'
        }, {
            status: 500
        });
    }
}
async function DELETE(request, { params }) {
    try {
        const { id } = params;
        if (!id) {
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                error: 'Patient ID is required'
            }, {
                status: 400
            });
        }
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pool"].connect();
        try {
            const result = await client.query('DELETE FROM patients WHERE id = $1 RETURNING id', [
                id
            ]);
            if (result.rows.length === 0) {
                return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                    error: 'Patient not found'
                }, {
                    status: 404
                });
            }
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                message: 'Patient deleted successfully'
            });
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error deleting patient:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to delete patient'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__26c5edd3._.js.map