{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/lib/database.ts"], "sourcesContent": ["// Database connection using direct PostgreSQL connection\nimport { Pool } from 'pg';\n\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: {\n    rejectUnauthorized: false\n  }\n});\n\nexport { pool };\n\n// Database helper functions\nexport const db = {\n  query: async (text: string, params?: any[]) => {\n    const client = await pool.connect();\n    try {\n      const result = await client.query(text, params);\n      return result;\n    } finally {\n      client.release();\n    }\n  },\n  \n  getClient: async () => {\n    return await pool.connect();\n  }\n};\n\n// Test database connection\nexport const testConnection = async () => {\n  try {\n    const result = await db.query('SELECT NOW()');\n    console.log('Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AACzD;;;;;;AAEA,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK;QACH,oBAAoB;IACtB;AACF;;AAKO,MAAM,KAAK;IAChB,OAAO,OAAO,MAAc;QAC1B,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,MAAM;YACxC,OAAO;QACT,SAAU;YACR,OAAO,OAAO;QAChB;IACF;IAEA,WAAW;QACT,OAAO,MAAM,KAAK,OAAO;IAC3B;AACF;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,SAAS,MAAM,GAAG,KAAK,CAAC;QAC9B,QAAQ,GAAG,CAAC,oCAAoC,OAAO,IAAI,CAAC,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/api/dashboard/stats/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { pool } from '@/lib/database';\n\nexport async function GET(request: NextRequest) {\n  try {\n    const client = await pool.connect();\n    \n    try {\n      // Get total patients\n      const totalPatientsResult = await client.query('SELECT COUNT(*) FROM patients');\n      const totalPatients = parseInt(totalPatientsResult.rows[0].count);\n\n      // Get new patients this month\n      const newPatientsResult = await client.query(`\n        SELECT COUNT(*) FROM patients \n        WHERE created_at >= date_trunc('month', CURRENT_DATE)\n      `);\n      const newPatientsThisMonth = parseInt(newPatientsResult.rows[0].count);\n\n      // Get gender distribution\n      const genderResult = await client.query(`\n        SELECT gender, COUNT(*) as count \n        FROM patients \n        GROUP BY gender\n      `);\n      \n      const genderDistribution = {\n        male: 0,\n        female: 0,\n        other: 0\n      };\n      \n      genderResult.rows.forEach(row => {\n        const gender = row.gender.toLowerCase();\n        if (gender === 'male') genderDistribution.male = parseInt(row.count);\n        else if (gender === 'female') genderDistribution.female = parseInt(row.count);\n        else genderDistribution.other = parseInt(row.count);\n      });\n\n      // Get blood group distribution\n      const bloodGroupResult = await client.query(`\n        SELECT blood_group, COUNT(*) as count \n        FROM patients \n        WHERE blood_group IS NOT NULL\n        GROUP BY blood_group\n        ORDER BY count DESC\n      `);\n      \n      const bloodGroupDistribution: { [key: string]: number } = {};\n      bloodGroupResult.rows.forEach(row => {\n        bloodGroupDistribution[row.blood_group] = parseInt(row.count);\n      });\n\n      // Get average age\n      const avgAgeResult = await client.query(`\n        SELECT AVG(EXTRACT(YEAR FROM AGE(date_of_birth))) as avg_age \n        FROM patients \n        WHERE date_of_birth IS NOT NULL\n      `);\n      const averageAge = Math.round(parseFloat(avgAgeResult.rows[0].avg_age || '0'));\n\n      // Get BMI distribution\n      const bmiResult = await client.query(`\n        SELECT \n          COUNT(CASE WHEN bmi < 18.5 THEN 1 END) as underweight,\n          COUNT(CASE WHEN bmi >= 18.5 AND bmi < 25 THEN 1 END) as normal,\n          COUNT(CASE WHEN bmi >= 25 AND bmi < 30 THEN 1 END) as overweight,\n          COUNT(CASE WHEN bmi >= 30 THEN 1 END) as obese\n        FROM patients \n        WHERE bmi IS NOT NULL\n      `);\n      \n      const bmiDistribution = {\n        underweight: parseInt(bmiResult.rows[0].underweight || '0'),\n        normal: parseInt(bmiResult.rows[0].normal || '0'),\n        overweight: parseInt(bmiResult.rows[0].overweight || '0'),\n        obese: parseInt(bmiResult.rows[0].obese || '0')\n      };\n\n      // Get recent patients for activity\n      const recentPatientsResult = await client.query(`\n        SELECT first_name, last_name, created_at\n        FROM patients \n        ORDER BY created_at DESC \n        LIMIT 5\n      `);\n\n      const recentActivity = recentPatientsResult.rows.map(row => ({\n        action: 'New patient registered',\n        patient: `${row.first_name} ${row.last_name}`,\n        time: new Date(row.created_at).toLocaleString()\n      }));\n\n      const stats = {\n        totalPatients,\n        newPatientsThisMonth,\n        averageAge,\n        genderDistribution,\n        bloodGroupDistribution,\n        bmiDistribution,\n        recentActivity,\n        // Mock data for other stats\n        appointments: 156,\n        criticalCases: 4\n      };\n\n      return NextResponse.json({\n        success: true,\n        data: stats\n      });\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Error fetching dashboard stats:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch dashboard statistics' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,qBAAqB;YACrB,MAAM,sBAAsB,MAAM,OAAO,KAAK,CAAC;YAC/C,MAAM,gBAAgB,SAAS,oBAAoB,IAAI,CAAC,EAAE,CAAC,KAAK;YAEhE,8BAA8B;YAC9B,MAAM,oBAAoB,MAAM,OAAO,KAAK,CAAC,CAAC;;;MAG9C,CAAC;YACD,MAAM,uBAAuB,SAAS,kBAAkB,IAAI,CAAC,EAAE,CAAC,KAAK;YAErE,0BAA0B;YAC1B,MAAM,eAAe,MAAM,OAAO,KAAK,CAAC,CAAC;;;;MAIzC,CAAC;YAED,MAAM,qBAAqB;gBACzB,MAAM;gBACN,QAAQ;gBACR,OAAO;YACT;YAEA,aAAa,IAAI,CAAC,OAAO,CAAC,CAAA;gBACxB,MAAM,SAAS,IAAI,MAAM,CAAC,WAAW;gBACrC,IAAI,WAAW,QAAQ,mBAAmB,IAAI,GAAG,SAAS,IAAI,KAAK;qBAC9D,IAAI,WAAW,UAAU,mBAAmB,MAAM,GAAG,SAAS,IAAI,KAAK;qBACvE,mBAAmB,KAAK,GAAG,SAAS,IAAI,KAAK;YACpD;YAEA,+BAA+B;YAC/B,MAAM,mBAAmB,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;;MAM7C,CAAC;YAED,MAAM,yBAAoD,CAAC;YAC3D,iBAAiB,IAAI,CAAC,OAAO,CAAC,CAAA;gBAC5B,sBAAsB,CAAC,IAAI,WAAW,CAAC,GAAG,SAAS,IAAI,KAAK;YAC9D;YAEA,kBAAkB;YAClB,MAAM,eAAe,MAAM,OAAO,KAAK,CAAC,CAAC;;;;MAIzC,CAAC;YACD,MAAM,aAAa,KAAK,KAAK,CAAC,WAAW,aAAa,IAAI,CAAC,EAAE,CAAC,OAAO,IAAI;YAEzE,uBAAuB;YACvB,MAAM,YAAY,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;;;;MAQtC,CAAC;YAED,MAAM,kBAAkB;gBACtB,aAAa,SAAS,UAAU,IAAI,CAAC,EAAE,CAAC,WAAW,IAAI;gBACvD,QAAQ,SAAS,UAAU,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI;gBAC7C,YAAY,SAAS,UAAU,IAAI,CAAC,EAAE,CAAC,UAAU,IAAI;gBACrD,OAAO,SAAS,UAAU,IAAI,CAAC,EAAE,CAAC,KAAK,IAAI;YAC7C;YAEA,mCAAmC;YACnC,MAAM,uBAAuB,MAAM,OAAO,KAAK,CAAC,CAAC;;;;;MAKjD,CAAC;YAED,MAAM,iBAAiB,qBAAqB,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBAC3D,QAAQ;oBACR,SAAS,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,SAAS,EAAE;oBAC7C,MAAM,IAAI,KAAK,IAAI,UAAU,EAAE,cAAc;gBAC/C,CAAC;YAED,MAAM,QAAQ;gBACZ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA,4BAA4B;gBAC5B,cAAc;gBACd,eAAe;YACjB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QAEF,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAuC,GAChD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}