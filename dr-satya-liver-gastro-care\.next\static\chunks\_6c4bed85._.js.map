{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: 'from-blue-500 to-blue-600',\n    green: 'from-green-500 to-green-600',\n    amber: 'from-amber-500 to-amber-600',\n    red: 'from-red-500 to-red-600',\n    teal: 'from-teal-500 to-teal-600'\n  };\n\n  return (\n    <Card className={`relative overflow-hidden ${className}`} gradient>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex-1\">\n          <p className=\"text-sm font-medium text-gray-600 mb-1\">{title}</p>\n          <p className=\"text-3xl font-bold text-gray-900 mb-1\">{value}</p>\n          {subtitle && (\n            <p className=\"text-sm text-gray-500\">{subtitle}</p>\n          )}\n          {trend && (\n            <div className=\"flex items-center mt-2\">\n              <span className={`text-sm font-medium ${\n                trend.isPositive ? 'text-green-600' : 'text-red-600'\n              }`}>\n                {trend.isPositive ? '↗' : '↘'} {Math.abs(trend.value)}%\n              </span>\n              <span className=\"text-xs text-gray-500 ml-1\">vs last month</span>\n            </div>\n          )}\n        </div>\n        {icon && (\n          <div className={`p-3 rounded-lg bg-gradient-to-r ${colorClasses[color]} text-white`}>\n            {icon}\n          </div>\n        )}\n      </div>\n    </Card>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <Card \n      className={`cursor-pointer ${className}`}\n      onClick={() => onClick?.(patient)}\n      hover\n    >\n      <div className=\"flex items-center space-x-4\">\n        {/* Avatar */}\n        <div className=\"relative\">\n          {patient.profileImageUrl ? (\n            <img\n              src={patient.profileImageUrl}\n              alt={patient.fullName}\n              className=\"w-12 h-12 rounded-full object-cover\"\n            />\n          ) : (\n            <div className=\"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white font-semibold\">\n              {getInitials(patient.fullName)}\n            </div>\n          )}\n          <div className=\"absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white\" />\n        </div>\n\n        {/* Patient Info */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-center justify-between\">\n            <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n              {patient.fullName}\n            </h3>\n            <span className=\"text-xs font-medium text-blue-600 bg-blue-100 px-2 py-1 rounded-full\">\n              {patient.patientId}\n            </span>\n          </div>\n          \n          <div className=\"flex items-center space-x-4 mt-1 text-sm text-gray-500\">\n            <span>{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n            <span>•</span>\n            <span>{patient.gender}</span>\n            <span>•</span>\n            <span>{patient.mobileNumber}</span>\n          </div>\n          \n          {patient.lastVisit && (\n            <p className=\"text-xs text-gray-400 mt-1\">\n              Last visit: {patient.lastVisit}\n            </p>\n          )}\n        </div>\n\n        {/* Action Arrow */}\n        <div className=\"text-gray-400\">\n          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n          </svg>\n        </div>\n      </div>\n    </Card>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;;;AAHA;;;AAeA,MAAM,OAA4B;QAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,AAAC,4BAGjB,OADA,cAAc,CAAC,QAAQ,EAAC,WAExB,OADA,aAAa,CAAC,OAAO,EAAC,UAEtB,OADA,WAAW,4CAA4C,YAAW,UAElE,OADA,UAAU,mBAAmB,IAAG,UACtB,OAAV,WAAU;IAGd,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;KApDM;AAqEC,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;QACN,OAAO;QACP,OAAO;QACP,KAAK;QACL,MAAM;IACR;IAEA,qBACE,6LAAC;QAAK,WAAW,AAAC,4BAAqC,OAAV;QAAa,QAAQ;kBAChE,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAyC;;;;;;wBACrD,0BACC,6LAAC;4BAAE,WAAU;sCAAyB;;;;;;wBAEvC,uBACC,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAK,WAAW,AAAC,uBAEjB,OADC,MAAM,UAAU,GAAG,mBAAmB;;wCAErC,MAAM,UAAU,GAAG,MAAM;wCAAI;wCAAE,KAAK,GAAG,CAAC,MAAM,KAAK;wCAAE;;;;;;;8CAExD,6LAAC;oCAAK,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;gBAIlD,sBACC,6LAAC;oBAAI,WAAW,AAAC,mCAAsD,OAApB,YAAY,CAAC,MAAM,EAAC;8BACpE;;;;;;;;;;;;;;;;;AAMb;MA7Ca;AA8DN,MAAM,cAA0C;QAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,6LAAC;QACC,WAAW,AAAC,kBAA2B,OAAV;QAC7B,SAAS,IAAM,oBAAA,8BAAA,QAAU;QACzB,KAAK;kBAEL,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;wBACZ,QAAQ,eAAe,iBACtB,6LAAC;4BACC,KAAK,QAAQ,eAAe;4BAC5B,KAAK,QAAQ,QAAQ;4BACrB,WAAU;;;;;qFAGZ,6LAAC;4BAAI,WAAU;sCACZ,YAAY,QAAQ,QAAQ;;;;;;sCAGjC,6LAAC;4BAAI,WAAU;;;;;;;;;;;;8BAIjB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CACX,QAAQ,QAAQ;;;;;;8CAEnB,6LAAC;oCAAK,WAAU;8CACb,QAAQ,SAAS;;;;;;;;;;;;sCAItB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAM,QAAQ,GAAG,GAAG,AAAC,GAAc,OAAZ,QAAQ,GAAG,EAAC,YAAU;;;;;;8CAC9C,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAM,QAAQ,MAAM;;;;;;8CACrB,6LAAC;8CAAK;;;;;;8CACN,6LAAC;8CAAM,QAAQ,YAAY;;;;;;;;;;;;wBAG5B,QAAQ,SAAS,kBAChB,6LAAC;4BAAE,WAAU;;gCAA6B;gCAC3B,QAAQ,SAAS;;;;;;;;;;;;;8BAMpC,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjF;MAxEa;AAmFN,MAAM,WAAoC;QAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,6JAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,6LAAC;QAAK,WAAW;;0BACf,6LAAC;gBACC,WAAW,AAAC,qCAAwE,OAApC,cAAc,mBAAmB;gBACjF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,6LAAC;wBAAI,WAAU;;4BACZ,sBACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,6LAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,6LAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;GAnDa;MAAA;uCAqDE", "debugId": null}}, {"offset": {"line": 462, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserGroupIcon,\n  UserPlusIcon,\n  ChartBarIcon,\n  HeartIcon\n} from '@heroicons/react/24/outline';\nimport { StatCard } from '@/components/ui/Card';\nimport { useAuthStore } from '@/store';\nimport { toast } from 'react-hot-toast';\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuthStore();\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/dashboard/stats');\n        const data = await response.json();\n\n        if (data.success) {\n          setStats(data.data);\n        } else {\n          toast.error('Failed to fetch dashboard statistics');\n        }\n      } catch (error) {\n        console.error('Error fetching stats:', error);\n        toast.error('Failed to fetch dashboard statistics');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  const statCards = stats ? [\n    {\n      title: 'Total Patients',\n      value: stats.totalPatients.toLocaleString(),\n      subtitle: 'Active patients',\n      icon: <UserGroupIcon className=\"w-6 h-6\" />,\n      trend: { value: 12, isPositive: true },\n      color: 'blue' as const,\n    },\n    {\n      title: 'New Patients',\n      value: stats.newPatientsThisMonth.toString(),\n      subtitle: 'This month',\n      icon: <UserPlusIcon className=\"w-6 h-6\" />,\n      trend: { value: 8, isPositive: true },\n      color: 'green' as const,\n    },\n    {\n      title: 'Appointments',\n      value: stats.appointments.toString(),\n      subtitle: 'This week',\n      icon: <ChartBarIcon className=\"w-6 h-6\" />,\n      trend: { value: 3, isPositive: false },\n      color: 'amber' as const,\n    },\n    {\n      title: 'Critical Cases',\n      value: stats.criticalCases.toString(),\n      subtitle: 'Require attention',\n      icon: <HeartIcon className=\"w-6 h-6\" />,\n      trend: { value: 2, isPositive: false },\n      color: 'red' as const,\n    },\n  ] : [];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <motion.div\n      className=\"space-y-8\"\n      variants={containerVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n    >\n      {/* Header */}\n      <motion.div variants={itemVariants}>\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">\n          Welcome back, {user ? `${user.firstName} ${user.lastName}` : 'Doctor'}\n        </h1>\n        <p className=\"text-gray-600\">\n          Here's what's happening with your patients today.\n        </p>\n      </motion.div>\n\n      {/* Stats Grid */}\n      <motion.div\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n        variants={containerVariants}\n      >\n        {loading ? (\n          // Loading skeleton\n          Array.from({ length: 4 }).map((_, index) => (\n            <motion.div key={index} variants={itemVariants}>\n              <div className=\"medical-card p-6 animate-pulse\">\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex-1\">\n                    <div className=\"h-4 bg-gray-200 rounded w-1/2 mb-2\" />\n                    <div className=\"h-8 bg-gray-200 rounded w-3/4 mb-2\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-1/3\" />\n                  </div>\n                  <div className=\"w-12 h-12 bg-gray-200 rounded-lg\" />\n                </div>\n              </div>\n            </motion.div>\n          ))\n        ) : (\n          statCards.map((stat, index) => (\n            <motion.div key={stat.title} variants={itemVariants}>\n              <StatCard {...stat} />\n            </motion.div>\n          ))\n        )}\n      </motion.div>\n\n      {/* Quick Actions */}\n      <motion.div variants={itemVariants}>\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n          Quick Actions\n        </h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n          <motion.button\n            className=\"medical-card p-6 text-left hover:shadow-lg transition-all duration-200\"\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <UserPlusIcon className=\"w-8 h-8 text-blue-600 mb-3\" />\n            <h3 className=\"font-semibold text-gray-900 mb-1\">Add New Patient</h3>\n            <p className=\"text-sm text-gray-600\">Register a new patient in the system</p>\n          </motion.button>\n\n          <motion.button\n            className=\"medical-card p-6 text-left hover:shadow-lg transition-all duration-200\"\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <UserGroupIcon className=\"w-8 h-8 text-green-600 mb-3\" />\n            <h3 className=\"font-semibold text-gray-900 mb-1\">View All Patients</h3>\n            <p className=\"text-sm text-gray-600\">Browse and search patient records</p>\n          </motion.button>\n\n          <motion.button\n            className=\"medical-card p-6 text-left hover:shadow-lg transition-all duration-200\"\n            whileHover={{ scale: 1.02 }}\n            whileTap={{ scale: 0.98 }}\n          >\n            <ChartBarIcon className=\"w-8 h-8 text-purple-600 mb-3\" />\n            <h3 className=\"font-semibold text-gray-900 mb-1\">View Analytics</h3>\n            <p className=\"text-sm text-gray-600\">Check medical reports and insights</p>\n          </motion.button>\n        </div>\n      </motion.div>\n\n      {/* Recent Activity */}\n      <motion.div variants={itemVariants}>\n        <h2 className=\"text-xl font-semibold text-gray-900 mb-4\">\n          Recent Activity\n        </h2>\n        <div className=\"medical-card p-6\">\n          <div className=\"space-y-4\">\n            {loading ? (\n              // Loading skeleton for recent activity\n              Array.from({ length: 3 }).map((_, index) => (\n                <div key={index} className=\"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0 animate-pulse\">\n                  <div className=\"flex-1\">\n                    <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\" />\n                    <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n                  </div>\n                  <div className=\"h-3 bg-gray-200 rounded w-16\" />\n                </div>\n              ))\n            ) : (\n              stats?.recentActivity?.map((activity: any, index: number) => (\n                <motion.div\n                  key={index}\n                  className=\"flex items-center justify-between py-3 border-b border-gray-100 last:border-b-0\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  transition={{ delay: index * 0.1 }}\n                >\n                  <div>\n                    <p className=\"font-medium text-gray-900\">{activity.action}</p>\n                    <p className=\"text-sm text-gray-600\">Patient: {activity.patient}</p>\n                  </div>\n                  <span className=\"text-sm text-gray-500\">{activity.time}</span>\n                </motion.div>\n              )) || (\n                <p className=\"text-gray-500 text-center py-4\">No recent activity</p>\n              )\n            )}\n          </div>\n        </div>\n      </motion.div>\n    </motion.div>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;;;AAZA;;;;;;;AAcA,MAAM,gBAA0B;QAqLlB;;IApLZ,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM;sDAAa;oBACjB,IAAI;wBACF,MAAM,WAAW,MAAM,MAAM;wBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;wBAEhC,IAAI,KAAK,OAAO,EAAE;4BAChB,SAAS,KAAK,IAAI;wBACpB,OAAO;4BACL,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;wBACd;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yBAAyB;wBACvC,0JAAA,CAAA,QAAK,CAAC,KAAK,CAAC;oBACd,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;kCAAG,EAAE;IAEL,MAAM,YAAY,QAAQ;QACxB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,cAAc;YACzC,UAAU;YACV,oBAAM,6LAAC,4NAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;gBAAE,OAAO;gBAAI,YAAY;YAAK;YACrC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,oBAAoB,CAAC,QAAQ;YAC1C,UAAU;YACV,oBAAM,6LAAC,0NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAK;YACpC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,YAAY,CAAC,QAAQ;YAClC,UAAU;YACV,oBAAM,6LAAC,0NAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAM;YACrC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,UAAU;YACV,oBAAM,6LAAC,oNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAM;YACrC,OAAO;QACT;KACD,GAAG,EAAE;IAEN,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,UAAU;QACV,SAAQ;QACR,SAAQ;;0BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;;4BAAwC;4BACrC,OAAO,AAAC,GAAoB,OAAlB,KAAK,SAAS,EAAC,KAAiB,OAAd,KAAK,QAAQ,IAAK;;;;;;;kCAE/D,6LAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;0BAET,UACC,mBAAmB;gBACnB,MAAM,IAAI,CAAC;oBAAE,QAAQ;gBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAa,UAAU;kCAChC,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;0DACf,6LAAC;gDAAI,WAAU;;;;;;;;;;;;kDAEjB,6LAAC;wCAAI,WAAU;;;;;;;;;;;;;;;;;uBARJ;;;;oEAcnB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACnB,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;wBAAkB,UAAU;kCACrC,cAAA,6LAAC,mIAAA,CAAA,WAAQ;4BAAE,GAAG,IAAI;;;;;;uBADH,KAAK,KAAK;;;;;;;;;;0BAQjC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC,4NAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;kDACzB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gCACZ,WAAU;gCACV,YAAY;oCAAE,OAAO;gCAAK;gCAC1B,UAAU;oCAAE,OAAO;gCAAK;;kDAExB,6LAAC,0NAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;kDACxB,6LAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAM3C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,UAAU;;kCACpB,6LAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ,UACC,uCAAuC;4BACvC,MAAM,IAAI,CAAC;gCAAE,QAAQ;4BAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,6LAAC;oCAAgB,WAAU;;sDACzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;;;;;;;;;;;;sDAEjB,6LAAC;4CAAI,WAAU;;;;;;;mCALP;;;;gFASZ,CAAA,kBAAA,6BAAA,wBAAA,MAAO,cAAc,cAArB,4CAAA,sBAAuB,GAAG,CAAC,CAAC,UAAe,sBACzC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAI;;sDAEjC,6LAAC;;8DACC,6LAAC;oDAAE,WAAU;8DAA6B,SAAS,MAAM;;;;;;8DACzD,6LAAC;oDAAE,WAAU;;wDAAwB;wDAAU,SAAS,OAAO;;;;;;;;;;;;;sDAEjE,6LAAC;4CAAK,WAAU;sDAAyB,SAAS,IAAI;;;;;;;mCAVjD;;;;gGAaP,6LAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9D;GA5MM;;QACa,wHAAA,CAAA,eAAY;;;KADzB;uCA8MS", "debugId": null}}, {"offset": {"line": 1019, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/node_modules/%40heroicons/react/24/outline/esm/HeartIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction HeartIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(HeartIcon);\nexport default ForwardRef;"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS,UAAU,KAIlB,EAAE,MAAM;QAJU,EACjB,KAAK,EACL,OAAO,EACP,GAAG,OACJ,GAJkB;IAKjB,OAAO,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,OAAO,OAAO,MAAM,CAAC;QAC3D,OAAO;QACP,MAAM;QACN,SAAS;QACT,aAAa;QACb,QAAQ;QACR,eAAe;QACf,aAAa;QACb,KAAK;QACL,mBAAmB;IACrB,GAAG,QAAQ,QAAQ,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,SAAS;QAC3D,IAAI;IACN,GAAG,SAAS,MAAM,WAAW,GAAE,6JAAA,CAAA,gBAAmB,CAAC,QAAQ;QACzD,eAAe;QACf,gBAAgB;QAChB,GAAG;IACL;AACF;AACA,MAAM,aAAa,WAAW,GAAG,6JAAA,CAAA,aAAgB,CAAC;uCACnC", "ignoreList": [0], "debugId": null}}]}