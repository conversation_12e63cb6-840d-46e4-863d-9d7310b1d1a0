'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  className?: string;
}

const Logo: React.FC<LogoProps> = ({ 
  size = 'md', 
  showText = true, 
  className = '' 
}) => {
  const sizeClasses = {
    sm: 'w-8 h-8',
    md: 'w-12 h-12',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-lg',
    lg: 'text-xl',
    xl: 'text-3xl'
  };

  return (
    <motion.div 
      className={`flex items-center gap-3 ${className}`}
      initial={{ opacity: 0, scale: 0.8 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5, ease: "easeOut" }}
    >
      {/* Medical Logo Icon */}
      <motion.div 
        className={`${sizeClasses[size]} relative`}
        whileHover={{ scale: 1.05 }}
        transition={{ type: "spring", stiffness: 300 }}
      >
        <svg
          viewBox="0 0 100 100"
          className="w-full h-full"
          xmlns="http://www.w3.org/2000/svg"
        >
          {/* Background Circle with Gradient */}
          <defs>
            <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#1e40af" />
              <stop offset="50%" stopColor="#3b82f6" />
              <stop offset="100%" stopColor="#0d9488" />
            </linearGradient>
            <linearGradient id="liverGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#059669" />
              <stop offset="100%" stopColor="#0d9488" />
            </linearGradient>
          </defs>
          
          {/* Main Circle Background */}
          <circle
            cx="50"
            cy="50"
            r="48"
            fill="url(#logoGradient)"
            stroke="#ffffff"
            strokeWidth="2"
          />
          
          {/* Medical Cross */}
          <g transform="translate(50, 50)">
            {/* Vertical bar of cross */}
            <rect
              x="-3"
              y="-20"
              width="6"
              height="40"
              fill="#ffffff"
              rx="3"
            />
            {/* Horizontal bar of cross */}
            <rect
              x="-20"
              y="-3"
              width="40"
              height="6"
              fill="#ffffff"
              rx="3"
            />
          </g>
          
          {/* Liver Shape (stylized) */}
          <g transform="translate(50, 50)">
            <path
              d="M -15 -8 Q -20 -12 -15 -16 Q -10 -18 -5 -16 Q 0 -14 5 -16 Q 10 -18 15 -16 Q 20 -12 15 -8 Q 12 -4 8 0 Q 5 4 0 6 Q -5 4 -8 0 Q -12 -4 -15 -8 Z"
              fill="url(#liverGradient)"
              opacity="0.3"
            />
          </g>
          
          {/* Stethoscope accent */}
          <g transform="translate(50, 50)">
            <circle cx="18" cy="18" r="4" fill="#ffffff" opacity="0.8" />
            <path
              d="M 14 14 Q 10 10 5 12 Q 0 14 -5 12 Q -10 10 -14 14"
              stroke="#ffffff"
              strokeWidth="2"
              fill="none"
              opacity="0.8"
            />
          </g>
        </svg>
      </motion.div>

      {/* Text Logo */}
      {showText && (
        <motion.div 
          className="flex flex-col"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.h1 
            className={`font-bold text-gray-800 leading-tight ${textSizeClasses[size]}`}
            whileHover={{ scale: 1.02 }}
          >
            Dr Satya's
          </motion.h1>
          <motion.p 
            className={`font-medium text-blue-600 leading-tight ${
              size === 'sm' ? 'text-xs' : 
              size === 'md' ? 'text-sm' : 
              size === 'lg' ? 'text-base' : 'text-lg'
            }`}
            whileHover={{ scale: 1.02 }}
          >
            Liver & Gastro Care
          </motion.p>
          {size === 'lg' || size === 'xl' ? (
            <motion.p 
              className="text-xs text-gray-500 mt-1"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.4 }}
            >
              Excellence in Digestive Health
            </motion.p>
          ) : null}
        </motion.div>
      )}
    </motion.div>
  );
};

export default Logo;
