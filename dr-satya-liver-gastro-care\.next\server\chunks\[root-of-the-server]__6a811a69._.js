module.exports = {

"[project]/.next-internal/server/app/api/dashboard/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/shared/lib/no-fallback-error.external.js [external] (next/dist/shared/lib/no-fallback-error.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/shared/lib/no-fallback-error.external.js", () => require("next/dist/shared/lib/no-fallback-error.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": ((__turbopack_context__) => {

var { m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/pg [external] (pg, esm_import)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
const mod = await __turbopack_context__.y("pg");

__turbopack_context__.n(mod);
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, true);}),
"[project]/src/lib/database.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
// Database connection using direct PostgreSQL connection
__turbopack_context__.s({
    "db": ()=>db,
    "pool": ()=>pool,
    "testConnection": ()=>testConnection
});
var __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__ = __turbopack_context__.i("[externals]/pg [external] (pg, esm_import)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__
]);
[__TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
const pool = new __TURBOPACK__imported__module__$5b$externals$5d2f$pg__$5b$external$5d$__$28$pg$2c$__esm_import$29$__["Pool"]({
    connectionString: process.env.DATABASE_URL,
    ssl: {
        rejectUnauthorized: false
    }
});
;
const db = {
    query: async (text, params)=>{
        const client = await pool.connect();
        try {
            const result = await client.query(text, params);
            return result;
        } finally{
            client.release();
        }
    },
    getClient: async ()=>{
        return await pool.connect();
    }
};
const testConnection = async ()=>{
    try {
        const result = await db.query('SELECT NOW()');
        console.log('Database connected successfully:', result.rows[0]);
        return true;
    } catch (error) {
        console.error('Database connection failed:', error);
        return false;
    }
};
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),
"[project]/src/app/api/dashboard/stats/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { a: __turbopack_async_module__ } = __turbopack_context__;
__turbopack_async_module__(async (__turbopack_handle_async_dependencies__, __turbopack_async_result__) => { try {
__turbopack_context__.s({
    "GET": ()=>GET
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/server.js [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/database.ts [app-route] (ecmascript)");
var __turbopack_async_dependencies__ = __turbopack_handle_async_dependencies__([
    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__
]);
[__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__] = __turbopack_async_dependencies__.then ? (await __turbopack_async_dependencies__)() : __turbopack_async_dependencies__;
;
;
async function GET(request) {
    try {
        const client = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$database$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["pool"].connect();
        try {
            // Get total patients
            const totalPatientsResult = await client.query('SELECT COUNT(*) FROM patients');
            const totalPatients = parseInt(totalPatientsResult.rows[0].count);
            // Get new patients this month
            const newPatientsResult = await client.query(`
        SELECT COUNT(*) FROM patients 
        WHERE created_at >= date_trunc('month', CURRENT_DATE)
      `);
            const newPatientsThisMonth = parseInt(newPatientsResult.rows[0].count);
            // Get gender distribution
            const genderResult = await client.query(`
        SELECT gender, COUNT(*) as count 
        FROM patients 
        GROUP BY gender
      `);
            const genderDistribution = {
                male: 0,
                female: 0,
                other: 0
            };
            genderResult.rows.forEach((row)=>{
                const gender = row.gender.toLowerCase();
                if (gender === 'male') genderDistribution.male = parseInt(row.count);
                else if (gender === 'female') genderDistribution.female = parseInt(row.count);
                else genderDistribution.other = parseInt(row.count);
            });
            // Get blood group distribution
            const bloodGroupResult = await client.query(`
        SELECT blood_group, COUNT(*) as count 
        FROM patients 
        WHERE blood_group IS NOT NULL
        GROUP BY blood_group
        ORDER BY count DESC
      `);
            const bloodGroupDistribution = {};
            bloodGroupResult.rows.forEach((row)=>{
                bloodGroupDistribution[row.blood_group] = parseInt(row.count);
            });
            // Get average age
            const avgAgeResult = await client.query(`
        SELECT AVG(EXTRACT(YEAR FROM AGE(date_of_birth))) as avg_age 
        FROM patients 
        WHERE date_of_birth IS NOT NULL
      `);
            const averageAge = Math.round(parseFloat(avgAgeResult.rows[0].avg_age || '0'));
            // Get BMI distribution
            const bmiResult = await client.query(`
        SELECT 
          COUNT(CASE WHEN bmi < 18.5 THEN 1 END) as underweight,
          COUNT(CASE WHEN bmi >= 18.5 AND bmi < 25 THEN 1 END) as normal,
          COUNT(CASE WHEN bmi >= 25 AND bmi < 30 THEN 1 END) as overweight,
          COUNT(CASE WHEN bmi >= 30 THEN 1 END) as obese
        FROM patients 
        WHERE bmi IS NOT NULL
      `);
            const bmiDistribution = {
                underweight: parseInt(bmiResult.rows[0].underweight || '0'),
                normal: parseInt(bmiResult.rows[0].normal || '0'),
                overweight: parseInt(bmiResult.rows[0].overweight || '0'),
                obese: parseInt(bmiResult.rows[0].obese || '0')
            };
            // Get recent patients for activity
            const recentPatientsResult = await client.query(`
        SELECT first_name, last_name, created_at
        FROM patients 
        ORDER BY created_at DESC 
        LIMIT 5
      `);
            const recentActivity = recentPatientsResult.rows.map((row)=>({
                    action: 'New patient registered',
                    patient: `${row.first_name} ${row.last_name}`,
                    time: new Date(row.created_at).toLocaleString()
                }));
            const stats = {
                totalPatients,
                newPatientsThisMonth,
                averageAge,
                genderDistribution,
                bloodGroupDistribution,
                bmiDistribution,
                recentActivity,
                // Mock data for other stats
                appointments: 156,
                criticalCases: 4
            };
            return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
                success: true,
                data: stats
            });
        } finally{
            client.release();
        }
    } catch (error) {
        console.error('Error fetching dashboard stats:', error);
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            error: 'Failed to fetch dashboard statistics'
        }, {
            status: 500
        });
    }
}
__turbopack_async_result__();
} catch(e) { __turbopack_async_result__(e); } }, false);}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__6a811a69._.js.map