{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface CardProps {\n  children: React.ReactNode;\n  className?: string;\n  hover?: boolean;\n  padding?: 'sm' | 'md' | 'lg';\n  shadow?: 'sm' | 'md' | 'lg';\n  onClick?: () => void;\n  gradient?: boolean;\n}\n\nconst Card: React.FC<CardProps> = ({\n  children,\n  className = '',\n  hover = true,\n  padding = 'md',\n  shadow = 'md',\n  onClick,\n  gradient = false\n}) => {\n  const paddingClasses = {\n    sm: 'p-4',\n    md: 'p-6',\n    lg: 'p-8'\n  };\n\n  const shadowClasses = {\n    sm: 'shadow-sm',\n    md: 'shadow-md',\n    lg: 'shadow-lg'\n  };\n\n  const baseClasses = `\n    medical-card \n    ${paddingClasses[padding]} \n    ${shadowClasses[shadow]}\n    ${gradient ? 'bg-gradient-to-br from-white to-blue-50' : 'bg-white'}\n    ${onClick ? 'cursor-pointer' : ''}\n    ${className}\n  `;\n\n  const cardVariants = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    hover: hover ? { \n      y: -4, \n      boxShadow: '0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)' \n    } : {}\n  };\n\n  return (\n    <motion.div\n      className={baseClasses}\n      variants={cardVariants}\n      initial=\"initial\"\n      animate=\"animate\"\n      whileHover=\"hover\"\n      transition={{ duration: 0.2, ease: \"easeOut\" }}\n      onClick={onClick}\n    >\n      {children}\n    </motion.div>\n  );\n};\n\n// Specialized Card Components\n\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  subtitle?: string;\n  icon?: React.ReactNode;\n  trend?: {\n    value: number;\n    isPositive: boolean;\n  };\n  color?: 'blue' | 'green' | 'amber' | 'red' | 'teal';\n  className?: string;\n}\n\nexport const StatCard: React.FC<StatCardProps> = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  trend,\n  color = 'blue',\n  className = ''\n}) => {\n  const colorClasses = {\n    blue: {\n      gradient: 'from-blue-500 via-blue-600 to-blue-700',\n      bg: 'from-blue-50 to-blue-100',\n      text: 'text-blue-700',\n      border: 'border-blue-200'\n    },\n    green: {\n      gradient: 'from-emerald-500 via-emerald-600 to-emerald-700',\n      bg: 'from-emerald-50 to-emerald-100',\n      text: 'text-emerald-700',\n      border: 'border-emerald-200'\n    },\n    amber: {\n      gradient: 'from-amber-500 via-amber-600 to-amber-700',\n      bg: 'from-amber-50 to-amber-100',\n      text: 'text-amber-700',\n      border: 'border-amber-200'\n    },\n    red: {\n      gradient: 'from-red-500 via-red-600 to-red-700',\n      bg: 'from-red-50 to-red-100',\n      text: 'text-red-700',\n      border: 'border-red-200'\n    },\n    teal: {\n      gradient: 'from-teal-500 via-teal-600 to-teal-700',\n      bg: 'from-teal-50 to-teal-100',\n      text: 'text-teal-700',\n      border: 'border-teal-200'\n    }\n  };\n\n  const currentColor = colorClasses[color];\n\n  return (\n    <motion.div\n      className={`medical-card-glass relative overflow-hidden border ${currentColor.border} ${className}`}\n      whileHover={{\n        y: -8,\n        scale: 1.02,\n        transition: { duration: 0.3 }\n      }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className={`absolute inset-0 bg-gradient-to-br ${currentColor.bg} opacity-50`} />\n\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        <motion.div\n          className=\"absolute -top-4 -right-4 w-24 h-24 rounded-full bg-white/20\"\n          animate={{\n            scale: [1, 1.2, 1],\n            rotate: [0, 180, 360],\n          }}\n          transition={{\n            duration: 8,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n        <motion.div\n          className=\"absolute -bottom-4 -left-4 w-16 h-16 rounded-full bg-white/10\"\n          animate={{\n            scale: [1, 1.3, 1],\n            rotate: [360, 180, 0],\n          }}\n          transition={{\n            duration: 6,\n            repeat: Infinity,\n            ease: \"easeInOut\",\n          }}\n        />\n      </div>\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex-1\">\n            <motion.p\n              className=\"text-sm font-semibold text-gray-600 mb-2 uppercase tracking-wide\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              {title}\n            </motion.p>\n\n            <motion.p\n              className=\"text-4xl font-bold text-gray-900 mb-2\"\n              initial={{ opacity: 0, scale: 0.5 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ delay: 0.2, type: \"spring\", stiffness: 200 }}\n            >\n              {value}\n            </motion.p>\n\n            {subtitle && (\n              <motion.p\n                className=\"text-sm text-gray-600 font-medium\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                {subtitle}\n              </motion.p>\n            )}\n\n            {trend && (\n              <motion.div\n                className=\"flex items-center mt-3\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n              >\n                <motion.span\n                  className={`inline-flex items-center gap-1 text-sm font-bold px-2 py-1 rounded-full ${\n                    trend.isPositive\n                      ? 'text-emerald-700 bg-emerald-100'\n                      : 'text-red-700 bg-red-100'\n                  }`}\n                  whileHover={{ scale: 1.05 }}\n                >\n                  <motion.span\n                    animate={{ rotate: trend.isPositive ? 0 : 180 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    ↗\n                  </motion.span>\n                  {Math.abs(trend.value)}%\n                </motion.span>\n                <span className=\"text-xs text-gray-500 ml-2 font-medium\">vs last month</span>\n              </motion.div>\n            )}\n          </div>\n\n          {icon && (\n            <motion.div\n              className={`p-4 rounded-2xl bg-gradient-to-br ${currentColor.gradient} text-white shadow-lg`}\n              whileHover={{\n                scale: 1.1,\n                rotate: 5,\n                boxShadow: '0 20px 40px rgba(0, 0, 0, 0.2)'\n              }}\n              initial={{ opacity: 0, scale: 0, rotate: -90 }}\n              animate={{ opacity: 1, scale: 1, rotate: 0 }}\n              transition={{ delay: 0.3, type: \"spring\", stiffness: 200 }}\n            >\n              {icon}\n            </motion.div>\n          )}\n        </div>\n      </div>\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className={`absolute inset-0 bg-gradient-to-r ${currentColor.gradient} opacity-0 rounded-2xl`}\n        whileHover={{ opacity: 0.1 }}\n        transition={{ duration: 0.3 }}\n      />\n    </motion.div>\n  );\n};\n\ninterface PatientCardProps {\n  patient: {\n    id: string;\n    fullName: string;\n    patientId: string;\n    age?: number;\n    gender: string;\n    mobileNumber: string;\n    profileImageUrl?: string;\n    lastVisit?: string;\n  };\n  onClick?: (patient: any) => void;\n  className?: string;\n}\n\nexport const PatientCard: React.FC<PatientCardProps> = ({\n  patient,\n  onClick,\n  className = ''\n}) => {\n  const getInitials = (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word[0])\n      .join('')\n      .toUpperCase()\n      .slice(0, 2);\n  };\n\n  return (\n    <motion.div\n      className={`medical-card-glass cursor-pointer border border-blue-100 relative overflow-hidden group ${className}`}\n      onClick={() => onClick?.(patient)}\n      whileHover={{\n        y: -6,\n        scale: 1.02,\n        boxShadow: '0 20px 40px rgba(0, 102, 204, 0.15)',\n        transition: { duration: 0.3 }\n      }}\n      whileTap={{ scale: 0.98 }}\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50/50 to-teal-50/50\" />\n\n      {/* Hover Glow Effect */}\n      <motion.div\n        className=\"absolute inset-0 bg-gradient-to-r from-blue-500/5 via-purple-500/5 to-teal-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n      />\n\n      <div className=\"relative z-10 p-6\">\n        <div className=\"flex items-center space-x-4\">\n          {/* Premium Avatar */}\n          <motion.div\n            className=\"relative\"\n            whileHover={{ scale: 1.1, rotate: 5 }}\n            transition={{ duration: 0.2 }}\n          >\n            {patient.profileImageUrl ? (\n              <img\n                src={patient.profileImageUrl}\n                alt={patient.fullName}\n                className=\"w-16 h-16 rounded-2xl object-cover shadow-lg\"\n              />\n            ) : (\n              <div className=\"w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-500 via-blue-600 to-teal-500 flex items-center justify-center text-white font-bold text-lg shadow-lg\">\n                {getInitials(patient.fullName)}\n              </div>\n            )}\n            {/* Status Indicator */}\n            <div className=\"absolute -bottom-1 -right-1 w-5 h-5 bg-green-500 rounded-full border-3 border-white shadow-lg\">\n              <div className=\"w-full h-full bg-green-400 rounded-full animate-pulse\"></div>\n            </div>\n          </motion.div>\n\n          {/* Patient Info */}\n          <div className=\"flex-1 min-w-0\">\n            <motion.div\n              className=\"flex items-center justify-between mb-2\"\n              initial={{ opacity: 0, x: -20 }}\n              animate={{ opacity: 1, x: 0 }}\n              transition={{ delay: 0.1 }}\n            >\n              <h3 className=\"font-bold text-gray-900 text-lg truncate\">\n                {patient.fullName}\n              </h3>\n              <span className=\"px-3 py-1 bg-gradient-to-r from-blue-100 to-teal-100 text-blue-700 text-xs font-bold rounded-full border border-blue-200\">\n                {patient.patientId}\n              </span>\n            </motion.div>\n\n            <motion.div\n              className=\"flex items-center space-x-3 text-sm\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"flex items-center gap-1\">\n                <div className={`w-2 h-2 rounded-full ${\n                  patient.gender === 'Male' ? 'bg-blue-500' :\n                  patient.gender === 'Female' ? 'bg-pink-500' : 'bg-purple-500'\n                }`}></div>\n                <span className=\"text-gray-600 font-medium\">{patient.age ? `${patient.age} years` : 'Age N/A'}</span>\n              </div>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <span className=\"text-gray-600 font-medium\">{patient.gender}</span>\n\n              <span className=\"text-gray-400\">•</span>\n\n              <div className=\"flex items-center gap-1\">\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <span className=\"text-gray-600 font-medium\">{patient.mobileNumber}</span>\n              </div>\n            </motion.div>\n\n            {patient.lastVisit && (\n              <motion.div\n                className=\"flex items-center gap-1 mt-2\"\n                initial={{ opacity: 0, y: 10 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3 }}\n              >\n                <svg className=\"w-3 h-3 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\" />\n                </svg>\n                <p className=\"text-xs text-gray-500 font-medium\">\n                  Last visit: {patient.lastVisit}\n                </p>\n              </motion.div>\n            )}\n          </div>\n\n          {/* Arrow Indicator */}\n          <motion.div\n            className=\"text-gray-400 group-hover:text-blue-500 transition-colors duration-200\"\n            whileHover={{ x: 5 }}\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 5l7 7-7 7\" />\n            </svg>\n          </motion.div>\n        </div>\n\n        {/* Bottom Border Animation */}\n        <motion.div\n          className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-teal-500 rounded-full\"\n          initial={{ width: 0 }}\n          whileHover={{ width: '100%' }}\n          transition={{ duration: 0.3 }}\n        />\n      </div>\n    </motion.div>\n  );\n};\n\ninterface InfoCardProps {\n  title: string;\n  children: React.ReactNode;\n  icon?: React.ReactNode;\n  className?: string;\n  collapsible?: boolean;\n  defaultExpanded?: boolean;\n}\n\nexport const InfoCard: React.FC<InfoCardProps> = ({\n  title,\n  children,\n  icon,\n  className = '',\n  collapsible = false,\n  defaultExpanded = true\n}) => {\n  const [isExpanded, setIsExpanded] = React.useState(defaultExpanded);\n\n  return (\n    <Card className={className}>\n      <div \n        className={`flex items-center justify-between ${collapsible ? 'cursor-pointer' : ''}`}\n        onClick={collapsible ? () => setIsExpanded(!isExpanded) : undefined}\n      >\n        <div className=\"flex items-center space-x-3\">\n          {icon && (\n            <div className=\"p-2 bg-blue-100 rounded-lg text-blue-600\">\n              {icon}\n            </div>\n          )}\n          <h3 className=\"text-lg font-semibold text-gray-900\">{title}</h3>\n        </div>\n        {collapsible && (\n          <motion.div\n            animate={{ rotate: isExpanded ? 180 : 0 }}\n            transition={{ duration: 0.2 }}\n          >\n            <svg className=\"w-5 h-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 9l-7 7-7-7\" />\n            </svg>\n          </motion.div>\n        )}\n      </div>\n      \n      <motion.div\n        initial={false}\n        animate={{ \n          height: isExpanded ? 'auto' : 0,\n          opacity: isExpanded ? 1 : 0\n        }}\n        transition={{ duration: 0.3 }}\n        style={{ overflow: 'hidden' }}\n      >\n        <div className=\"mt-4\">\n          {children}\n        </div>\n      </motion.div>\n    </Card>\n  );\n};\n\nexport default Card;\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,YAAY,EAAE,EACd,QAAQ,IAAI,EACZ,UAAU,IAAI,EACd,SAAS,IAAI,EACb,OAAO,EACP,WAAW,KAAK,EACjB;IACC,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,cAAc,CAAC;;IAEnB,EAAE,cAAc,CAAC,QAAQ,CAAC;IAC1B,EAAE,aAAa,CAAC,OAAO,CAAC;IACxB,EAAE,WAAW,4CAA4C,WAAW;IACpE,EAAE,UAAU,mBAAmB,GAAG;IAClC,EAAE,UAAU;EACd,CAAC;IAED,MAAM,eAAe;QACnB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,OAAO,QAAQ;YACb,GAAG,CAAC;YACJ,WAAW;QACb,IAAI,CAAC;IACP;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW;QACX,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;QACX,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;QAC7C,SAAS;kBAER;;;;;;AAGP;AAiBO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,KAAK,EACL,QAAQ,MAAM,EACd,YAAY,EAAE,EACf;IACC,MAAM,eAAe;QACnB,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,OAAO;YACL,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,KAAK;YACH,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;QACA,MAAM;YACJ,UAAU;YACV,IAAI;YACJ,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,eAAe,YAAY,CAAC,MAAM;IAExC,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,mDAAmD,EAAE,aAAa,MAAM,CAAC,CAAC,EAAE,WAAW;QACnG,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC;gBAAI,WAAW,CAAC,mCAAmC,EAAE,aAAa,EAAE,CAAC,WAAW,CAAC;;;;;;0BAGlF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAG;gCAAK;6BAAI;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;kCAEF,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BACP,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,QAAQ;gCAAC;gCAAK;gCAAK;6BAAE;wBACvB;wBACA,YAAY;4BACV,UAAU;4BACV,QAAQ;4BACR,MAAM;wBACR;;;;;;;;;;;;0BAIJ,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;8CAGH,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;wCAAK,MAAM;wCAAU,WAAW;oCAAI;8CAExD;;;;;;gCAGF,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAExB;;;;;;gCAIJ,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;;sDAEzB,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CACV,WAAW,CAAC,wEAAwE,EAClF,MAAM,UAAU,GACZ,oCACA,2BACJ;4CACF,YAAY;gDAAE,OAAO;4CAAK;;8DAE1B,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oDACV,SAAS;wDAAE,QAAQ,MAAM,UAAU,GAAG,IAAI;oDAAI;oDAC9C,YAAY;wDAAE,UAAU;oDAAI;8DAC7B;;;;;;gDAGA,KAAK,GAAG,CAAC,MAAM,KAAK;gDAAE;;;;;;;sDAEzB,8OAAC;4CAAK,WAAU;sDAAyC;;;;;;;;;;;;;;;;;;wBAK9D,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAW,CAAC,kCAAkC,EAAE,aAAa,QAAQ,CAAC,qBAAqB,CAAC;4BAC5F,YAAY;gCACV,OAAO;gCACP,QAAQ;gCACR,WAAW;4BACb;4BACA,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ,CAAC;4BAAG;4BAC7C,SAAS;gCAAE,SAAS;gCAAG,OAAO;gCAAG,QAAQ;4BAAE;4BAC3C,YAAY;gCAAE,OAAO;gCAAK,MAAM;gCAAU,WAAW;4BAAI;sCAExD;;;;;;;;;;;;;;;;;0BAOT,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,CAAC,kCAAkC,EAAE,aAAa,QAAQ,CAAC,sBAAsB,CAAC;gBAC7F,YAAY;oBAAE,SAAS;gBAAI;gBAC3B,YAAY;oBAAE,UAAU;gBAAI;;;;;;;;;;;;AAIpC;AAiBO,MAAM,cAA0C,CAAC,EACtD,OAAO,EACP,OAAO,EACP,YAAY,EAAE,EACf;IACC,MAAM,cAAc,CAAC;QACnB,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,IAAI,CAAC,EAAE,EACnB,IAAI,CAAC,IACL,WAAW,GACX,KAAK,CAAC,GAAG;IACd;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,wFAAwF,EAAE,WAAW;QACjH,SAAS,IAAM,UAAU;QACzB,YAAY;YACV,GAAG,CAAC;YACJ,OAAO;YACP,WAAW;YACX,YAAY;gBAAE,UAAU;YAAI;QAC9B;QACA,UAAU;YAAE,OAAO;QAAK;QACxB,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;;;;;;0BAGZ,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,OAAO;oCAAK,QAAQ;gCAAE;gCACpC,YAAY;oCAAE,UAAU;gCAAI;;oCAE3B,QAAQ,eAAe,iBACtB,8OAAC;wCACC,KAAK,QAAQ,eAAe;wCAC5B,KAAK,QAAQ,QAAQ;wCACrB,WAAU;;;;;iGAGZ,8OAAC;wCAAI,WAAU;kDACZ,YAAY,QAAQ,QAAQ;;;;;;kDAIjC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;;;;;;;0CAKnB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAG,WAAU;0DACX,QAAQ,QAAQ;;;;;;0DAEnB,8OAAC;gDAAK,WAAU;0DACb,QAAQ,SAAS;;;;;;;;;;;;kDAItB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,qBAAqB,EACpC,QAAQ,MAAM,KAAK,SAAS,gBAC5B,QAAQ,MAAM,KAAK,WAAW,gBAAgB,iBAC9C;;;;;;kEACF,8OAAC;wDAAK,WAAU;kEAA6B,QAAQ,GAAG,GAAG,GAAG,QAAQ,GAAG,CAAC,MAAM,CAAC,GAAG;;;;;;;;;;;;0DAGtF,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,8OAAC;gDAAK,WAAU;0DAA6B,QAAQ,MAAM;;;;;;0DAE3D,8OAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAEhC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;kEAEvE,8OAAC;wDAAK,WAAU;kEAA6B,QAAQ,YAAY;;;;;;;;;;;;;;;;;;oCAIpE,QAAQ,SAAS,kBAChB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;;0DAEzB,8OAAC;gDAAI,WAAU;gDAAwB,MAAK;gDAAO,QAAO;gDAAe,SAAQ;0DAC/E,cAAA,8OAAC;oDAAK,eAAc;oDAAQ,gBAAe;oDAAQ,aAAa;oDAAG,GAAE;;;;;;;;;;;0DAEvE,8OAAC;gDAAE,WAAU;;oDAAoC;oDAClC,QAAQ,SAAS;;;;;;;;;;;;;;;;;;;0CAOtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,YAAY;oCAAE,GAAG;gCAAE;0CAEnB,cAAA,8OAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;kCAM3E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BAAE,OAAO;wBAAO;wBAC5B,YAAY;4BAAE,UAAU;wBAAI;;;;;;;;;;;;;;;;;;AAKtC;AAWO,MAAM,WAAoC,CAAC,EAChD,KAAK,EACL,QAAQ,EACR,IAAI,EACJ,YAAY,EAAE,EACd,cAAc,KAAK,EACnB,kBAAkB,IAAI,EACvB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEnD,qBACE,8OAAC;QAAK,WAAW;;0BACf,8OAAC;gBACC,WAAW,CAAC,kCAAkC,EAAE,cAAc,mBAAmB,IAAI;gBACrF,SAAS,cAAc,IAAM,cAAc,CAAC,cAAc;;kCAE1D,8OAAC;wBAAI,WAAU;;4BACZ,sBACC,8OAAC;gCAAI,WAAU;0CACZ;;;;;;0CAGL,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;;;;;;;oBAEtD,6BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,QAAQ,aAAa,MAAM;wBAAE;wBACxC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,8OAAC;4BAAI,WAAU;4BAAwB,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCAC/E,cAAA,8OAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;0BAM7E,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;gBACT,SAAS;oBACP,QAAQ,aAAa,SAAS;oBAC9B,SAAS,aAAa,IAAI;gBAC5B;gBACA,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,OAAO;oBAAE,UAAU;gBAAS;0BAE5B,cAAA,8OAAC;oBAAI,WAAU;8BACZ;;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 846, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { motion } from 'framer-motion';\nimport {\n  UserGroupIcon,\n  UserPlusIcon,\n  ChartBarIcon,\n  HeartIcon\n} from '@heroicons/react/24/outline';\nimport { StatCard } from '@/components/ui/Card';\nimport { useAuthStore } from '@/store';\nimport { toast } from 'react-hot-toast';\n\nconst DashboardPage: React.FC = () => {\n  const { user } = useAuthStore();\n  const [stats, setStats] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    const fetchStats = async () => {\n      try {\n        const response = await fetch('/api/dashboard/stats');\n        const data = await response.json();\n\n        if (data.success) {\n          setStats(data.data);\n        } else {\n          toast.error('Failed to fetch dashboard statistics');\n        }\n      } catch (error) {\n        console.error('Error fetching stats:', error);\n        toast.error('Failed to fetch dashboard statistics');\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  const statCards = stats ? [\n    {\n      title: 'Total Patients',\n      value: stats.totalPatients.toLocaleString(),\n      subtitle: 'Active patients',\n      icon: <UserGroupIcon className=\"w-6 h-6\" />,\n      trend: { value: 12, isPositive: true },\n      color: 'blue' as const,\n    },\n    {\n      title: 'New Patients',\n      value: stats.newPatientsThisMonth.toString(),\n      subtitle: 'This month',\n      icon: <UserPlusIcon className=\"w-6 h-6\" />,\n      trend: { value: 8, isPositive: true },\n      color: 'green' as const,\n    },\n    {\n      title: 'Appointments',\n      value: stats.appointments.toString(),\n      subtitle: 'This week',\n      icon: <ChartBarIcon className=\"w-6 h-6\" />,\n      trend: { value: 3, isPositive: false },\n      color: 'amber' as const,\n    },\n    {\n      title: 'Critical Cases',\n      value: stats.criticalCases.toString(),\n      subtitle: 'Require attention',\n      icon: <HeartIcon className=\"w-6 h-6\" />,\n      trend: { value: 2, isPositive: false },\n      color: 'red' as const,\n    },\n  ] : [];\n\n  const containerVariants = {\n    hidden: { opacity: 0 },\n    visible: {\n      opacity: 1,\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      {/* Premium Animated Background */}\n      <div className=\"absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-teal-50\">\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]\"></div>\n        <div className=\"absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(16,185,129,0.1),transparent_50%)]\"></div>\n      </div>\n\n      {/* Floating Medical Elements */}\n      <div className=\"absolute inset-0 overflow-hidden pointer-events-none\">\n        {[...Array(8)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-2 bg-blue-400/20 rounded-full\"\n            style={{\n              left: `${Math.random() * 100}%`,\n              top: `${Math.random() * 100}%`,\n            }}\n            animate={{\n              y: [-20, 20, -20],\n              x: [-10, 10, -10],\n              scale: [1, 1.5, 1],\n              opacity: [0.3, 0.8, 0.3],\n            }}\n            transition={{\n              duration: 4 + Math.random() * 4,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: Math.random() * 2,\n            }}\n          />\n        ))}\n      </div>\n\n      <motion.div\n        className=\"relative z-10 space-y-8 p-6\"\n        variants={containerVariants}\n        initial=\"hidden\"\n        animate=\"visible\"\n      >\n        {/* Premium Header */}\n        <motion.div\n          variants={itemVariants}\n          className=\"relative\"\n        >\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <motion.h1\n                className=\"text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-3\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.2 }}\n              >\n                Welcome back, {user ? `${user.firstName} ${user.lastName}` : 'Doctor'}\n              </motion.h1>\n              <motion.p\n                className=\"text-xl text-gray-600 font-medium\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.6, delay: 0.4 }}\n              >\n                Here's what's happening with your patients today.\n              </motion.p>\n            </div>\n\n            {/* Premium Time Display */}\n            <motion.div\n              className=\"text-right\"\n              initial={{ opacity: 0, scale: 0.8 }}\n              animate={{ opacity: 1, scale: 1 }}\n              transition={{ duration: 0.6, delay: 0.6 }}\n            >\n              <div className=\"text-3xl font-bold text-gray-800\">\n                {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}\n              </div>\n              <div className=\"text-sm text-gray-500 font-medium\">\n                {new Date().toLocaleDateString('en-US', {\n                  weekday: 'long',\n                  year: 'numeric',\n                  month: 'long',\n                  day: 'numeric'\n                })}\n              </div>\n            </motion.div>\n          </div>\n\n          {/* Decorative Line */}\n          <motion.div\n            className=\"mt-6 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-teal-500 rounded-full\"\n            initial={{ width: 0 }}\n            animate={{ width: '100%' }}\n            transition={{ duration: 1.2, delay: 0.8 }}\n          />\n        </motion.div>\n\n        {/* Premium Stats Grid */}\n        <motion.div\n          className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\"\n          variants={containerVariants}\n        >\n          {loading ? (\n            // Premium Loading Skeletons\n            Array.from({ length: 4 }).map((_, index) => (\n              <motion.div\n                key={index}\n                variants={itemVariants}\n                className=\"relative\"\n              >\n                <div className=\"medical-card-glass p-8 relative overflow-hidden\">\n                  {/* Shimmer Effect */}\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse\"></div>\n\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex-1\">\n                      <div className=\"h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-3 animate-pulse\"></div>\n                      <div className=\"h-10 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-3 animate-pulse\"></div>\n                      <div className=\"h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full animate-pulse\"></div>\n                    </div>\n                    <div className=\"w-16 h-16 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl animate-pulse\"></div>\n                  </div>\n                </div>\n              </motion.div>\n            ))\n          ) : (\n            statCards.map((stat, index) => (\n              <motion.div\n                key={stat.title}\n                variants={itemVariants}\n                whileHover={{\n                  y: -12,\n                  scale: 1.03,\n                  transition: { duration: 0.3 }\n                }}\n              >\n                <StatCard {...stat} />\n              </motion.div>\n            ))\n          )}\n        </motion.div>\n\n        {/* Premium Quick Actions */}\n        <motion.div variants={itemVariants} className=\"relative\">\n          <motion.h2\n            className=\"text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-8\"\n            initial={{ opacity: 0, x: -30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            Quick Actions\n          </motion.h2>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n            <motion.div\n              className=\"medical-card-glass p-8 cursor-pointer group relative overflow-hidden border border-blue-200\"\n              whileHover={{\n                y: -8,\n                scale: 1.03,\n                boxShadow: '0 25px 50px rgba(59, 130, 246, 0.25)',\n                transition: { duration: 0.3 }\n              }}\n              whileTap={{ scale: 0.97 }}\n              onClick={() => window.location.href = '/patients/new'}\n            >\n              {/* Hover Glow Effect */}\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              />\n\n              <div className=\"relative z-10 flex items-center space-x-6\">\n                <motion.div\n                  className=\"p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                  whileHover={{ scale: 1.1, rotate: 5 }}\n                >\n                  <UserPlusIcon className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">Add New Patient</h3>\n                  <p className=\"text-gray-600 font-medium\">Register a new patient in the system</p>\n                </div>\n              </div>\n\n              {/* Bottom Border Animation */}\n              <motion.div\n                className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500\"\n                initial={{ width: 0 }}\n                whileHover={{ width: '100%' }}\n                transition={{ duration: 0.3 }}\n              />\n            </motion.div>\n\n            <motion.div\n              className=\"medical-card-glass p-8 cursor-pointer group relative overflow-hidden border border-emerald-200\"\n              whileHover={{\n                y: -8,\n                scale: 1.03,\n                boxShadow: '0 25px 50px rgba(16, 185, 129, 0.25)',\n                transition: { duration: 0.3 }\n              }}\n              whileTap={{ scale: 0.97 }}\n              onClick={() => window.location.href = '/patients'}\n            >\n              {/* Hover Glow Effect */}\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              />\n\n              <div className=\"relative z-10 flex items-center space-x-6\">\n                <motion.div\n                  className=\"p-4 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                  whileHover={{ scale: 1.1, rotate: 5 }}\n                >\n                  <UserGroupIcon className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">View All Patients</h3>\n                  <p className=\"text-gray-600 font-medium\">Browse and search patient records</p>\n                </div>\n              </div>\n\n              {/* Bottom Border Animation */}\n              <motion.div\n                className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-emerald-500 to-teal-500\"\n                initial={{ width: 0 }}\n                whileHover={{ width: '100%' }}\n                transition={{ duration: 0.3 }}\n              />\n            </motion.div>\n\n            <motion.div\n              className=\"medical-card-glass p-8 cursor-pointer group relative overflow-hidden border border-purple-200\"\n              whileHover={{\n                y: -8,\n                scale: 1.03,\n                boxShadow: '0 25px 50px rgba(147, 51, 234, 0.25)',\n                transition: { duration: 0.3 }\n              }}\n              whileTap={{ scale: 0.97 }}\n              onClick={() => window.location.href = '/analytics'}\n            >\n              {/* Hover Glow Effect */}\n              <motion.div\n                className=\"absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n              />\n\n              <div className=\"relative z-10 flex items-center space-x-6\">\n                <motion.div\n                  className=\"p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                  whileHover={{ scale: 1.1, rotate: 5 }}\n                >\n                  <ChartBarIcon className=\"w-8 h-8 text-white\" />\n                </motion.div>\n                <div>\n                  <h3 className=\"text-xl font-bold text-gray-900 mb-2\">View Analytics</h3>\n                  <p className=\"text-gray-600 font-medium\">Check medical reports and insights</p>\n                </div>\n              </div>\n\n              {/* Bottom Border Animation */}\n              <motion.div\n                className=\"absolute bottom-0 left-0 h-1 bg-gradient-to-r from-purple-500 to-pink-500\"\n                initial={{ width: 0 }}\n                whileHover={{ width: '100%' }}\n                transition={{ duration: 0.3 }}\n              />\n            </motion.div>\n          </div>\n        </motion.div>\n\n        {/* Premium Recent Activity */}\n        <motion.div variants={itemVariants} className=\"relative\">\n          <motion.h2\n            className=\"text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-8\"\n            initial={{ opacity: 0, x: -30 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ duration: 0.6, delay: 0.2 }}\n          >\n            Recent Activity\n          </motion.h2>\n\n          <div className=\"medical-card-glass p-8 relative overflow-hidden\">\n            {/* Background Pattern */}\n            <div className=\"absolute inset-0 opacity-5\">\n              <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\">\n                <pattern id=\"activity-pattern\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n                  <circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"currentColor\" />\n                </pattern>\n                <rect width=\"100\" height=\"100\" fill=\"url(#activity-pattern)\" />\n              </svg>\n            </div>\n\n            <div className=\"relative z-10 space-y-6\">\n              {loading ? (\n                // Premium Loading Skeletons\n                Array.from({ length: 5 }).map((_, index) => (\n                  <motion.div\n                    key={index}\n                    className=\"flex items-center space-x-4 p-4 bg-white/50 rounded-2xl backdrop-blur-sm\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <div className=\"w-12 h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full animate-pulse\" />\n                    <div className=\"flex-1\">\n                      <div className=\"h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-2 animate-pulse\" />\n                      <div className=\"h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full w-2/3 animate-pulse\" />\n                    </div>\n                    <div className=\"h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full w-20 animate-pulse\" />\n                  </motion.div>\n                ))\n              ) : (\n                stats?.recentActivity?.map((activity: any, index: number) => (\n                  <motion.div\n                    key={index}\n                    className=\"flex items-center justify-between p-6 bg-white/60 rounded-2xl backdrop-blur-sm border border-white/20 hover:bg-white/80 transition-all duration-300 group\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.1 }}\n                    whileHover={{ scale: 1.02, x: 8 }}\n                  >\n                    <div className=\"flex items-center space-x-4\">\n                      <motion.div\n                        className=\"w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300\"\n                        whileHover={{ scale: 1.1, rotate: 5 }}\n                      >\n                        <UserPlusIcon className=\"w-6 h-6 text-white\" />\n                      </motion.div>\n                      <div>\n                        <p className=\"font-bold text-gray-900 text-lg\">{activity.action}</p>\n                        <p className=\"text-gray-600 font-medium\">Patient: {activity.patient}</p>\n                      </div>\n                    </div>\n\n                    <div className=\"text-right\">\n                      <span className=\"text-sm text-gray-500 font-medium\">{activity.time}</span>\n                      <div className=\"flex items-center justify-end gap-1 mt-1\">\n                        <div className=\"w-2 h-2 bg-blue-500 rounded-full animate-pulse\"></div>\n                        <span className=\"text-xs text-gray-400\">Active</span>\n                      </div>\n                    </div>\n                  </motion.div>\n                )) || (\n                  <motion.div\n                    className=\"text-center py-12\"\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                  >\n                    <div className=\"w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center\">\n                      <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\" />\n                      </svg>\n                    </div>\n                    <p className=\"text-gray-500 font-medium\">No recent activity</p>\n                  </motion.div>\n                )\n              )}\n            </div>\n          </div>\n        </motion.div>\n      </motion.div>\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AACA;AACA;AAZA;;;;;;;;AAcA,MAAM,gBAA0B;IAC9B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa;YACjB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM;gBAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB,OAAO;oBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG,EAAE;IAEL,MAAM,YAAY,QAAQ;QACxB;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,cAAc;YACzC,UAAU;YACV,oBAAM,8OAAC,yNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;YAC/B,OAAO;gBAAE,OAAO;gBAAI,YAAY;YAAK;YACrC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,oBAAoB,CAAC,QAAQ;YAC1C,UAAU;YACV,oBAAM,8OAAC,uNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAK;YACpC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,YAAY,CAAC,QAAQ;YAClC,UAAU;YACV,oBAAM,8OAAC,uNAAA,CAAA,eAAY;gBAAC,WAAU;;;;;;YAC9B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAM;YACrC,OAAO;QACT;QACA;YACE,OAAO;YACP,OAAO,MAAM,aAAa,CAAC,QAAQ;YACnC,UAAU;YACV,oBAAM,8OAAC,iNAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;YAC3B,OAAO;gBAAE,OAAO;gBAAG,YAAY;YAAM;YACrC,OAAO;QACT;KACD,GAAG,EAAE;IAEN,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;QAAE;QACrB,SAAS;YACP,SAAS;YACT,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;;;;;;;;;;;0BAIjB,8OAAC;gBAAI,WAAU;0BACZ;uBAAI,MAAM;iBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAET,WAAU;wBACV,OAAO;4BACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;4BAC/B,KAAK,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;wBAChC;wBACA,SAAS;4BACP,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;4BACjB,GAAG;gCAAC,CAAC;gCAAI;gCAAI,CAAC;6BAAG;4BACjB,OAAO;gCAAC;gCAAG;gCAAK;6BAAE;4BAClB,SAAS;gCAAC;gCAAK;gCAAK;6BAAI;wBAC1B;wBACA,YAAY;4BACV,UAAU,IAAI,KAAK,MAAM,KAAK;4BAC9B,QAAQ;4BACR,MAAM;4BACN,OAAO,KAAK,MAAM,KAAK;wBACzB;uBAjBK;;;;;;;;;;0BAsBX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;gBACV,SAAQ;gBACR,SAAQ;;kCAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,UAAU;wBACV,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gDACR,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;;oDACzC;oDACgB,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GAAG;;;;;;;0DAE/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gDACP,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,UAAU;oDAAK,OAAO;gDAAI;0DACzC;;;;;;;;;;;;kDAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAI;wCAClC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;4CAAK,OAAO;wCAAI;;0DAExC,8OAAC;gDAAI,WAAU;0DACZ,IAAI,OAAO,kBAAkB,CAAC,EAAE,EAAE;oDAAE,MAAM;oDAAW,QAAQ;gDAAU;;;;;;0DAE1E,8OAAC;gDAAI,WAAU;0DACZ,IAAI,OAAO,kBAAkB,CAAC,SAAS;oDACtC,SAAS;oDACT,MAAM;oDACN,OAAO;oDACP,KAAK;gDACP;;;;;;;;;;;;;;;;;;0CAMN,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCAAE,OAAO;gCAAE;gCACpB,SAAS;oCAAE,OAAO;gCAAO;gCACzB,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;;;;;;;;;;;;kCAK5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU;kCAET,UACC,4BAA4B;wBAC5B,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,WAAU;0CAEV,cAAA,8OAAC;oCAAI,WAAU;;sDAEb,8OAAC;4CAAI,WAAU;;;;;;sDAEf,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;sEACf,8OAAC;4DAAI,WAAU;;;;;;;;;;;;8DAEjB,8OAAC;oDAAI,WAAU;;;;;;;;;;;;;;;;;;+BAdd;;;;4EAoBT,UAAU,GAAG,CAAC,CAAC,MAAM,sBACnB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gCAET,UAAU;gCACV,YAAY;oCACV,GAAG,CAAC;oCACJ,OAAO;oCACP,YAAY;wCAAE,UAAU;oCAAI;gCAC9B;0CAEA,cAAA,8OAAC,gIAAA,CAAA,WAAQ;oCAAE,GAAG,IAAI;;;;;;+BARb,KAAK,KAAK;;;;;;;;;;kCAevB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;0CAID,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CACV,GAAG,CAAC;4CACJ,OAAO;4CACP,WAAW;4CACX,YAAY;gDAAE,UAAU;4CAAI;wCAC9B;wCACA,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0DAGtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;4DAAK,QAAQ;wDAAE;kEAEpC,cAAA,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAuC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;0DAK7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,YAAY;oDAAE,OAAO;gDAAO;gDAC5B,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;kDAIhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CACV,GAAG,CAAC;4CACJ,OAAO;4CACP,WAAW;4CACX,YAAY;gDAAE,UAAU;4CAAI;wCAC9B;wCACA,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0DAGtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;4DAAK,QAAQ;wDAAE;kEAEpC,cAAA,8OAAC,yNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;kEAE3B,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAuC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;0DAK7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,YAAY;oDAAE,OAAO;gDAAO;gDAC5B,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;kDAIhC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CACV,GAAG,CAAC;4CACJ,OAAO;4CACP,WAAW;4CACX,YAAY;gDAAE,UAAU;4CAAI;wCAC9B;wCACA,UAAU;4CAAE,OAAO;wCAAK;wCACxB,SAAS,IAAM,OAAO,QAAQ,CAAC,IAAI,GAAG;;0DAGtC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;;;;;;0DAGZ,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;4DAAK,QAAQ;wDAAE;kEAEpC,cAAA,8OAAC,uNAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAE1B,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAuC;;;;;;0EACrD,8OAAC;gEAAE,WAAU;0EAA4B;;;;;;;;;;;;;;;;;;0DAK7C,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,YAAY;oDAAE,OAAO;gDAAO;gDAC5B,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;;;;;;;;;;;;;kCAOpC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBAAC,UAAU;wBAAc,WAAU;;0CAC5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,UAAU;oCAAK,OAAO;gCAAI;0CACzC;;;;;;0CAID,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;4CAAgB,SAAQ;;8DACrC,8OAAC;oDAAQ,IAAG;oDAAmB,GAAE;oDAAI,GAAE;oDAAI,OAAM;oDAAK,QAAO;oDAAK,cAAa;8DAC7E,cAAA,8OAAC;wDAAO,IAAG;wDAAK,IAAG;wDAAK,GAAE;wDAAI,MAAK;;;;;;;;;;;8DAErC,8OAAC;oDAAK,OAAM;oDAAM,QAAO;oDAAM,MAAK;;;;;;;;;;;;;;;;;kDAIxC,8OAAC;wCAAI,WAAU;kDACZ,UACC,4BAA4B;wCAC5B,MAAM,IAAI,CAAC;4CAAE,QAAQ;wCAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBAChC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;gDAAI;;kEAEjC,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAI,WAAU;;;;;;;;;;;;kEAEjB,8OAAC;wDAAI,WAAU;;;;;;;+CAXV;;;;4FAeT,OAAO,gBAAgB,IAAI,CAAC,UAAe,sBACzC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gDAET,WAAU;gDACV,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,YAAY;oDAAE,OAAO,QAAQ;gDAAI;gDACjC,YAAY;oDAAE,OAAO;oDAAM,GAAG;gDAAE;;kEAEhC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,YAAY;oEAAE,OAAO;oEAAK,QAAQ;gEAAE;0EAEpC,cAAA,8OAAC,uNAAA,CAAA,eAAY;oEAAC,WAAU;;;;;;;;;;;0EAE1B,8OAAC;;kFACC,8OAAC;wEAAE,WAAU;kFAAmC,SAAS,MAAM;;;;;;kFAC/D,8OAAC;wEAAE,WAAU;;4EAA4B;4EAAU,SAAS,OAAO;;;;;;;;;;;;;;;;;;;kEAIvE,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAK,WAAU;0EAAqC,SAAS,IAAI;;;;;;0EAClE,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;;;;;;kFACf,8OAAC;wEAAK,WAAU;kFAAwB;;;;;;;;;;;;;;;;;;;+CAxBvC;;;;2GA6BP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;;8DAE5B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAwB,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEAC/E,cAAA,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,8OAAC;oDAAE,WAAU;8DAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU7D;uCAEe", "debugId": null}}]}