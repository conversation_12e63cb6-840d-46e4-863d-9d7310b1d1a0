'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MagnifyingGlassIcon, 
  FunnelIcon, 
  UserPlusIcon,
  AdjustmentsHorizontalIcon 
} from '@heroicons/react/24/outline';
import { PatientCard } from '@/components/ui/Card';
import { ButtonLoading } from '@/components/ui/Loading';

const PatientsPage: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);

  // Mock patient data
  const mockPatients = [
    {
      id: '1',
      fullName: '<PERSON>',
      patientId: 'PAT20240001',
      age: 45,
      gender: 'Male',
      mobileNumber: '+91 **********',
      lastVisit: '2024-01-15',
    },
    {
      id: '2',
      fullName: '<PERSON>',
      patientId: 'PAT20240002',
      age: 32,
      gender: 'Female',
      mobileNumber: '+91 **********',
      lastVisit: '2024-01-14',
    },
    {
      id: '3',
      fullName: '<PERSON>',
      patientId: 'PAT20240003',
      age: 58,
      gender: 'Male',
      mobileNumber: '+91 **********',
      lastVisit: '2024-01-13',
    },
    {
      id: '4',
      fullName: 'Emily Davis',
      patientId: 'PAT20240004',
      age: 28,
      gender: 'Female',
      mobileNumber: '+91 **********',
      lastVisit: '2024-01-12',
    },
    {
      id: '5',
      fullName: 'Robert Wilson',
      patientId: 'PAT20240005',
      age: 67,
      gender: 'Male',
      mobileNumber: '+91 **********',
      lastVisit: '2024-01-11',
    },
    {
      id: '6',
      fullName: 'Lisa Anderson',
      patientId: 'PAT20240006',
      age: 41,
      gender: 'Female',
      mobileNumber: '+91 **********',
      lastVisit: '2024-01-10',
    },
  ];

  const filteredPatients = mockPatients.filter(patient =>
    patient.fullName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    patient.patientId.toLowerCase().includes(searchQuery.toLowerCase()) ||
    patient.mobileNumber.includes(searchQuery)
  );

  const handlePatientClick = (patient: any) => {
    console.log('Patient clicked:', patient);
    // Navigate to patient detail page
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div 
        className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"
        variants={itemVariants}
      >
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Patients</h1>
          <p className="text-gray-600 mt-1">
            Manage and view all patient records
          </p>
        </div>
        <motion.button
          className="btn-primary"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <UserPlusIcon className="w-5 h-5" />
          Add New Patient
        </motion.button>
      </motion.div>

      {/* Search and Filters */}
      <motion.div 
        className="medical-card p-6"
        variants={itemVariants}
      >
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Search Bar */}
          <div className="flex-1 relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <MagnifyingGlassIcon className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              className="form-input pl-10"
              placeholder="Search by name, patient ID, or phone number..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          {/* Filter Button */}
          <motion.button
            className="btn-secondary"
            onClick={() => setShowFilters(!showFilters)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <FunnelIcon className="w-5 h-5" />
            Filters
          </motion.button>
        </div>

        {/* Filter Panel */}
        <motion.div
          initial={false}
          animate={{ 
            height: showFilters ? 'auto' : 0,
            opacity: showFilters ? 1 : 0
          }}
          transition={{ duration: 0.3 }}
          style={{ overflow: 'hidden' }}
        >
          <div className="mt-6 pt-6 border-t border-gray-200">
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <label className="form-label">Gender</label>
                <select className="form-input">
                  <option value="">All Genders</option>
                  <option value="Male">Male</option>
                  <option value="Female">Female</option>
                  <option value="Other">Other</option>
                </select>
              </div>
              <div>
                <label className="form-label">Age Range</label>
                <select className="form-input">
                  <option value="">All Ages</option>
                  <option value="0-18">0-18 years</option>
                  <option value="19-35">19-35 years</option>
                  <option value="36-50">36-50 years</option>
                  <option value="51-65">51-65 years</option>
                  <option value="65+">65+ years</option>
                </select>
              </div>
              <div>
                <label className="form-label">Blood Group</label>
                <select className="form-input">
                  <option value="">All Blood Groups</option>
                  <option value="A+">A+</option>
                  <option value="A-">A-</option>
                  <option value="B+">B+</option>
                  <option value="B-">B-</option>
                  <option value="AB+">AB+</option>
                  <option value="AB-">AB-</option>
                  <option value="O+">O+</option>
                  <option value="O-">O-</option>
                </select>
              </div>
            </div>
          </div>
        </motion.div>
      </motion.div>

      {/* Results Summary */}
      <motion.div variants={itemVariants}>
        <p className="text-gray-600">
          Showing {filteredPatients.length} of {mockPatients.length} patients
        </p>
      </motion.div>

      {/* Patient List */}
      <motion.div
        className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        variants={containerVariants}
      >
        {filteredPatients.map((patient, index) => (
          <motion.div
            key={patient.id}
            variants={itemVariants}
            transition={{ delay: index * 0.05 }}
          >
            <PatientCard
              patient={patient}
              onClick={handlePatientClick}
            />
          </motion.div>
        ))}
      </motion.div>

      {/* Empty State */}
      {filteredPatients.length === 0 && (
        <motion.div
          className="text-center py-12"
          variants={itemVariants}
        >
          <div className="w-24 h-24 mx-auto mb-4 bg-gray-100 rounded-full flex items-center justify-center">
            <MagnifyingGlassIcon className="w-12 h-12 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No patients found
          </h3>
          <p className="text-gray-600 mb-6">
            Try adjusting your search criteria or add a new patient.
          </p>
          <motion.button
            className="btn-primary"
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <UserPlusIcon className="w-5 h-5" />
            Add New Patient
          </motion.button>
        </motion.div>
      )}
    </motion.div>
  );
};

export default PatientsPage;
