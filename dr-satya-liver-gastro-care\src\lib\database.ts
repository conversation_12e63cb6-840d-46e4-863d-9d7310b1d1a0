import { createClient } from '@supabase/supabase-js';

// Database configuration for NeonDB
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Create Supabase client for NeonDB connection
export const supabase = createClient(supabaseUrl, supabaseKey);

// Database connection using direct PostgreSQL connection
import { Pool } from 'pg';

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

export { pool };

// Database helper functions
export const db = {
  query: async (text: string, params?: any[]) => {
    const client = await pool.connect();
    try {
      const result = await client.query(text, params);
      return result;
    } finally {
      client.release();
    }
  },
  
  getClient: async () => {
    return await pool.connect();
  }
};

// Test database connection
export const testConnection = async () => {
  try {
    const result = await db.query('SELECT NOW()');
    console.log('Database connected successfully:', result.rows[0]);
    return true;
  } catch (error) {
    console.error('Database connection failed:', error);
    return false;
  }
};
