'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useParams, useRouter } from 'next/navigation';
import { 
  ArrowLeftIcon,
  PencilIcon,
  PhoneIcon,
  EnvelopeIcon,
  MapPinIcon,
  CalendarIcon,
  UserIcon,
  HeartIcon,
  ScaleIcon,
  DocumentTextIcon
} from '@heroicons/react/24/outline';
import { InfoCard } from '@/components/ui/Card';
import Loading from '@/components/ui/Loading';
import { toast } from 'react-hot-toast';

const PatientDetailPage: React.FC = () => {
  const params = useParams();
  const router = useRouter();
  const [patient, setPatient] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPatient = async () => {
      try {
        const response = await fetch(`/api/patients/${params.id}`);
        const data = await response.json();
        
        if (data.success) {
          setPatient(data.data);
        } else {
          toast.error('Patient not found');
          router.push('/patients');
        }
      } catch (error) {
        console.error('Error fetching patient:', error);
        toast.error('Failed to fetch patient details');
        router.push('/patients');
      } finally {
        setLoading(false);
      }
    };

    if (params.id) {
      fetchPatient();
    }
  }, [params.id, router]);

  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return 'N/A';
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const getBMICategory = (bmi: number) => {
    if (bmi < 18.5) return { category: 'Underweight', color: 'text-blue-600' };
    if (bmi < 25) return { category: 'Normal', color: 'text-green-600' };
    if (bmi < 30) return { category: 'Overweight', color: 'text-yellow-600' };
    return { category: 'Obese', color: 'text-red-600' };
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  if (loading) {
    return <Loading text="Loading patient details..." fullScreen />;
  }

  if (!patient) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Patient Not Found</h2>
        <button
          onClick={() => router.push('/patients')}
          className="btn-primary"
        >
          <ArrowLeftIcon className="w-5 h-5" />
          Back to Patients
        </button>
      </div>
    );
  }

  return (
    <motion.div
      className="max-w-6xl mx-auto space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div 
        className="flex items-center justify-between"
        variants={itemVariants}
      >
        <div className="flex items-center space-x-4">
          <button
            onClick={() => router.push('/patients')}
            className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
          >
            <ArrowLeftIcon className="w-6 h-6 text-gray-600" />
          </button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              {patient.fullName}
            </h1>
            <p className="text-gray-600">
              Patient ID: {patient.patientId} • Age: {calculateAge(patient.dateOfBirth)} years
            </p>
          </div>
        </div>
        <motion.button
          className="btn-primary"
          whileHover={{ scale: 1.02 }}
          whileTap={{ scale: 0.98 }}
        >
          <PencilIcon className="w-5 h-5" />
          Edit Patient
        </motion.button>
      </motion.div>

      {/* Patient Overview Cards */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Basic Information */}
        <motion.div variants={itemVariants}>
          <InfoCard title="Basic Information" icon={<UserIcon className="w-5 h-5" />}>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-16 h-16 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white font-bold text-xl">
                  {patient.firstName?.[0]?.toUpperCase()}{patient.lastName?.[0]?.toUpperCase()}
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">{patient.fullName}</h3>
                  <p className="text-sm text-gray-600">{patient.gender} • {calculateAge(patient.dateOfBirth)} years old</p>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <CalendarIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">Born: {new Date(patient.dateOfBirth).toLocaleDateString()}</span>
                </div>
                
                <div className="flex items-center space-x-3">
                  <PhoneIcon className="w-4 h-4 text-gray-400" />
                  <span className="text-sm">{patient.mobileNumber}</span>
                </div>
                
                {patient.email && (
                  <div className="flex items-center space-x-3">
                    <EnvelopeIcon className="w-4 h-4 text-gray-400" />
                    <span className="text-sm">{patient.email}</span>
                  </div>
                )}
                
                <div className="flex items-start space-x-3">
                  <MapPinIcon className="w-4 h-4 text-gray-400 mt-0.5" />
                  <span className="text-sm">{patient.address}</span>
                </div>
              </div>
            </div>
          </InfoCard>
        </motion.div>

        {/* Physical Information */}
        <motion.div variants={itemVariants}>
          <InfoCard title="Physical Information" icon={<ScaleIcon className="w-5 h-5" />}>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <p className="text-sm text-gray-600">Height</p>
                  <p className="font-semibold">{patient.height ? `${patient.height} cm` : 'N/A'}</p>
                </div>
                <div>
                  <p className="text-sm text-gray-600">Weight</p>
                  <p className="font-semibold">{patient.weight ? `${patient.weight} kg` : 'N/A'}</p>
                </div>
              </div>
              
              {patient.bmi && (
                <div>
                  <p className="text-sm text-gray-600">BMI</p>
                  <div className="flex items-center space-x-2">
                    <p className="font-semibold">{patient.bmi}</p>
                    <span className={`text-sm font-medium ${getBMICategory(patient.bmi).color}`}>
                      ({getBMICategory(patient.bmi).category})
                    </span>
                  </div>
                </div>
              )}
              
              <div>
                <p className="text-sm text-gray-600">Blood Group</p>
                <p className="font-semibold">{patient.bloodGroup || 'N/A'}</p>
              </div>
            </div>
          </InfoCard>
        </motion.div>

        {/* Medical Scores */}
        <motion.div variants={itemVariants}>
          <InfoCard title="Medical Scores" icon={<HeartIcon className="w-5 h-5" />}>
            <div className="space-y-4">
              <div>
                <p className="text-sm text-gray-600">Charlson Index</p>
                <p className="font-semibold">{patient.charlsonIndex ?? 'N/A'}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">ASA Grade</p>
                <p className="font-semibold">{patient.asaGrade || 'N/A'}</p>
              </div>
              
              <div>
                <p className="text-sm text-gray-600">ECOG Grade</p>
                <p className="font-semibold">{patient.ecogGrade || 'N/A'}</p>
              </div>
            </div>
          </InfoCard>
        </motion.div>
      </div>

      {/* Additional Information */}
      <motion.div variants={itemVariants}>
        <InfoCard 
          title="Additional Information" 
          icon={<DocumentTextIcon className="w-5 h-5" />}
          collapsible
          defaultExpanded={false}
        >
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Personal Details</h4>
              <div className="space-y-2 text-sm">
                <div><span className="text-gray-600">Aadhar:</span> {patient.aadharNumber}</div>
                <div><span className="text-gray-600">Occupation:</span> {patient.occupation || 'N/A'}</div>
                <div><span className="text-gray-600">Marital Status:</span> {patient.maritalStatus || 'N/A'}</div>
                <div><span className="text-gray-600">City:</span> {patient.city || 'N/A'}</div>
                <div><span className="text-gray-600">State:</span> {patient.state || 'N/A'}</div>
                <div><span className="text-gray-600">Pincode:</span> {patient.pincode || 'N/A'}</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-gray-900 mb-3">Medical History</h4>
              <div className="space-y-2 text-sm">
                <div><span className="text-gray-600">Created:</span> {new Date(patient.createdAt).toLocaleDateString()}</div>
                <div><span className="text-gray-600">Last Updated:</span> {new Date(patient.updatedAt).toLocaleDateString()}</div>
                {patient.notes && (
                  <div>
                    <span className="text-gray-600">Notes:</span>
                    <p className="mt-1 text-gray-900">{patient.notes}</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </InfoCard>
      </motion.div>
    </motion.div>
  );
};

export default PatientDetailPage;
