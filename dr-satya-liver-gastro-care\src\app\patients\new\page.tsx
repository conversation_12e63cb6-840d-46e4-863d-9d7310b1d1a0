'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { 
  UserIcon, 
  PhoneIcon, 
  EnvelopeIcon,
  MapPinIcon,
  ScaleIcon,
  HeartIcon,
  DocumentTextIcon,
  CameraIcon
} from '@heroicons/react/24/outline';
import { toast } from 'react-hot-toast';
import { InfoCard } from '@/components/ui/Card';
import { ButtonLoading } from '@/components/ui/Loading';

// Validation schema
const patientSchema = z.object({
  // Basic Information
  firstName: z.string().min(2, 'First name must be at least 2 characters'),
  lastName: z.string().min(2, 'Last name must be at least 2 characters'),
  dateOfBirth: z.string().min(1, 'Date of birth is required'),
  gender: z.enum(['Male', 'Female', 'Other'], { required_error: 'Gender is required' }),
  aadharNumber: z.string().regex(/^\d{12}$/, 'Aadhar number must be exactly 12 digits'),
  mobileNumber: z.string().regex(/^\d{10}$/, 'Mobile number must be exactly 10 digits'),
  email: z.string().email('Please enter a valid email address').optional().or(z.literal('')),
  address: z.string().min(10, 'Address must be at least 10 characters'),
  occupation: z.string().optional(),

  // Physical Information
  heightCm: z.number().min(50, 'Height must be at least 50 cm').max(250, 'Height must be less than 250 cm'),
  weightKg: z.number().min(10, 'Weight must be at least 10 kg').max(300, 'Weight must be less than 300 kg'),
  bloodGroup: z.enum(['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'], { required_error: 'Blood group is required' }),

  // Medical Scores
  charlsonIndex: z.number().min(0).max(37).optional(),
  asaGrade: z.number().min(1).max(6).optional(),
  ecogGrade: z.number().min(0).max(5).optional(),

  // Comorbidities
  comorbidities: z.array(z.string()).optional(),
});

type PatientFormData = z.infer<typeof patientSchema>;

const NewPatientPage: React.FC = () => {
  const [currentSection, setCurrentSection] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedComorbidities, setSelectedComorbidities] = useState<string[]>([]);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors },
    setValue,
  } = useForm<PatientFormData>({
    resolver: zodResolver(patientSchema),
    defaultValues: {
      charlsonIndex: 0,
      asaGrade: 1,
      ecogGrade: 0,
      comorbidities: [],
    },
  });

  const watchHeight = watch('heightCm');
  const watchWeight = watch('weightKg');

  // Calculate BMI
  const calculateBMI = () => {
    if (watchHeight && watchWeight) {
      const heightInMeters = watchHeight / 100;
      const bmi = watchWeight / (heightInMeters * heightInMeters);
      return bmi.toFixed(1);
    }
    return null;
  };

  // Calculate age from date of birth
  const calculateAge = (dateOfBirth: string) => {
    if (!dateOfBirth) return null;
    const today = new Date();
    const birthDate = new Date(dateOfBirth);
    let age = today.getFullYear() - birthDate.getFullYear();
    const monthDiff = today.getMonth() - birthDate.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
      age--;
    }
    return age;
  };

  const mockComorbidities = [
    { id: '1', name: 'Diabetes Mellitus', category: 'Endocrine' },
    { id: '2', name: 'Hypertension', category: 'Cardiovascular' },
    { id: '3', name: 'Heart Disease', category: 'Cardiovascular' },
    { id: '4', name: 'Chronic Kidney Disease', category: 'Renal' },
    { id: '5', name: 'Liver Disease', category: 'Hepatic' },
    { id: '6', name: 'COPD', category: 'Respiratory' },
    { id: '7', name: 'Asthma', category: 'Respiratory' },
    { id: '8', name: 'Obesity', category: 'Metabolic' },
  ];

  const sections = [
    {
      title: 'Basic Information',
      icon: <UserIcon className="w-5 h-5" />,
      description: 'Personal details and contact information',
    },
    {
      title: 'Physical Information',
      icon: <ScaleIcon className="w-5 h-5" />,
      description: 'Height, weight, and blood group',
    },
    {
      title: 'Medical Information',
      icon: <HeartIcon className="w-5 h-5" />,
      description: 'Medical scores and comorbidities',
    },
    {
      title: 'Review & Submit',
      icon: <DocumentTextIcon className="w-5 h-5" />,
      description: 'Review all information before submitting',
    },
  ];

  const onSubmit = async (data: PatientFormData) => {
    setIsLoading(true);
    try {
      // Calculate BMI
      let bmi = null;
      if (data.heightCm && data.weightKg) {
        const heightInMeters = data.heightCm / 100;
        bmi = data.weightKg / (heightInMeters * heightInMeters);
      }

      const patientData = {
        firstName: data.firstName,
        lastName: data.lastName,
        dateOfBirth: data.dateOfBirth,
        gender: data.gender,
        aadharNumber: data.aadharNumber,
        mobileNumber: data.mobileNumber,
        email: data.email || null,
        address: data.address,
        occupation: data.occupation || null,
        height: data.heightCm,
        weight: data.weightKg,
        bmi: bmi ? parseFloat(bmi.toFixed(2)) : null,
        bloodGroup: data.bloodGroup,
        charlsonIndex: data.charlsonIndex || null,
        asaGrade: data.asaGrade || null,
        ecogGrade: data.ecogGrade || null,
        comorbidities: selectedComorbidities,
        createdBy: 'current-user-id' // This should come from auth context
      };

      const response = await fetch('/api/patients', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(patientData),
      });

      const result = await response.json();

      if (result.success) {
        toast.success('Patient registered successfully!');
        // Redirect to patient detail page
        window.location.href = `/patients/${result.data.id}`;
      } else {
        toast.error(result.error || 'Failed to register patient');
      }
    } catch (error) {
      console.error('Error registering patient:', error);
      toast.error('Failed to register patient. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const nextSection = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(currentSection + 1);
    }
  };

  const prevSection = () => {
    if (currentSection > 0) {
      setCurrentSection(currentSection - 1);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      className="max-w-4xl mx-auto space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Add New Patient
        </h1>
        <p className="text-gray-600">
          Register a new patient in the system with comprehensive medical information.
        </p>
      </motion.div>

      {/* Progress Steps */}
      <motion.div className="medical-card p-6" variants={itemVariants}>
        <div className="flex items-center justify-between">
          {sections.map((section, index) => (
            <div
              key={index}
              className={`flex items-center ${index < sections.length - 1 ? 'flex-1' : ''}`}
            >
              <div className="flex items-center">
                <div
                  className={`w-10 h-10 rounded-full flex items-center justify-center ${
                    index <= currentSection
                      ? 'bg-blue-600 text-white'
                      : 'bg-gray-200 text-gray-500'
                  }`}
                >
                  {index < currentSection ? (
                    <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  ) : (
                    section.icon
                  )}
                </div>
                <div className="ml-3 hidden sm:block">
                  <p className={`text-sm font-medium ${
                    index <= currentSection ? 'text-blue-600' : 'text-gray-500'
                  }`}>
                    {section.title}
                  </p>
                  <p className="text-xs text-gray-500">{section.description}</p>
                </div>
              </div>
              {index < sections.length - 1 && (
                <div className={`flex-1 h-0.5 mx-4 ${
                  index < currentSection ? 'bg-blue-600' : 'bg-gray-200'
                }`} />
              )}
            </div>
          ))}
        </div>
      </motion.div>

      {/* Form */}
      <form onSubmit={handleSubmit(onSubmit)}>
        <motion.div variants={itemVariants}>
          {/* Section 0: Basic Information */}
          {currentSection === 0 && (
            <InfoCard title="Basic Information" icon={<UserIcon className="w-5 h-5" />}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="form-label">First Name *</label>
                  <input
                    {...register('firstName')}
                    className={`form-input ${errors.firstName ? 'error' : ''}`}
                    placeholder="Enter first name"
                  />
                  {errors.firstName && (
                    <p className="mt-1 text-sm text-red-600">{errors.firstName.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Last Name *</label>
                  <input
                    {...register('lastName')}
                    className={`form-input ${errors.lastName ? 'error' : ''}`}
                    placeholder="Enter last name"
                  />
                  {errors.lastName && (
                    <p className="mt-1 text-sm text-red-600">{errors.lastName.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Date of Birth *</label>
                  <input
                    {...register('dateOfBirth')}
                    type="date"
                    className={`form-input ${errors.dateOfBirth ? 'error' : ''}`}
                  />
                  {errors.dateOfBirth && (
                    <p className="mt-1 text-sm text-red-600">{errors.dateOfBirth.message}</p>
                  )}
                  {watch('dateOfBirth') && (
                    <p className="mt-1 text-sm text-blue-600">
                      Age: {calculateAge(watch('dateOfBirth'))} years
                    </p>
                  )}
                </div>

                <div>
                  <label className="form-label">Gender *</label>
                  <select
                    {...register('gender')}
                    className={`form-input ${errors.gender ? 'error' : ''}`}
                  >
                    <option value="">Select gender</option>
                    <option value="Male">Male</option>
                    <option value="Female">Female</option>
                    <option value="Other">Other</option>
                  </select>
                  {errors.gender && (
                    <p className="mt-1 text-sm text-red-600">{errors.gender.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Aadhar Number *</label>
                  <input
                    {...register('aadharNumber')}
                    className={`form-input ${errors.aadharNumber ? 'error' : ''}`}
                    placeholder="Enter 12-digit Aadhar number"
                    maxLength={12}
                  />
                  {errors.aadharNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.aadharNumber.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Mobile Number *</label>
                  <input
                    {...register('mobileNumber')}
                    className={`form-input ${errors.mobileNumber ? 'error' : ''}`}
                    placeholder="Enter 10-digit mobile number"
                    maxLength={10}
                  />
                  {errors.mobileNumber && (
                    <p className="mt-1 text-sm text-red-600">{errors.mobileNumber.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Email Address</label>
                  <input
                    {...register('email')}
                    type="email"
                    className={`form-input ${errors.email ? 'error' : ''}`}
                    placeholder="Enter email address (optional)"
                  />
                  {errors.email && (
                    <p className="mt-1 text-sm text-red-600">{errors.email.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Occupation</label>
                  <input
                    {...register('occupation')}
                    className="form-input"
                    placeholder="Enter occupation"
                  />
                </div>

                <div className="md:col-span-2">
                  <label className="form-label">Address *</label>
                  <textarea
                    {...register('address')}
                    className={`form-input ${errors.address ? 'error' : ''}`}
                    placeholder="Enter complete address"
                    rows={3}
                  />
                  {errors.address && (
                    <p className="mt-1 text-sm text-red-600">{errors.address.message}</p>
                  )}
                </div>
              </div>
            </InfoCard>
          )}

          {/* Section 1: Physical Information */}
          {currentSection === 1 && (
            <InfoCard title="Physical Information" icon={<ScaleIcon className="w-5 h-5" />}>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="form-label">Height (cm) *</label>
                  <input
                    {...register('heightCm', { valueAsNumber: true })}
                    type="number"
                    className={`form-input ${errors.heightCm ? 'error' : ''}`}
                    placeholder="Enter height in centimeters"
                    min="50"
                    max="250"
                  />
                  {errors.heightCm && (
                    <p className="mt-1 text-sm text-red-600">{errors.heightCm.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Weight (kg) *</label>
                  <input
                    {...register('weightKg', { valueAsNumber: true })}
                    type="number"
                    className={`form-input ${errors.weightKg ? 'error' : ''}`}
                    placeholder="Enter weight in kilograms"
                    min="10"
                    max="300"
                    step="0.1"
                  />
                  {errors.weightKg && (
                    <p className="mt-1 text-sm text-red-600">{errors.weightKg.message}</p>
                  )}
                </div>

                <div>
                  <label className="form-label">Blood Group *</label>
                  <select
                    {...register('bloodGroup')}
                    className={`form-input ${errors.bloodGroup ? 'error' : ''}`}
                  >
                    <option value="">Select blood group</option>
                    <option value="A+">A+</option>
                    <option value="A-">A-</option>
                    <option value="B+">B+</option>
                    <option value="B-">B-</option>
                    <option value="AB+">AB+</option>
                    <option value="AB-">AB-</option>
                    <option value="O+">O+</option>
                    <option value="O-">O-</option>
                  </select>
                  {errors.bloodGroup && (
                    <p className="mt-1 text-sm text-red-600">{errors.bloodGroup.message}</p>
                  )}
                </div>

                {/* BMI Display */}
                {watchHeight && watchWeight && (
                  <div>
                    <label className="form-label">BMI (Calculated)</label>
                    <div className="form-input bg-gray-50 text-gray-700">
                      {calculateBMI()}
                    </div>
                    <p className="mt-1 text-sm text-gray-600">
                      Automatically calculated from height and weight
                    </p>
                  </div>
                )}
              </div>
            </InfoCard>
          )}

          {/* Section 2: Medical Information */}
          {currentSection === 2 && (
            <InfoCard title="Medical Information" icon={<HeartIcon className="w-5 h-5" />}>
              <div className="space-y-6">
                {/* Medical Scores */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Medical Scores</h4>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div>
                      <label className="form-label">Charlson Index (0-37)</label>
                      <input
                        {...register('charlsonIndex', { valueAsNumber: true })}
                        type="number"
                        className="form-input"
                        placeholder="0"
                        min="0"
                        max="37"
                      />
                    </div>

                    <div>
                      <label className="form-label">ASA Grade (1-6)</label>
                      <select
                        {...register('asaGrade', { valueAsNumber: true })}
                        className="form-input"
                      >
                        <option value="">Select ASA Grade</option>
                        <option value={1}>1 - Normal healthy patient</option>
                        <option value={2}>2 - Mild systemic disease</option>
                        <option value={3}>3 - Severe systemic disease</option>
                        <option value={4}>4 - Life-threatening disease</option>
                        <option value={5}>5 - Moribund patient</option>
                        <option value={6}>6 - Brain-dead patient</option>
                      </select>
                    </div>

                    <div>
                      <label className="form-label">ECOG Grade (0-5)</label>
                      <select
                        {...register('ecogGrade', { valueAsNumber: true })}
                        className="form-input"
                      >
                        <option value="">Select ECOG Grade</option>
                        <option value={0}>0 - Fully active</option>
                        <option value={1}>1 - Restricted in strenuous activity</option>
                        <option value={2}>2 - Ambulatory, up >50% of time</option>
                        <option value={3}>3 - Confined to bed/chair >50% of time</option>
                        <option value={4}>4 - Completely disabled</option>
                        <option value={5}>5 - Dead</option>
                      </select>
                    </div>
                  </div>
                </div>

                {/* Comorbidities */}
                <div>
                  <h4 className="font-semibold text-gray-900 mb-4">Comorbidities</h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                    {mockComorbidities.map((comorbidity) => (
                      <label key={comorbidity.id} className="flex items-center space-x-3 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                        <input
                          type="checkbox"
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                          checked={selectedComorbidities.includes(comorbidity.id)}
                          onChange={(e) => {
                            if (e.target.checked) {
                              setSelectedComorbidities([...selectedComorbidities, comorbidity.id]);
                            } else {
                              setSelectedComorbidities(selectedComorbidities.filter(id => id !== comorbidity.id));
                            }
                          }}
                        />
                        <div>
                          <p className="text-sm font-medium text-gray-900">{comorbidity.name}</p>
                          <p className="text-xs text-gray-500">{comorbidity.category}</p>
                        </div>
                      </label>
                    ))}
                  </div>
                </div>
              </div>
            </InfoCard>
          )}

          {/* Section 3: Review & Submit */}
          {currentSection === 3 && (
            <InfoCard title="Review & Submit" icon={<DocumentTextIcon className="w-5 h-5" />}>
              <div className="space-y-6">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-semibold text-blue-900 mb-2">Review Patient Information</h4>
                  <p className="text-sm text-blue-700">
                    Please review all the information below before submitting the patient registration.
                  </p>
                </div>

                {/* Summary sections would go here */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Basic Information</h5>
                    <div className="text-sm space-y-1">
                      <p><span className="text-gray-600">Name:</span> {watch('firstName')} {watch('lastName')}</p>
                      <p><span className="text-gray-600">DOB:</span> {watch('dateOfBirth')}</p>
                      <p><span className="text-gray-600">Gender:</span> {watch('gender')}</p>
                      <p><span className="text-gray-600">Mobile:</span> {watch('mobileNumber')}</p>
                      <p><span className="text-gray-600">Email:</span> {watch('email') || 'N/A'}</p>
                    </div>
                  </div>

                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Physical Information</h5>
                    <div className="text-sm space-y-1">
                      <p><span className="text-gray-600">Height:</span> {watch('heightCm')} cm</p>
                      <p><span className="text-gray-600">Weight:</span> {watch('weightKg')} kg</p>
                      <p><span className="text-gray-600">BMI:</span> {calculateBMI()}</p>
                      <p><span className="text-gray-600">Blood Group:</span> {watch('bloodGroup')}</p>
                    </div>
                  </div>
                </div>

                {selectedComorbidities.length > 0 && (
                  <div>
                    <h5 className="font-medium text-gray-900 mb-2">Selected Comorbidities</h5>
                    <div className="flex flex-wrap gap-2">
                      {selectedComorbidities.map(id => {
                        const comorbidity = mockComorbidities.find(c => c.id === id);
                        return comorbidity ? (
                          <span key={id} className="px-3 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                            {comorbidity.name}
                          </span>
                        ) : null;
                      })}
                    </div>
                  </div>
                )}
              </div>
            </InfoCard>
          )}

          {/* Navigation Buttons */}
          <div className="flex justify-between mt-6">
            <button
              type="button"
              onClick={prevSection}
              disabled={currentSection === 0}
              className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
            >
              Previous
            </button>
            
            {currentSection < sections.length - 1 ? (
              <button
                type="button"
                onClick={nextSection}
                className="btn-primary"
              >
                Next
              </button>
            ) : (
              <ButtonLoading
                loading={isLoading}
                className="btn-primary"
                disabled={isLoading}
              >
                {isLoading ? 'Registering...' : 'Register Patient'}
              </ButtonLoading>
            )}
          </div>
        </motion.div>
      </form>
    </motion.div>
  );
};

export default NewPatientPage;
