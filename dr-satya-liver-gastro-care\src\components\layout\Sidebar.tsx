'use client';

import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { 
  HomeIcon, 
  UserGroupIcon, 
  UserPlusIcon, 
  ChartBarIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  Bars3Icon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import Logo from '../ui/Logo';
import { useUIStore, useAuthStore } from '@/store';

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {
  const pathname = usePathname();
  const { sidebarOpen, toggleSidebar } = useUIStore();
  const { user, logout } = useAuthStore();

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: HomeIcon,
      description: 'Overview and statistics'
    },
    {
      name: 'Patients',
      href: '/patients',
      icon: UserGroupIcon,
      description: 'View all patients'
    },
    {
      name: 'Add Patient',
      href: '/patients/new',
      icon: UserPlusIcon,
      description: 'Register new patient'
    },
    {
      name: 'Analytics',
      href: '/analytics',
      icon: ChartBarIcon,
      description: 'Medical reports & insights'
    },
    {
      name: 'Settings',
      href: '/settings',
      icon: Cog6ToothIcon,
      description: 'Application settings'
    }
  ];

  const sidebarVariants = {
    open: {
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      x: "-100%",
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    }
  };

  const itemVariants = {
    open: {
      opacity: 1,
      x: 0,
      transition: {
        type: "spring",
        stiffness: 300,
        damping: 30
      }
    },
    closed: {
      opacity: 0,
      x: -20
    }
  };

  const handleLogout = () => {
    logout();
    // Redirect to login page
    window.location.href = '/login';
  };

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {sidebarOpen && (
          <motion.div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={toggleSidebar}
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.aside
        className={`fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 z-50 lg:relative lg:translate-x-0 ${className}`}
        variants={sidebarVariants}
        animate={sidebarOpen ? "open" : "closed"}
        initial="closed"
      >
        <div className="flex flex-col h-full">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <Logo size="md" showText />
            <button
              onClick={toggleSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 lg:hidden"
            >
              <XMarkIcon className="w-6 h-6 text-gray-600" />
            </button>
          </div>

          {/* User Info */}
          {user && (
            <motion.div
              className="p-6 border-b border-gray-200"
              variants={itemVariants}
            >
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white font-semibold">
                  {user?.firstName?.[0]?.toUpperCase() || 'D'}{user?.lastName?.[0]?.toUpperCase() || 'R'}
                </div>
                <div>
                  <p className="font-semibold text-gray-900">{user?.firstName || 'Doctor'} {user?.lastName || ''}</p>
                  <p className="text-sm text-gray-500 capitalize">{user?.role || 'doctor'}</p>
                </div>
              </div>
            </motion.div>
          )}

          {/* Navigation */}
          <nav className="flex-1 p-6">
            <motion.ul className="space-y-2">
              {navigationItems.map((item, index) => {
                const isActive = pathname === item.href;
                const Icon = item.icon;

                return (
                  <motion.li
                    key={item.name}
                    variants={itemVariants}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      href={item.href}
                      className={`group flex items-center px-4 py-3 rounded-lg transition-all duration-200 ${
                        isActive
                          ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-700'
                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                      }`}
                    >
                      <Icon 
                        className={`w-6 h-6 mr-3 transition-colors ${
                          isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-600'
                        }`} 
                      />
                      <div className="flex-1">
                        <p className={`font-medium ${isActive ? 'text-blue-700' : ''}`}>
                          {item.name}
                        </p>
                        <p className="text-xs text-gray-500 mt-0.5">
                          {item.description}
                        </p>
                      </div>
                      {isActive && (
                        <motion.div
                          className="w-2 h-2 bg-blue-700 rounded-full"
                          layoutId="activeIndicator"
                          transition={{ type: "spring", stiffness: 300, damping: 30 }}
                        />
                      )}
                    </Link>
                  </motion.li>
                );
              })}
            </motion.ul>
          </nav>

          {/* Footer */}
          <motion.div
            className="p-6 border-t border-gray-200"
            variants={itemVariants}
          >
            <button
              onClick={handleLogout}
              className="w-full flex items-center px-4 py-3 text-gray-700 hover:bg-red-50 hover:text-red-600 rounded-lg transition-all duration-200 group"
            >
              <ArrowRightOnRectangleIcon className="w-6 h-6 mr-3 text-gray-400 group-hover:text-red-600" />
              <span className="font-medium">Sign Out</span>
            </button>
            
            <div className="mt-4 text-center">
              <p className="text-xs text-gray-400">
                Dr Satya's Liver & Gastro Care
              </p>
              <p className="text-xs text-gray-400">
                Version 1.0.0
              </p>
            </div>
          </motion.div>
        </div>
      </motion.aside>

      {/* Mobile Menu Button */}
      <motion.button
        className="fixed top-4 left-4 z-50 p-3 bg-white rounded-lg shadow-lg lg:hidden"
        onClick={toggleSidebar}
        whileHover={{ scale: 1.05 }}
        whileTap={{ scale: 0.95 }}
      >
        <Bars3Icon className="w-6 h-6 text-gray-600" />
      </motion.button>
    </>
  );
};

export default Sidebar;
