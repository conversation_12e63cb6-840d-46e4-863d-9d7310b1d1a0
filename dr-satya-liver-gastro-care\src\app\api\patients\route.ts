import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/database';
import { v4 as uuidv4 } from 'uuid';

// GET - Fetch all patients
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const search = searchParams.get('search') || '';
    const limit = parseInt(searchParams.get('limit') || '50');
    const offset = parseInt(searchParams.get('offset') || '0');

    const client = await pool.connect();
    
    try {
      let query = `
        SELECT 
          id,
          first_name,
          middle_name,
          last_name,
          date_of_birth,
          gender,
          aadhar_number,
          mobile_number,
          email,
          address,
          height,
          weight,
          bmi,
          blood_group,
          profile_image_url,
          created_at,
          updated_at
        FROM patients
      `;
      
      const params: any[] = [];
      
      if (search) {
        query += ` WHERE 
          LOWER(first_name || ' ' || COALESCE(middle_name, '') || ' ' || last_name) LIKE LOWER($1) 
          OR mobile_number LIKE $1 
          OR email LIKE LOWER($1)
        `;
        params.push(`%${search}%`);
      }
      
      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
      params.push(limit, offset);

      const result = await client.query(query, params);
      
      // Transform data to match frontend expectations
      const patients = result.rows.map(row => ({
        id: row.id,
        fullName: `${row.first_name} ${row.middle_name ? row.middle_name + ' ' : ''}${row.last_name}`.trim(),
        patientId: row.id.substring(0, 8).toUpperCase(), // Use first 8 chars of ID as patient ID
        firstName: row.first_name,
        middleName: row.middle_name,
        lastName: row.last_name,
        dateOfBirth: row.date_of_birth,
        age: row.date_of_birth ? new Date().getFullYear() - new Date(row.date_of_birth).getFullYear() : null,
        gender: row.gender,
        aadharNumber: row.aadhar_number,
        mobileNumber: row.mobile_number,
        email: row.email,
        address: row.address,
        height: row.height,
        weight: row.weight,
        bmi: row.bmi,
        bloodGroup: row.blood_group,
        profileImageUrl: row.profile_image_url,
        createdAt: row.created_at,
        updatedAt: row.updated_at,
        lastVisit: row.created_at ? new Date(row.created_at).toISOString().split('T')[0] : null
      }));

      // Get total count for pagination
      let countQuery = 'SELECT COUNT(*) FROM patients';
      const countParams: any[] = [];
      
      if (search) {
        countQuery += ` WHERE 
          LOWER(first_name || ' ' || COALESCE(middle_name, '') || ' ' || last_name) LIKE LOWER($1) 
          OR mobile_number LIKE $1 
          OR email LIKE LOWER($1)
        `;
        countParams.push(`%${search}%`);
      }
      
      const countResult = await client.query(countQuery, countParams);
      const total = parseInt(countResult.rows[0].count);

      return NextResponse.json({
        success: true,
        data: patients,
        pagination: {
          total,
          limit,
          offset,
          hasMore: offset + limit < total
        }
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching patients:', error);
    return NextResponse.json(
      { error: 'Failed to fetch patients' },
      { status: 500 }
    );
  }
}

// POST - Create new patient
export async function POST(request: NextRequest) {
  try {
    const patientData = await request.json();

    const client = await pool.connect();
    
    try {
      const id = uuidv4();
      
      const query = `
        INSERT INTO patients (
          id, first_name, middle_name, last_name, date_of_birth, gender,
          aadhar_number, mobile_number, email, address, height, weight,
          blood_group, charlson_index, asa_grade, ecog_grade, created_by,
          created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19
        ) RETURNING *
      `;

      // Calculate BMI if height and weight are provided
      let bmi = null;
      if (patientData.height && patientData.weight) {
        const heightInMeters = patientData.height / 100;
        bmi = patientData.weight / (heightInMeters * heightInMeters);
      }

      const values = [
        id,
        patientData.firstName,
        patientData.middleName || null,
        patientData.lastName,
        patientData.dateOfBirth,
        patientData.gender,
        patientData.aadharNumber,
        patientData.mobileNumber,
        patientData.email || null,
        patientData.address,
        patientData.height || null,
        patientData.weight || null,
        patientData.bloodGroup || null,
        patientData.charlsonIndex || null,
        patientData.asaGrade || null,
        patientData.ecogGrade || null,
        patientData.createdBy || 'system',
        new Date().toISOString(),
        new Date().toISOString()
      ];

      const result = await client.query(query, values);
      
      return NextResponse.json({
        success: true,
        data: result.rows[0],
        message: 'Patient created successfully'
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error creating patient:', error);
    return NextResponse.json(
      { error: 'Failed to create patient' },
      { status: 500 }
    );
  }
}
