{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/lib/database.ts"], "sourcesContent": ["// Database connection using direct PostgreSQL connection\nimport { Pool } from 'pg';\n\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: {\n    rejectUnauthorized: false\n  }\n});\n\nexport { pool };\n\n// Database helper functions\nexport const db = {\n  query: async (text: string, params?: any[]) => {\n    const client = await pool.connect();\n    try {\n      const result = await client.query(text, params);\n      return result;\n    } finally {\n      client.release();\n    }\n  },\n  \n  getClient: async () => {\n    return await pool.connect();\n  }\n};\n\n// Test database connection\nexport const testConnection = async () => {\n  try {\n    const result = await db.query('SELECT NOW()');\n    console.log('Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AACzD;;;;;;AAEA,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK;QACH,oBAAoB;IACtB;AACF;;AAKO,MAAM,KAAK;IAChB,OAAO,OAAO,MAAc;QAC1B,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,MAAM;YACxC,OAAO;QACT,SAAU;YACR,OAAO,OAAO;QAChB;IACF;IAEA,WAAW;QACT,OAAO,MAAM,KAAK,OAAO;IAC3B;AACF;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,SAAS,MAAM,GAAG,KAAK,CAAC;QAC9B,QAAQ,GAAG,CAAC,oCAAoC,OAAO,IAAI,CAAC,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 136, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/api/patients/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { pool } from '@/lib/database';\nimport { v4 as uuidv4 } from 'uuid';\n\n// GET - Fetch all patients\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const search = searchParams.get('search') || '';\n    const limit = parseInt(searchParams.get('limit') || '50');\n    const offset = parseInt(searchParams.get('offset') || '0');\n\n    const client = await pool.connect();\n    \n    try {\n      let query = `\n        SELECT \n          id,\n          first_name,\n          middle_name,\n          last_name,\n          date_of_birth,\n          gender,\n          aadhar_number,\n          mobile_number,\n          email,\n          address,\n          height,\n          weight,\n          bmi,\n          blood_group,\n          profile_image_url,\n          created_at,\n          updated_at\n        FROM patients\n      `;\n      \n      const params: any[] = [];\n      \n      if (search) {\n        query += ` WHERE \n          LOWER(first_name || ' ' || COALESCE(middle_name, '') || ' ' || last_name) LIKE LOWER($1) \n          OR mobile_number LIKE $1 \n          OR email LIKE LOWER($1)\n        `;\n        params.push(`%${search}%`);\n      }\n      \n      query += ` ORDER BY created_at DESC LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;\n      params.push(limit, offset);\n\n      const result = await client.query(query, params);\n      \n      // Transform data to match frontend expectations\n      const patients = result.rows.map(row => ({\n        id: row.id,\n        fullName: `${row.first_name} ${row.middle_name ? row.middle_name + ' ' : ''}${row.last_name}`.trim(),\n        patientId: row.id.substring(0, 8).toUpperCase(), // Use first 8 chars of ID as patient ID\n        firstName: row.first_name,\n        middleName: row.middle_name,\n        lastName: row.last_name,\n        dateOfBirth: row.date_of_birth,\n        age: row.date_of_birth ? new Date().getFullYear() - new Date(row.date_of_birth).getFullYear() : null,\n        gender: row.gender,\n        aadharNumber: row.aadhar_number,\n        mobileNumber: row.mobile_number,\n        email: row.email,\n        address: row.address,\n        height: row.height,\n        weight: row.weight,\n        bmi: row.bmi,\n        bloodGroup: row.blood_group,\n        profileImageUrl: row.profile_image_url,\n        createdAt: row.created_at,\n        updatedAt: row.updated_at,\n        lastVisit: row.created_at ? new Date(row.created_at).toISOString().split('T')[0] : null\n      }));\n\n      // Get total count for pagination\n      let countQuery = 'SELECT COUNT(*) FROM patients';\n      const countParams: any[] = [];\n      \n      if (search) {\n        countQuery += ` WHERE \n          LOWER(first_name || ' ' || COALESCE(middle_name, '') || ' ' || last_name) LIKE LOWER($1) \n          OR mobile_number LIKE $1 \n          OR email LIKE LOWER($1)\n        `;\n        countParams.push(`%${search}%`);\n      }\n      \n      const countResult = await client.query(countQuery, countParams);\n      const total = parseInt(countResult.rows[0].count);\n\n      return NextResponse.json({\n        success: true,\n        data: patients,\n        pagination: {\n          total,\n          limit,\n          offset,\n          hasMore: offset + limit < total\n        }\n      });\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Error fetching patients:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch patients' },\n      { status: 500 }\n    );\n  }\n}\n\n// POST - Create new patient\nexport async function POST(request: NextRequest) {\n  try {\n    const patientData = await request.json();\n\n    const client = await pool.connect();\n    \n    try {\n      const id = uuidv4();\n      \n      const query = `\n        INSERT INTO patients (\n          id, first_name, middle_name, last_name, date_of_birth, gender,\n          aadhar_number, mobile_number, email, address, height, weight,\n          blood_group, charlson_index, asa_grade, ecog_grade, created_by,\n          created_at, updated_at\n        ) VALUES (\n          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19\n        ) RETURNING *\n      `;\n\n      // Calculate BMI if height and weight are provided\n      let bmi = null;\n      if (patientData.height && patientData.weight) {\n        const heightInMeters = patientData.height / 100;\n        bmi = patientData.weight / (heightInMeters * heightInMeters);\n      }\n\n      const values = [\n        id,\n        patientData.firstName,\n        patientData.middleName || null,\n        patientData.lastName,\n        patientData.dateOfBirth,\n        patientData.gender,\n        patientData.aadharNumber,\n        patientData.mobileNumber,\n        patientData.email || null,\n        patientData.address,\n        patientData.height || null,\n        patientData.weight || null,\n        patientData.bloodGroup || null,\n        patientData.charlsonIndex || null,\n        patientData.asaGrade || null,\n        patientData.ecogGrade || null,\n        patientData.createdBy || 'system',\n        new Date().toISOString(),\n        new Date().toISOString()\n      ];\n\n      const result = await client.query(query, values);\n      \n      return NextResponse.json({\n        success: true,\n        data: result.rows[0],\n        message: 'Patient created successfully'\n      });\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Error creating patient:', error);\n    return NextResponse.json(\n      { error: 'Failed to create patient' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,SAAS,aAAa,GAAG,CAAC,aAAa;QAEtD,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,IAAI,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;MAoBb,CAAC;YAED,MAAM,SAAgB,EAAE;YAExB,IAAI,QAAQ;gBACV,SAAS,CAAC;;;;QAIV,CAAC;gBACD,OAAO,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YAC3B;YAEA,SAAS,CAAC,iCAAiC,EAAE,OAAO,MAAM,GAAG,EAAE,SAAS,EAAE,OAAO,MAAM,GAAG,GAAG;YAC7F,OAAO,IAAI,CAAC,OAAO;YAEnB,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,OAAO;YAEzC,gDAAgD;YAChD,MAAM,WAAW,OAAO,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACvC,IAAI,IAAI,EAAE;oBACV,UAAU,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC,IAAI;oBAClG,WAAW,IAAI,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;oBAC7C,WAAW,IAAI,UAAU;oBACzB,YAAY,IAAI,WAAW;oBAC3B,UAAU,IAAI,SAAS;oBACvB,aAAa,IAAI,aAAa;oBAC9B,KAAK,IAAI,aAAa,GAAG,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,IAAI,aAAa,EAAE,WAAW,KAAK;oBAChG,QAAQ,IAAI,MAAM;oBAClB,cAAc,IAAI,aAAa;oBAC/B,cAAc,IAAI,aAAa;oBAC/B,OAAO,IAAI,KAAK;oBAChB,SAAS,IAAI,OAAO;oBACpB,QAAQ,IAAI,MAAM;oBAClB,QAAQ,IAAI,MAAM;oBAClB,KAAK,IAAI,GAAG;oBACZ,YAAY,IAAI,WAAW;oBAC3B,iBAAiB,IAAI,iBAAiB;oBACtC,WAAW,IAAI,UAAU;oBACzB,WAAW,IAAI,UAAU;oBACzB,WAAW,IAAI,UAAU,GAAG,IAAI,KAAK,IAAI,UAAU,EAAE,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE,GAAG;gBACrF,CAAC;YAED,iCAAiC;YACjC,IAAI,aAAa;YACjB,MAAM,cAAqB,EAAE;YAE7B,IAAI,QAAQ;gBACV,cAAc,CAAC;;;;QAIf,CAAC;gBACD,YAAY,IAAI,CAAC,CAAC,CAAC,EAAE,OAAO,CAAC,CAAC;YAChC;YAEA,MAAM,cAAc,MAAM,OAAO,KAAK,CAAC,YAAY;YACnD,MAAM,QAAQ,SAAS,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK;YAEhD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;gBACN,YAAY;oBACV;oBACA;oBACA;oBACA,SAAS,SAAS,QAAQ;gBAC5B;YACF;QAEF,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,cAAc,MAAM,QAAQ,IAAI;QAEtC,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,MAAM,KAAK,CAAA,GAAA,4KAAA,CAAA,KAAM,AAAD;YAEhB,MAAM,QAAQ,CAAC;;;;;;;;;MASf,CAAC;YAED,kDAAkD;YAClD,IAAI,MAAM;YACV,IAAI,YAAY,MAAM,IAAI,YAAY,MAAM,EAAE;gBAC5C,MAAM,iBAAiB,YAAY,MAAM,GAAG;gBAC5C,MAAM,YAAY,MAAM,GAAG,CAAC,iBAAiB,cAAc;YAC7D;YAEA,MAAM,SAAS;gBACb;gBACA,YAAY,SAAS;gBACrB,YAAY,UAAU,IAAI;gBAC1B,YAAY,QAAQ;gBACpB,YAAY,WAAW;gBACvB,YAAY,MAAM;gBAClB,YAAY,YAAY;gBACxB,YAAY,YAAY;gBACxB,YAAY,KAAK,IAAI;gBACrB,YAAY,OAAO;gBACnB,YAAY,MAAM,IAAI;gBACtB,YAAY,MAAM,IAAI;gBACtB,YAAY,UAAU,IAAI;gBAC1B,YAAY,aAAa,IAAI;gBAC7B,YAAY,QAAQ,IAAI;gBACxB,YAAY,SAAS,IAAI;gBACzB,YAAY,SAAS,IAAI;gBACzB,IAAI,OAAO,WAAW;gBACtB,IAAI,OAAO,WAAW;aACvB;YAED,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,OAAO;YAEzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM,OAAO,IAAI,CAAC,EAAE;gBACpB,SAAS;YACX;QAEF,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}