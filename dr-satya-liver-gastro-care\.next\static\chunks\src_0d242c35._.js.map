{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showText?: boolean;\n  className?: string;\n}\n\nconst Logo: React.FC<LogoProps> = ({ \n  size = 'md', \n  showText = true, \n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-8 h-8',\n    md: 'w-12 h-12',\n    lg: 'w-16 h-16',\n    xl: 'w-24 h-24'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-lg',\n    lg: 'text-xl',\n    xl: 'text-3xl'\n  };\n\n  return (\n    <motion.div \n      className={`flex items-center gap-3 ${className}`}\n      initial={{ opacity: 0, scale: 0.8 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.5, ease: \"easeOut\" }}\n    >\n      {/* Medical Logo Icon */}\n      <motion.div \n        className={`${sizeClasses[size]} relative`}\n        whileHover={{ scale: 1.05 }}\n        transition={{ type: \"spring\", stiffness: 300 }}\n      >\n        <svg\n          viewBox=\"0 0 100 100\"\n          className=\"w-full h-full\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          {/* Background Circle with Gradient */}\n          <defs>\n            <linearGradient id=\"logoGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#1e40af\" />\n              <stop offset=\"50%\" stopColor=\"#3b82f6\" />\n              <stop offset=\"100%\" stopColor=\"#0d9488\" />\n            </linearGradient>\n            <linearGradient id=\"liverGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#059669\" />\n              <stop offset=\"100%\" stopColor=\"#0d9488\" />\n            </linearGradient>\n          </defs>\n          \n          {/* Main Circle Background */}\n          <circle\n            cx=\"50\"\n            cy=\"50\"\n            r=\"48\"\n            fill=\"url(#logoGradient)\"\n            stroke=\"#ffffff\"\n            strokeWidth=\"2\"\n          />\n          \n          {/* Medical Cross */}\n          <g transform=\"translate(50, 50)\">\n            {/* Vertical bar of cross */}\n            <rect\n              x=\"-3\"\n              y=\"-20\"\n              width=\"6\"\n              height=\"40\"\n              fill=\"#ffffff\"\n              rx=\"3\"\n            />\n            {/* Horizontal bar of cross */}\n            <rect\n              x=\"-20\"\n              y=\"-3\"\n              width=\"40\"\n              height=\"6\"\n              fill=\"#ffffff\"\n              rx=\"3\"\n            />\n          </g>\n          \n          {/* Liver Shape (stylized) */}\n          <g transform=\"translate(50, 50)\">\n            <path\n              d=\"M -15 -8 Q -20 -12 -15 -16 Q -10 -18 -5 -16 Q 0 -14 5 -16 Q 10 -18 15 -16 Q 20 -12 15 -8 Q 12 -4 8 0 Q 5 4 0 6 Q -5 4 -8 0 Q -12 -4 -15 -8 Z\"\n              fill=\"url(#liverGradient)\"\n              opacity=\"0.3\"\n            />\n          </g>\n          \n          {/* Stethoscope accent */}\n          <g transform=\"translate(50, 50)\">\n            <circle cx=\"18\" cy=\"18\" r=\"4\" fill=\"#ffffff\" opacity=\"0.8\" />\n            <path\n              d=\"M 14 14 Q 10 10 5 12 Q 0 14 -5 12 Q -10 10 -14 14\"\n              stroke=\"#ffffff\"\n              strokeWidth=\"2\"\n              fill=\"none\"\n              opacity=\"0.8\"\n            />\n          </g>\n        </svg>\n      </motion.div>\n\n      {/* Text Logo */}\n      {showText && (\n        <motion.div \n          className=\"flex flex-col\"\n          initial={{ opacity: 0, x: -20 }}\n          animate={{ opacity: 1, x: 0 }}\n          transition={{ duration: 0.5, delay: 0.2 }}\n        >\n          <motion.h1 \n            className={`font-bold text-gray-800 leading-tight ${textSizeClasses[size]}`}\n            whileHover={{ scale: 1.02 }}\n          >\n            Dr Satya's\n          </motion.h1>\n          <motion.p \n            className={`font-medium text-blue-600 leading-tight ${\n              size === 'sm' ? 'text-xs' : \n              size === 'md' ? 'text-sm' : \n              size === 'lg' ? 'text-base' : 'text-lg'\n            }`}\n            whileHover={{ scale: 1.02 }}\n          >\n            Liver & Gastro Care\n          </motion.p>\n          {size === 'lg' || size === 'xl' ? (\n            <motion.p \n              className=\"text-xs text-gray-500 mt-1\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 0.4 }}\n            >\n              Excellence in Digestive Health\n            </motion.p>\n          ) : null}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,OAA4B;QAAC,EACjC,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,AAAC,2BAAoC,OAAV;QACtC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;;0BAG7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,AAAC,GAAoB,OAAlB,WAAW,CAAC,KAAK,EAAC;gBAChC,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,YAAY;oBAAE,MAAM;oBAAU,WAAW;gBAAI;0BAE7C,cAAA,6LAAC;oBACC,SAAQ;oBACR,WAAU;oBACV,OAAM;;sCAGN,6LAAC;;8CACC,6LAAC;oCAAe,IAAG;oCAAe,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC7D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAEhC,6LAAC;oCAAe,IAAG;oCAAgB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC9D,6LAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,6LAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;;;;;;;sCAKlC,6LAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;;;;;;sCAId,6LAAC;4BAAE,WAAU;;8CAEX,6LAAC;oCACC,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;oCACP,MAAK;oCACL,IAAG;;;;;;8CAGL,6LAAC;oCACC,GAAE;oCACF,GAAE;oCACF,OAAM;oCACN,QAAO;oCACP,MAAK;oCACL,IAAG;;;;;;;;;;;;sCAKP,6LAAC;4BAAE,WAAU;sCACX,cAAA,6LAAC;gCACC,GAAE;gCACF,MAAK;gCACL,SAAQ;;;;;;;;;;;sCAKZ,6LAAC;4BAAE,WAAU;;8CACX,6LAAC;oCAAO,IAAG;oCAAK,IAAG;oCAAK,GAAE;oCAAI,MAAK;oCAAU,SAAQ;;;;;;8CACrD,6LAAC;oCACC,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,SAAQ;;;;;;;;;;;;;;;;;;;;;;;YAOf,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;oBAAK,OAAO;gBAAI;;kCAExC,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,AAAC,yCAA8D,OAAtB,eAAe,CAAC,KAAK;wBACzE,YAAY;4BAAE,OAAO;wBAAK;kCAC3B;;;;;;kCAGD,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAW,AAAC,2CAIX,OAHC,SAAS,OAAO,YAChB,SAAS,OAAO,YAChB,SAAS,OAAO,cAAc;wBAEhC,YAAY;4BAAE,OAAO;wBAAK;kCAC3B;;;;;;oBAGA,SAAS,QAAQ,SAAS,qBACzB,6LAAC,6LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAC1B;;;;;mEAGC;;;;;;;;;;;;;AAKd;KA/IM;uCAiJS", "debugId": null}}, {"offset": {"line": 315, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, Patient, Comorbidity, DashboardStats } from '@/types';\n\n// Authentication Store\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: { email: string; password: string }) => Promise<boolean>;\n  logout: () => void;\n  setUser: (user: User) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      \n      login: async (credentials) => {\n        set({ isLoading: true });\n        try {\n          const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(credentials),\n          });\n\n          const data = await response.json();\n\n          if (response.ok && data.success) {\n            set({ user: data.user, isAuthenticated: true, isLoading: false });\n            return true;\n          }\n\n          set({ isLoading: false });\n          return false;\n        } catch (error) {\n          console.error('Login error:', error);\n          set({ isLoading: false });\n          return false;\n        }\n      },\n      \n      logout: () => {\n        set({ user: null, isAuthenticated: false });\n      },\n      \n      setUser: (user) => {\n        set({ user, isAuthenticated: true });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n\n// Patients Store\ninterface PatientsState {\n  patients: Patient[];\n  currentPatient: Patient | null;\n  isLoading: boolean;\n  searchQuery: string;\n  filters: {\n    gender?: string;\n    bloodGroup?: string;\n    ageRange?: { min: number; max: number };\n  };\n  \n  // Actions\n  setPatients: (patients: Patient[]) => void;\n  addPatient: (patient: Patient) => void;\n  updatePatient: (id: string, updates: Partial<Patient>) => void;\n  deletePatient: (id: string) => void;\n  setCurrentPatient: (patient: Patient | null) => void;\n  setSearchQuery: (query: string) => void;\n  setFilters: (filters: any) => void;\n  setLoading: (loading: boolean) => void;\n  \n  // Computed\n  filteredPatients: () => Patient[];\n}\n\nexport const usePatientsStore = create<PatientsState>((set, get) => ({\n  patients: [],\n  currentPatient: null,\n  isLoading: false,\n  searchQuery: '',\n  filters: {},\n  \n  setPatients: (patients) => set({ patients }),\n  \n  addPatient: (patient) => \n    set((state) => ({ patients: [...state.patients, patient] })),\n  \n  updatePatient: (id, updates) =>\n    set((state) => ({\n      patients: state.patients.map((p) => \n        p.id === id ? { ...p, ...updates } : p\n      ),\n      currentPatient: state.currentPatient?.id === id \n        ? { ...state.currentPatient, ...updates } \n        : state.currentPatient,\n    })),\n  \n  deletePatient: (id) =>\n    set((state) => ({\n      patients: state.patients.filter((p) => p.id !== id),\n      currentPatient: state.currentPatient?.id === id ? null : state.currentPatient,\n    })),\n  \n  setCurrentPatient: (patient) => set({ currentPatient: patient }),\n  \n  setSearchQuery: (searchQuery) => set({ searchQuery }),\n  \n  setFilters: (filters) => set({ filters }),\n  \n  setLoading: (isLoading) => set({ isLoading }),\n  \n  filteredPatients: () => {\n    const { patients, searchQuery, filters } = get();\n    \n    return patients.filter((patient) => {\n      // Search query filter\n      if (searchQuery) {\n        const query = searchQuery.toLowerCase();\n        const matchesSearch = \n          patient.fullName.toLowerCase().includes(query) ||\n          patient.patientId.toLowerCase().includes(query) ||\n          patient.mobileNumber.includes(query) ||\n          patient.email?.toLowerCase().includes(query);\n        \n        if (!matchesSearch) return false;\n      }\n      \n      // Gender filter\n      if (filters.gender && patient.gender !== filters.gender) {\n        return false;\n      }\n      \n      // Blood group filter\n      if (filters.bloodGroup && patient.bloodGroup !== filters.bloodGroup) {\n        return false;\n      }\n      \n      // Age range filter\n      if (filters.ageRange) {\n        const age = new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear();\n        if (age < filters.ageRange.min || age > filters.ageRange.max) {\n          return false;\n        }\n      }\n      \n      return true;\n    });\n  },\n}));\n\n// Comorbidities Store\ninterface ComorbiditiesState {\n  comorbidities: Comorbidity[];\n  isLoading: boolean;\n  setComorbidities: (comorbidities: Comorbidity[]) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useComorbiditiesStore = create<ComorbiditiesState>((set) => ({\n  comorbidities: [],\n  isLoading: false,\n  \n  setComorbidities: (comorbidities) => set({ comorbidities }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// Dashboard Store\ninterface DashboardState {\n  stats: DashboardStats | null;\n  isLoading: boolean;\n  setStats: (stats: DashboardStats) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useDashboardStore = create<DashboardState>((set) => ({\n  stats: null,\n  isLoading: false,\n  \n  setStats: (stats) => set({ stats }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// UI Store for global UI state\ninterface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    message: string;\n    timestamp: number;\n  }>;\n  \n  toggleSidebar: () => void;\n  setTheme: (theme: 'light' | 'dark') => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n}\n\nexport const useUIStore = create<UIState>()(\n  persist(\n    (set, get) => ({\n      sidebarOpen: true,\n      theme: 'light',\n      notifications: [],\n      \n      toggleSidebar: () => \n        set((state) => ({ sidebarOpen: !state.sidebarOpen })),\n      \n      setTheme: (theme) => set({ theme }),\n      \n      addNotification: (notification) => {\n        const id = Math.random().toString(36).substr(2, 9);\n        const timestamp = Date.now();\n        \n        set((state) => ({\n          notifications: [...state.notifications, { ...notification, id, timestamp }],\n        }));\n        \n        // Auto-remove after 5 seconds\n        setTimeout(() => {\n          set((state) => ({\n            notifications: state.notifications.filter((n) => n.id !== id),\n          }));\n        }, 5000);\n      },\n      \n      removeNotification: (id) =>\n        set((state) => ({\n          notifications: state.notifications.filter((n) => n.id !== id),\n        })),\n    }),\n    {\n      name: 'ui-storage',\n      partialize: (state) => ({ \n        sidebarOpen: state.sidebarOpen, \n        theme: state.theme \n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAaO,MAAM,eAAe,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;oBAC/B,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,iBAAiB;wBAAM,WAAW;oBAAM;oBAC/D,OAAO;gBACT;gBAEA,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT;QACF;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;YAAM;QAC3C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB;YAAK;QACpC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AA8BG,MAAM,mBAAmB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACnE,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,aAAa;QACb,SAAS,CAAC;QAEV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAE5D,eAAe,CAAC,IAAI,UAClB,IAAI,CAAC;oBAIa;uBAJF;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,IAC5B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;oBAEvC,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;oBAAC,IACtC,MAAM,cAAc;gBAC1B;;QAEF,eAAe,CAAC,KACd,IAAI,CAAC;oBAEa;uBAFF;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAChD,gBAAgB,EAAA,wBAAA,MAAM,cAAc,cAApB,4CAAA,sBAAsB,EAAE,MAAK,KAAK,OAAO,MAAM,cAAc;gBAC/E;;QAEF,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAE9D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QAEvC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,kBAAkB;YAChB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YAE3C,OAAO,SAAS,MAAM,CAAC,CAAC;gBACtB,sBAAsB;gBACtB,IAAI,aAAa;wBAMb;oBALF,MAAM,QAAQ,YAAY,WAAW;oBACrC,MAAM,gBACJ,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UACxC,QAAQ,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACzC,QAAQ,YAAY,CAAC,QAAQ,CAAC,YAC9B,iBAAA,QAAQ,KAAK,cAAb,qCAAA,eAAe,WAAW,GAAG,QAAQ,CAAC;oBAExC,IAAI,CAAC,eAAe,OAAO;gBAC7B;gBAEA,gBAAgB;gBAChB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACvD,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,QAAQ,UAAU,EAAE;oBACnE,OAAO;gBACT;gBAEA,mBAAmB;gBACnB,IAAI,QAAQ,QAAQ,EAAE;oBACpB,MAAM,MAAM,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,QAAQ,WAAW,EAAE,WAAW;oBAChF,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,EAAE;wBAC5D,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT;QACF;IACF,CAAC;AAUM,MAAM,wBAAwB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,MAAQ,CAAC;QACxE,eAAe,EAAE;QACjB,WAAW;QAEX,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAUM,MAAM,oBAAoB,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QAChE,OAAO;QACP,WAAW;QAEX,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAmBM,MAAM,aAAa,CAAA,GAAA,2IAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,gJAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,aAAa;QACb,OAAO;QACP,eAAe,EAAE;QAEjB,eAAe,IACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QAErD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,iBAAiB,CAAC;YAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChD,MAAM,YAAY,KAAK,GAAG;YAE1B,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;2BAAI,MAAM,aAAa;wBAAE;4BAAE,GAAG,YAAY;4BAAE;4BAAI;wBAAU;qBAAE;gBAC7E,CAAC;YAED,8BAA8B;YAC9B,WAAW;gBACT,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC5D,CAAC;YACH,GAAG;QACL;QAEA,oBAAoB,CAAC,KACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC5D,CAAC;IACL,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 527, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { \n  HomeIcon, \n  UserGroupIcon, \n  UserPlusIcon, \n  ChartBarIcon,\n  Cog6ToothIcon,\n  ArrowRightOnRectangleIcon,\n  Bars3Icon,\n  XMarkIcon\n} from '@heroicons/react/24/outline';\nimport Logo from '../ui/Logo';\nimport { useUIStore, useAuthStore } from '@/store';\n\ninterface SidebarProps {\n  className?: string;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {\n  const pathname = usePathname();\n  const { sidebarOpen, toggleSidebar } = useUIStore();\n  const { user, logout } = useAuthStore();\n\n  const navigationItems = [\n    {\n      name: 'Dashboard',\n      href: '/dashboard',\n      icon: HomeIcon,\n      description: 'Overview and statistics'\n    },\n    {\n      name: 'Patients',\n      href: '/patients',\n      icon: UserGroupIcon,\n      description: 'View all patients'\n    },\n    {\n      name: 'Add Patient',\n      href: '/patients/new',\n      icon: UserPlusIcon,\n      description: 'Register new patient'\n    },\n    {\n      name: 'Analytics',\n      href: '/analytics',\n      icon: ChartBarIcon,\n      description: 'Medical reports & insights'\n    },\n    {\n      name: 'Settings',\n      href: '/settings',\n      icon: Cog6ToothIcon,\n      description: 'Application settings'\n    }\n  ];\n\n  const sidebarVariants = {\n    open: {\n      x: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      }\n    },\n    closed: {\n      x: \"-100%\",\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      }\n    }\n  };\n\n  const itemVariants = {\n    open: {\n      opacity: 1,\n      x: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 300,\n        damping: 30\n      }\n    },\n    closed: {\n      opacity: 0,\n      x: -20\n    }\n  };\n\n  const handleLogout = () => {\n    logout();\n    // Redirect to login page\n    window.location.href = '/login';\n  };\n\n  return (\n    <>\n      {/* Mobile Overlay */}\n      <AnimatePresence>\n        {sidebarOpen && (\n          <motion.div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n            onClick={toggleSidebar}\n          />\n        )}\n      </AnimatePresence>\n\n      {/* Sidebar */}\n      <motion.aside\n        className={`fixed left-0 top-0 h-full w-80 bg-white border-r border-gray-200 z-50 lg:relative lg:translate-x-0 ${className}`}\n        variants={sidebarVariants}\n        animate={sidebarOpen ? \"open\" : \"closed\"}\n        initial=\"closed\"\n      >\n        <div className=\"flex flex-col h-full\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n            <Logo size=\"md\" showText />\n            <button\n              onClick={toggleSidebar}\n              className=\"p-2 rounded-lg hover:bg-gray-100 lg:hidden\"\n            >\n              <XMarkIcon className=\"w-6 h-6 text-gray-600\" />\n            </button>\n          </div>\n\n          {/* User Info */}\n          {user && (\n            <motion.div\n              className=\"p-6 border-b border-gray-200\"\n              variants={itemVariants}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 rounded-full bg-gradient-to-r from-blue-500 to-teal-500 flex items-center justify-center text-white font-semibold\">\n                  {(user.firstName[0] + user.lastName[0]).toUpperCase()}\n                </div>\n                <div>\n                  <p className=\"font-semibold text-gray-900\">{user.firstName} {user.lastName}</p>\n                  <p className=\"text-sm text-gray-500 capitalize\">{user.role}</p>\n                </div>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Navigation */}\n          <nav className=\"flex-1 p-6\">\n            <motion.ul className=\"space-y-2\">\n              {navigationItems.map((item, index) => {\n                const isActive = pathname === item.href;\n                const Icon = item.icon;\n\n                return (\n                  <motion.li\n                    key={item.name}\n                    variants={itemVariants}\n                    transition={{ delay: index * 0.1 }}\n                  >\n                    <Link\n                      href={item.href}\n                      className={`group flex items-center px-4 py-3 rounded-lg transition-all duration-200 ${\n                        isActive\n                          ? 'bg-blue-50 text-blue-700 border-r-4 border-blue-700'\n                          : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'\n                      }`}\n                    >\n                      <Icon \n                        className={`w-6 h-6 mr-3 transition-colors ${\n                          isActive ? 'text-blue-700' : 'text-gray-400 group-hover:text-blue-600'\n                        }`} \n                      />\n                      <div className=\"flex-1\">\n                        <p className={`font-medium ${isActive ? 'text-blue-700' : ''}`}>\n                          {item.name}\n                        </p>\n                        <p className=\"text-xs text-gray-500 mt-0.5\">\n                          {item.description}\n                        </p>\n                      </div>\n                      {isActive && (\n                        <motion.div\n                          className=\"w-2 h-2 bg-blue-700 rounded-full\"\n                          layoutId=\"activeIndicator\"\n                          transition={{ type: \"spring\", stiffness: 300, damping: 30 }}\n                        />\n                      )}\n                    </Link>\n                  </motion.li>\n                );\n              })}\n            </motion.ul>\n          </nav>\n\n          {/* Footer */}\n          <motion.div\n            className=\"p-6 border-t border-gray-200\"\n            variants={itemVariants}\n          >\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center px-4 py-3 text-gray-700 hover:bg-red-50 hover:text-red-600 rounded-lg transition-all duration-200 group\"\n            >\n              <ArrowRightOnRectangleIcon className=\"w-6 h-6 mr-3 text-gray-400 group-hover:text-red-600\" />\n              <span className=\"font-medium\">Sign Out</span>\n            </button>\n            \n            <div className=\"mt-4 text-center\">\n              <p className=\"text-xs text-gray-400\">\n                Dr Satya's Liver & Gastro Care\n              </p>\n              <p className=\"text-xs text-gray-400\">\n                Version 1.0.0\n              </p>\n            </div>\n          </motion.div>\n        </div>\n      </motion.aside>\n\n      {/* Mobile Menu Button */}\n      <motion.button\n        className=\"fixed top-4 left-4 z-50 p-3 bg-white rounded-lg shadow-lg lg:hidden\"\n        onClick={toggleSidebar}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n      >\n        <Bars3Icon className=\"w-6 h-6 text-gray-600\" />\n      </motion.button>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AACA;;;AAjBA;;;;;;;AAuBA,MAAM,UAAkC;QAAC,EAAE,YAAY,EAAE,EAAE;;IACzD,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,WAAW,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD;IAChD,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,eAAY,AAAD;IAEpC,MAAM,kBAAkB;QACtB;YACE,MAAM;YACN,MAAM;YACN,MAAM,kNAAA,CAAA,WAAQ;YACd,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0NAAA,CAAA,eAAY;YAClB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,0NAAA,CAAA,eAAY;YAClB,aAAa;QACf;QACA;YACE,MAAM;YACN,MAAM;YACN,MAAM,4NAAA,CAAA,gBAAa;YACnB,aAAa;QACf;KACD;IAED,MAAM,kBAAkB;QACtB,MAAM;YACJ,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM;YACJ,SAAS;YACT,GAAG;YACH,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;QACA,QAAQ;YACN,SAAS;YACT,GAAG,CAAC;QACN;IACF;IAEA,MAAM,eAAe;QACnB;QACA,yBAAyB;QACzB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,qBACE;;0BAEE,6LAAC,4LAAA,CAAA,kBAAe;0BACb,6BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;oBACnB,SAAS;;;;;;;;;;;0BAMf,6LAAC,6LAAA,CAAA,SAAM,CAAC,KAAK;gBACX,WAAW,AAAC,sGAA+G,OAAV;gBACjH,UAAU;gBACV,SAAS,cAAc,SAAS;gBAChC,SAAQ;0BAER,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,mIAAA,CAAA,UAAI;oCAAC,MAAK;oCAAK,QAAQ;;;;;;8CACxB,6LAAC;oCACC,SAAS;oCACT,WAAU;8CAEV,cAAA,6LAAC,oNAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;;;;;;;;;;;;wBAKxB,sBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,CAAC,KAAK,SAAS,CAAC,EAAE,GAAG,KAAK,QAAQ,CAAC,EAAE,EAAE,WAAW;;;;;;kDAErD,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;;oDAA+B,KAAK,SAAS;oDAAC;oDAAE,KAAK,QAAQ;;;;;;;0DAC1E,6LAAC;gDAAE,WAAU;0DAAoC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;sCAOlE,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;gCAAC,WAAU;0CAClB,gBAAgB,GAAG,CAAC,CAAC,MAAM;oCAC1B,MAAM,WAAW,aAAa,KAAK,IAAI;oCACvC,MAAM,OAAO,KAAK,IAAI;oCAEtB,qBACE,6LAAC,6LAAA,CAAA,SAAM,CAAC,EAAE;wCAER,UAAU;wCACV,YAAY;4CAAE,OAAO,QAAQ;wCAAI;kDAEjC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,KAAK,IAAI;4CACf,WAAW,AAAC,4EAIX,OAHC,WACI,wDACA;;8DAGN,6LAAC;oDACC,WAAW,AAAC,kCAEX,OADC,WAAW,kBAAkB;;;;;;8DAGjC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAW,AAAC,eAA8C,OAAhC,WAAW,kBAAkB;sEACvD,KAAK,IAAI;;;;;;sEAEZ,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;;;;;;;gDAGpB,0BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,UAAS;oDACT,YAAY;wDAAE,MAAM;wDAAU,WAAW;wDAAK,SAAS;oDAAG;;;;;;;;;;;;uCA7B3D,KAAK,IAAI;;;;;gCAmCpB;;;;;;;;;;;sCAKJ,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,oPAAA,CAAA,4BAAyB;4CAAC,WAAU;;;;;;sDACrC,6LAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAGhC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;sDAGrC,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6LAAC,6LAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,WAAU;gBACV,SAAS;gBACT,YAAY;oBAAE,OAAO;gBAAK;gBAC1B,UAAU;oBAAE,OAAO;gBAAK;0BAExB,cAAA,6LAAC,oNAAA,CAAA,YAAS;oBAAC,WAAU;;;;;;;;;;;;;AAI7B;GAvNM;;QACa,qIAAA,CAAA,cAAW;QACW,wHAAA,CAAA,aAAU;QACxB,wHAAA,CAAA,eAAY;;;KAHjC;uCAyNS", "debugId": null}}, {"offset": {"line": 946, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/dashboard/layout.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\nimport Sidebar from '@/components/layout/Sidebar';\nimport { useUIStore } from '@/store';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nconst DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {\n  const { sidebarOpen } = useUIStore();\n\n  return (\n    <div className=\"flex h-screen bg-gray-50\">\n      {/* Sidebar */}\n      <Sidebar />\n      \n      {/* Main Content */}\n      <motion.main\n        className={`flex-1 flex flex-col overflow-hidden transition-all duration-300 ${\n          sidebarOpen ? 'lg:ml-80' : 'lg:ml-0'\n        }`}\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ duration: 0.3 }}\n      >\n        {/* Content Area */}\n        <div className=\"flex-1 overflow-auto\">\n          <div className=\"p-6 lg:p-8\">\n            {children}\n          </div>\n        </div>\n      </motion.main>\n    </div>\n  );\n};\n\nexport default DashboardLayout;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;;;AALA;;;;AAWA,MAAM,kBAAkD;QAAC,EAAE,QAAQ,EAAE;;IACnE,MAAM,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,aAAU,AAAD;IAEjC,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,0IAAA,CAAA,UAAO;;;;;0BAGR,6LAAC,6LAAA,CAAA,SAAM,CAAC,IAAI;gBACV,WAAW,AAAC,oEAEX,OADC,cAAc,aAAa;gBAE7B,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,UAAU;gBAAI;0BAG5B,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;;;;;AAMb;GA1BM;;QACoB,wHAAA,CAAA,aAAU;;;KAD9B;uCA4BS", "debugId": null}}]}