'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  UserIcon, 
  BellIcon, 
  ShieldCheckIcon,
  CogIcon,
  DatabaseIcon,
  CloudIcon
} from '@heroicons/react/24/outline';
import { InfoCard } from '@/components/ui/Card';
import { useAuthStore } from '@/store';
import { toast } from 'react-hot-toast';

const SettingsPage: React.FC = () => {
  const { user } = useAuthStore();
  const [notifications, setNotifications] = useState({
    newPatients: true,
    appointments: true,
    criticalAlerts: true,
    systemUpdates: false
  });

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  const handleNotificationChange = (key: string, value: boolean) => {
    setNotifications(prev => ({ ...prev, [key]: value }));
    toast.success('Notification settings updated');
  };

  return (
    <motion.div
      className="max-w-4xl mx-auto space-y-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Settings
        </h1>
        <p className="text-gray-600">
          Manage your application preferences and account settings.
        </p>
      </motion.div>

      {/* Profile Settings */}
      <motion.div variants={itemVariants}>
        <InfoCard title="Profile Information" icon={<UserIcon className="w-5 h-5" />}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="form-label">First Name</label>
              <input
                type="text"
                className="form-input"
                value={user?.firstName || ''}
                readOnly
              />
            </div>
            <div>
              <label className="form-label">Last Name</label>
              <input
                type="text"
                className="form-input"
                value={user?.lastName || ''}
                readOnly
              />
            </div>
            <div>
              <label className="form-label">Email</label>
              <input
                type="email"
                className="form-input"
                value={user?.email || ''}
                readOnly
              />
            </div>
            <div>
              <label className="form-label">Role</label>
              <input
                type="text"
                className="form-input capitalize"
                value={user?.role || ''}
                readOnly
              />
            </div>
          </div>
          <div className="mt-6">
            <button className="btn-secondary">
              Edit Profile
            </button>
          </div>
        </InfoCard>
      </motion.div>

      {/* Notification Settings */}
      <motion.div variants={itemVariants}>
        <InfoCard title="Notification Preferences" icon={<BellIcon className="w-5 h-5" />}>
          <div className="space-y-4">
            {Object.entries(notifications).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-gray-900 capitalize">
                    {key.replace(/([A-Z])/g, ' $1').trim()}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {key === 'newPatients' && 'Get notified when new patients are registered'}
                    {key === 'appointments' && 'Receive alerts for upcoming appointments'}
                    {key === 'criticalAlerts' && 'Immediate notifications for critical patient cases'}
                    {key === 'systemUpdates' && 'Updates about system maintenance and new features'}
                  </p>
                </div>
                <label className="relative inline-flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    className="sr-only peer"
                    checked={value}
                    onChange={(e) => handleNotificationChange(key, e.target.checked)}
                  />
                  <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                </label>
              </div>
            ))}
          </div>
        </InfoCard>
      </motion.div>

      {/* Security Settings */}
      <motion.div variants={itemVariants}>
        <InfoCard title="Security & Privacy" icon={<ShieldCheckIcon className="w-5 h-5" />}>
          <div className="space-y-4">
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Change Password</h4>
                <p className="text-sm text-gray-600">Update your account password</p>
              </div>
              <button className="btn-secondary">
                Change Password
              </button>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Two-Factor Authentication</h4>
                <p className="text-sm text-gray-600">Add an extra layer of security to your account</p>
              </div>
              <button className="btn-secondary">
                Enable 2FA
              </button>
            </div>
            
            <div className="flex items-center justify-between p-4 border border-gray-200 rounded-lg">
              <div>
                <h4 className="font-medium text-gray-900">Session Management</h4>
                <p className="text-sm text-gray-600">View and manage your active sessions</p>
              </div>
              <button className="btn-secondary">
                Manage Sessions
              </button>
            </div>
          </div>
        </InfoCard>
      </motion.div>

      {/* System Settings */}
      <motion.div variants={itemVariants}>
        <InfoCard title="System Configuration" icon={<CogIcon className="w-5 h-5" />}>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Application Settings</h4>
              
              <div>
                <label className="form-label">Default Language</label>
                <select className="form-input">
                  <option value="en">English</option>
                  <option value="hi">Hindi</option>
                  <option value="mr">Marathi</option>
                </select>
              </div>
              
              <div>
                <label className="form-label">Date Format</label>
                <select className="form-input">
                  <option value="dd/mm/yyyy">DD/MM/YYYY</option>
                  <option value="mm/dd/yyyy">MM/DD/YYYY</option>
                  <option value="yyyy-mm-dd">YYYY-MM-DD</option>
                </select>
              </div>
              
              <div>
                <label className="form-label">Time Zone</label>
                <select className="form-input">
                  <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                  <option value="UTC">UTC</option>
                </select>
              </div>
            </div>
            
            <div className="space-y-4">
              <h4 className="font-medium text-gray-900">Data Management</h4>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <DatabaseIcon className="w-5 h-5 text-blue-600" />
                  <h5 className="font-medium text-gray-900">Database Backup</h5>
                </div>
                <p className="text-sm text-gray-600 mb-3">Last backup: 2 hours ago</p>
                <button className="btn-secondary text-sm">
                  Create Backup
                </button>
              </div>
              
              <div className="p-4 border border-gray-200 rounded-lg">
                <div className="flex items-center space-x-3 mb-2">
                  <CloudIcon className="w-5 h-5 text-green-600" />
                  <h5 className="font-medium text-gray-900">Cloud Sync</h5>
                </div>
                <p className="text-sm text-gray-600 mb-3">Status: Connected</p>
                <button className="btn-secondary text-sm">
                  Sync Now
                </button>
              </div>
            </div>
          </div>
        </InfoCard>
      </motion.div>

      {/* Application Info */}
      <motion.div variants={itemVariants}>
        <div className="medical-card p-6 bg-gradient-to-r from-gray-50 to-blue-50 border border-gray-200">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Application Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div>
              <p className="text-gray-600">Version</p>
              <p className="font-medium text-gray-900">1.0.0</p>
            </div>
            <div>
              <p className="text-gray-600">Last Updated</p>
              <p className="font-medium text-gray-900">January 2024</p>
            </div>
            <div>
              <p className="text-gray-600">Support</p>
              <p className="font-medium text-blue-600"><EMAIL></p>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default SettingsPage;
