{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/lib/database.ts"], "sourcesContent": ["// Database connection using direct PostgreSQL connection\nimport { Pool } from 'pg';\n\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: {\n    rejectUnauthorized: false\n  }\n});\n\nexport { pool };\n\n// Database helper functions\nexport const db = {\n  query: async (text: string, params?: any[]) => {\n    const client = await pool.connect();\n    try {\n      const result = await client.query(text, params);\n      return result;\n    } finally {\n      client.release();\n    }\n  },\n  \n  getClient: async () => {\n    return await pool.connect();\n  }\n};\n\n// Test database connection\nexport const testConnection = async () => {\n  try {\n    const result = await db.query('SELECT NOW()');\n    console.log('Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AACzD;;;;;;AAEA,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK;QACH,oBAAoB;IACtB;AACF;;AAKO,MAAM,KAAK;IAChB,OAAO,OAAO,MAAc;QAC1B,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,MAAM;YACxC,OAAO;QACT,SAAU;YACR,OAAO,OAAO;QAChB;IACF;IAEA,WAAW;QACT,OAAO,MAAM,KAAK,OAAO;IAC3B;AACF;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,SAAS,MAAM,GAAG,KAAK,CAAC;QAC9B,QAAQ,GAAG,CAAC,oCAAoC,OAAO,IAAI,CAAC,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { pool } from '@/lib/database';\nimport bcrypt from 'bcryptjs';\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, password } = await request.json();\n\n    if (!email || !password) {\n      return NextResponse.json(\n        { error: 'Email and password are required' },\n        { status: 400 }\n      );\n    }\n\n    const client = await pool.connect();\n    \n    try {\n      // Check if user exists\n      const userResult = await client.query(\n        'SELECT id, email, first_name, last_name, role, profile_image_url, is_active FROM users WHERE email = $1',\n        [email]\n      );\n\n      if (userResult.rows.length === 0) {\n        return NextResponse.json(\n          { error: 'Invalid credentials' },\n          { status: 401 }\n        );\n      }\n\n      const user = userResult.rows[0];\n\n      if (!user.is_active) {\n        return NextResponse.json(\n          { error: 'Account is deactivated' },\n          { status: 401 }\n        );\n      }\n\n      // For demo purposes, accept the demo password\n      if (email === '<EMAIL>' && password === 'demo123') {\n        const userData = {\n          id: user.id,\n          email: user.email,\n          firstName: user.first_name,\n          lastName: user.last_name,\n          role: user.role,\n          profileImageUrl: user.profile_image_url,\n          isActive: user.is_active,\n          createdAt: new Date().toISOString(),\n          updatedAt: new Date().toISOString(),\n        };\n\n        return NextResponse.json({\n          success: true,\n          user: userData,\n          message: 'Login successful'\n        });\n      }\n\n      return NextResponse.json(\n        { error: 'Invalid credentials' },\n        { status: 401 }\n      );\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Login error:', error);\n    return NextResponse.json(\n      { error: 'Internal server error' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;;;;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAkC,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,uBAAuB;YACvB,MAAM,aAAa,MAAM,OAAO,KAAK,CACnC,2GACA;gBAAC;aAAM;YAGT,IAAI,WAAW,IAAI,CAAC,MAAM,KAAK,GAAG;gBAChC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAsB,GAC/B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,OAAO,WAAW,IAAI,CAAC,EAAE;YAE/B,IAAI,CAAC,KAAK,SAAS,EAAE;gBACnB,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAyB,GAClC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,8CAA8C;YAC9C,IAAI,UAAU,wBAAwB,aAAa,WAAW;gBAC5D,MAAM,WAAW;oBACf,IAAI,KAAK,EAAE;oBACX,OAAO,KAAK,KAAK;oBACjB,WAAW,KAAK,UAAU;oBAC1B,UAAU,KAAK,SAAS;oBACxB,MAAM,KAAK,IAAI;oBACf,iBAAiB,KAAK,iBAAiB;oBACvC,UAAU,KAAK,SAAS;oBACxB,WAAW,IAAI,OAAO,WAAW;oBACjC,WAAW,IAAI,OAAO,WAAW;gBACnC;gBAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACvB,SAAS;oBACT,MAAM;oBACN,SAAS;gBACX;YACF;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsB,GAC/B;gBAAE,QAAQ;YAAI;QAGlB,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gBAAgB;QAC9B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}