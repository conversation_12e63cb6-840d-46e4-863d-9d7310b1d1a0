{"name": "postgres-bytea", "main": "index.js", "version": "1.0.0", "description": "Postgres bytea parser", "license": "MIT", "repository": "bendrucker/postgres-bytea", "author": {"name": "<PERSON>", "email": "<EMAIL>", "url": "bendrucker.me"}, "engines": {"node": ">=0.10.0"}, "scripts": {"test": "standard && tape test.js"}, "keywords": ["bytea", "postgres", "binary", "parser"], "dependencies": {}, "devDependencies": {"tape": "^4.0.0", "standard": "^4.0.0"}, "files": ["index.js", "readme.md"]}