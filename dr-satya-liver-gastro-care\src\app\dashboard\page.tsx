'use client';

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  UserGroupIcon,
  UserPlusIcon,
  ChartBarIcon,
  HeartIcon
} from '@heroicons/react/24/outline';
import { StatCard } from '@/components/ui/Card';
import { useAuthStore } from '@/store';
import { toast } from 'react-hot-toast';

const DashboardPage: React.FC = () => {
  const { user } = useAuthStore();
  const [stats, setStats] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/dashboard/stats');
        const data = await response.json();

        if (data.success) {
          setStats(data.data);
        } else {
          toast.error('Failed to fetch dashboard statistics');
        }
      } catch (error) {
        console.error('Error fetching stats:', error);
        toast.error('Failed to fetch dashboard statistics');
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  const statCards = stats ? [
    {
      title: 'Total Patients',
      value: stats.totalPatients.toLocaleString(),
      subtitle: 'Active patients',
      icon: <UserGroupIcon className="w-6 h-6" />,
      trend: { value: 12, isPositive: true },
      color: 'blue' as const,
    },
    {
      title: 'New Patients',
      value: stats.newPatientsThisMonth.toString(),
      subtitle: 'This month',
      icon: <UserPlusIcon className="w-6 h-6" />,
      trend: { value: 8, isPositive: true },
      color: 'green' as const,
    },
    {
      title: 'Appointments',
      value: stats.appointments.toString(),
      subtitle: 'This week',
      icon: <ChartBarIcon className="w-6 h-6" />,
      trend: { value: 3, isPositive: false },
      color: 'amber' as const,
    },
    {
      title: 'Critical Cases',
      value: stats.criticalCases.toString(),
      subtitle: 'Require attention',
      icon: <HeartIcon className="w-6 h-6" />,
      trend: { value: 2, isPositive: false },
      color: 'red' as const,
    },
  ] : [];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Premium Animated Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-teal-50">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_20%,rgba(59,130,246,0.1),transparent_50%)]"></div>
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(16,185,129,0.1),transparent_50%)]"></div>
      </div>

      {/* Floating Medical Elements */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-2 h-2 bg-blue-400/20 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, 20, -20],
              x: [-10, 10, -10],
              scale: [1, 1.5, 1],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + Math.random() * 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: Math.random() * 2,
            }}
          />
        ))}
      </div>

      <motion.div
        className="relative z-10 space-y-8 p-6"
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        {/* Premium Header */}
        <motion.div
          variants={itemVariants}
          className="relative"
        >
          <div className="flex items-center justify-between">
            <div>
              <motion.h1
                className="text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-teal-600 bg-clip-text text-transparent mb-3"
                initial={{ opacity: 0, y: 30 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.8, delay: 0.2 }}
              >
                Welcome back, {user ? `${user.firstName} ${user.lastName}` : 'Doctor'}
              </motion.h1>
              <motion.p
                className="text-xl text-gray-600 font-medium"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                Here's what's happening with your patients today.
              </motion.p>
            </div>

            {/* Premium Time Display */}
            <motion.div
              className="text-right"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <div className="text-3xl font-bold text-gray-800">
                {new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </div>
              <div className="text-sm text-gray-500 font-medium">
                {new Date().toLocaleDateString('en-US', {
                  weekday: 'long',
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })}
              </div>
            </motion.div>
          </div>

          {/* Decorative Line */}
          <motion.div
            className="mt-6 h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-teal-500 rounded-full"
            initial={{ width: 0 }}
            animate={{ width: '100%' }}
            transition={{ duration: 1.2, delay: 0.8 }}
          />
        </motion.div>

        {/* Premium Stats Grid */}
        <motion.div
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8"
          variants={containerVariants}
        >
          {loading ? (
            // Premium Loading Skeletons
            Array.from({ length: 4 }).map((_, index) => (
              <motion.div
                key={index}
                variants={itemVariants}
                className="relative"
              >
                <div className="medical-card-glass p-8 relative overflow-hidden">
                  {/* Shimmer Effect */}
                  <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent animate-pulse"></div>

                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-3 animate-pulse"></div>
                      <div className="h-10 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-3 animate-pulse"></div>
                      <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full animate-pulse"></div>
                    </div>
                    <div className="w-16 h-16 bg-gradient-to-r from-gray-200 to-gray-300 rounded-2xl animate-pulse"></div>
                  </div>
                </div>
              </motion.div>
            ))
          ) : (
            statCards.map((stat, index) => (
              <motion.div
                key={stat.title}
                variants={itemVariants}
                whileHover={{
                  y: -12,
                  scale: 1.03,
                  transition: { duration: 0.3 }
                }}
              >
                <StatCard {...stat} />
              </motion.div>
            ))
          )}
        </motion.div>

        {/* Premium Quick Actions */}
        <motion.div variants={itemVariants} className="relative">
          <motion.h2
            className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Quick Actions
          </motion.h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <motion.div
              className="medical-card-glass p-8 cursor-pointer group relative overflow-hidden border border-blue-200"
              whileHover={{
                y: -8,
                scale: 1.03,
                boxShadow: '0 25px 50px rgba(59, 130, 246, 0.25)',
                transition: { duration: 0.3 }
              }}
              whileTap={{ scale: 0.97 }}
              onClick={() => window.location.href = '/patients/new'}
            >
              {/* Hover Glow Effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-blue-500/10 to-purple-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              />

              <div className="relative z-10 flex items-center space-x-6">
                <motion.div
                  className="p-4 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <UserPlusIcon className="w-8 h-8 text-white" />
                </motion.div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">Add New Patient</h3>
                  <p className="text-gray-600 font-medium">Register a new patient in the system</p>
                </div>
              </div>

              {/* Bottom Border Animation */}
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-blue-500 to-purple-500"
                initial={{ width: 0 }}
                whileHover={{ width: '100%' }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>

            <motion.div
              className="medical-card-glass p-8 cursor-pointer group relative overflow-hidden border border-emerald-200"
              whileHover={{
                y: -8,
                scale: 1.03,
                boxShadow: '0 25px 50px rgba(16, 185, 129, 0.25)',
                transition: { duration: 0.3 }
              }}
              whileTap={{ scale: 0.97 }}
              onClick={() => window.location.href = '/patients'}
            >
              {/* Hover Glow Effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-emerald-500/10 to-teal-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              />

              <div className="relative z-10 flex items-center space-x-6">
                <motion.div
                  className="p-4 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <UserGroupIcon className="w-8 h-8 text-white" />
                </motion.div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">View All Patients</h3>
                  <p className="text-gray-600 font-medium">Browse and search patient records</p>
                </div>
              </div>

              {/* Bottom Border Animation */}
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-emerald-500 to-teal-500"
                initial={{ width: 0 }}
                whileHover={{ width: '100%' }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>

            <motion.div
              className="medical-card-glass p-8 cursor-pointer group relative overflow-hidden border border-purple-200"
              whileHover={{
                y: -8,
                scale: 1.03,
                boxShadow: '0 25px 50px rgba(147, 51, 234, 0.25)',
                transition: { duration: 0.3 }
              }}
              whileTap={{ scale: 0.97 }}
              onClick={() => window.location.href = '/analytics'}
            >
              {/* Hover Glow Effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-purple-500/10 to-pink-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              />

              <div className="relative z-10 flex items-center space-x-6">
                <motion.div
                  className="p-4 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  whileHover={{ scale: 1.1, rotate: 5 }}
                >
                  <ChartBarIcon className="w-8 h-8 text-white" />
                </motion.div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-2">View Analytics</h3>
                  <p className="text-gray-600 font-medium">Check medical reports and insights</p>
                </div>
              </div>

              {/* Bottom Border Animation */}
              <motion.div
                className="absolute bottom-0 left-0 h-1 bg-gradient-to-r from-purple-500 to-pink-500"
                initial={{ width: 0 }}
                whileHover={{ width: '100%' }}
                transition={{ duration: 0.3 }}
              />
            </motion.div>
          </div>
        </motion.div>

        {/* Premium Recent Activity */}
        <motion.div variants={itemVariants} className="relative">
          <motion.h2
            className="text-3xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent mb-8"
            initial={{ opacity: 0, x: -30 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            Recent Activity
          </motion.h2>

          <div className="medical-card-glass p-8 relative overflow-hidden">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-5">
              <svg className="w-full h-full" viewBox="0 0 100 100">
                <pattern id="activity-pattern" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse">
                  <circle cx="10" cy="10" r="1" fill="currentColor" />
                </pattern>
                <rect width="100" height="100" fill="url(#activity-pattern)" />
              </svg>
            </div>

            <div className="relative z-10 space-y-6">
              {loading ? (
                // Premium Loading Skeletons
                Array.from({ length: 5 }).map((_, index) => (
                  <motion.div
                    key={index}
                    className="flex items-center space-x-4 p-4 bg-white/50 rounded-2xl backdrop-blur-sm"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <div className="w-12 h-12 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full animate-pulse" />
                    <div className="flex-1">
                      <div className="h-4 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full mb-2 animate-pulse" />
                      <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full w-2/3 animate-pulse" />
                    </div>
                    <div className="h-3 bg-gradient-to-r from-gray-200 to-gray-300 rounded-full w-20 animate-pulse" />
                  </motion.div>
                ))
              ) : (
                stats?.recentActivity?.map((activity: any, index: number) => (
                  <motion.div
                    key={index}
                    className="flex items-center justify-between p-6 bg-white/60 rounded-2xl backdrop-blur-sm border border-white/20 hover:bg-white/80 transition-all duration-300 group"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileHover={{ scale: 1.02, x: 8 }}
                  >
                    <div className="flex items-center space-x-4">
                      <motion.div
                        className="w-12 h-12 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                        whileHover={{ scale: 1.1, rotate: 5 }}
                      >
                        <UserPlusIcon className="w-6 h-6 text-white" />
                      </motion.div>
                      <div>
                        <p className="font-bold text-gray-900 text-lg">{activity.action}</p>
                        <p className="text-gray-600 font-medium">Patient: {activity.patient}</p>
                      </div>
                    </div>

                    <div className="text-right">
                      <span className="text-sm text-gray-500 font-medium">{activity.time}</span>
                      <div className="flex items-center justify-end gap-1 mt-1">
                        <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        <span className="text-xs text-gray-400">Active</span>
                      </div>
                    </div>
                  </motion.div>
                )) || (
                  <motion.div
                    className="text-center py-12"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                  >
                    <div className="w-16 h-16 bg-gradient-to-br from-gray-200 to-gray-300 rounded-full mx-auto mb-4 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                    <p className="text-gray-500 font-medium">No recent activity</p>
                  </motion.div>
                )
              )}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
};

export default DashboardPage;
