import { NextRequest, NextResponse } from 'next/server';
import { pool } from '@/lib/database';

export async function GET(request: NextRequest) {
  try {
    const client = await pool.connect();
    
    try {
      // Get total patients
      const totalPatientsResult = await client.query('SELECT COUNT(*) FROM patients');
      const totalPatients = parseInt(totalPatientsResult.rows[0].count);

      // Get new patients this month
      const newPatientsResult = await client.query(`
        SELECT COUNT(*) FROM patients 
        WHERE created_at >= date_trunc('month', CURRENT_DATE)
      `);
      const newPatientsThisMonth = parseInt(newPatientsResult.rows[0].count);

      // Get gender distribution
      const genderResult = await client.query(`
        SELECT gender, COUNT(*) as count 
        FROM patients 
        GROUP BY gender
      `);
      
      const genderDistribution = {
        male: 0,
        female: 0,
        other: 0
      };
      
      genderResult.rows.forEach(row => {
        const gender = row.gender.toLowerCase();
        if (gender === 'male') genderDistribution.male = parseInt(row.count);
        else if (gender === 'female') genderDistribution.female = parseInt(row.count);
        else genderDistribution.other = parseInt(row.count);
      });

      // Get blood group distribution
      const bloodGroupResult = await client.query(`
        SELECT blood_group, COUNT(*) as count 
        FROM patients 
        WHERE blood_group IS NOT NULL
        GROUP BY blood_group
        ORDER BY count DESC
      `);
      
      const bloodGroupDistribution: { [key: string]: number } = {};
      bloodGroupResult.rows.forEach(row => {
        bloodGroupDistribution[row.blood_group] = parseInt(row.count);
      });

      // Get average age
      const avgAgeResult = await client.query(`
        SELECT AVG(EXTRACT(YEAR FROM AGE(date_of_birth))) as avg_age 
        FROM patients 
        WHERE date_of_birth IS NOT NULL
      `);
      const averageAge = Math.round(parseFloat(avgAgeResult.rows[0].avg_age || '0'));

      // Get BMI distribution
      const bmiResult = await client.query(`
        SELECT 
          COUNT(CASE WHEN bmi < 18.5 THEN 1 END) as underweight,
          COUNT(CASE WHEN bmi >= 18.5 AND bmi < 25 THEN 1 END) as normal,
          COUNT(CASE WHEN bmi >= 25 AND bmi < 30 THEN 1 END) as overweight,
          COUNT(CASE WHEN bmi >= 30 THEN 1 END) as obese
        FROM patients 
        WHERE bmi IS NOT NULL
      `);
      
      const bmiDistribution = {
        underweight: parseInt(bmiResult.rows[0].underweight || '0'),
        normal: parseInt(bmiResult.rows[0].normal || '0'),
        overweight: parseInt(bmiResult.rows[0].overweight || '0'),
        obese: parseInt(bmiResult.rows[0].obese || '0')
      };

      // Get recent patients for activity
      const recentPatientsResult = await client.query(`
        SELECT first_name, last_name, created_at
        FROM patients 
        ORDER BY created_at DESC 
        LIMIT 5
      `);

      const recentActivity = recentPatientsResult.rows.map(row => ({
        action: 'New patient registered',
        patient: `${row.first_name} ${row.last_name}`,
        time: new Date(row.created_at).toLocaleString()
      }));

      const stats = {
        totalPatients,
        newPatientsThisMonth,
        averageAge,
        genderDistribution,
        bloodGroupDistribution,
        bmiDistribution,
        recentActivity,
        // Mock data for other stats
        appointments: 156,
        criticalCases: 4
      };

      return NextResponse.json({
        success: true,
        data: stats
      });

    } finally {
      client.release();
    }

  } catch (error) {
    console.error('Error fetching dashboard stats:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard statistics' },
      { status: 500 }
    );
  }
}
