'use client';

import React from 'react';
import { motion } from 'framer-motion';

interface LogoProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  showText?: boolean;
  className?: string;
  variant?: 'default' | 'minimal' | 'premium';
}

const Logo: React.FC<LogoProps> = ({
  size = 'md',
  showText = true,
  className = '',
  variant = 'premium'
}) => {
  const sizeClasses = {
    sm: 'w-10 h-10',
    md: 'w-14 h-14',
    lg: 'w-20 h-20',
    xl: 'w-28 h-28'
  };

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-xl',
    lg: 'text-2xl',
    xl: 'text-4xl'
  };

  const logoVariants = {
    hidden: {
      opacity: 0,
      scale: 0.5,
      rotate: -180
    },
    visible: {
      opacity: 1,
      scale: 1,
      rotate: 0,
      transition: {
        duration: 1.2,
        ease: [0.6, -0.05, 0.01, 0.99],
        staggerChildren: 0.1
      }
    }
  };

  const iconVariants = {
    hidden: { scale: 0, rotate: -90 },
    visible: {
      scale: 1,
      rotate: 0,
      transition: {
        type: "spring",
        stiffness: 200,
        damping: 15
      }
    }
  };

  const textVariants = {
    hidden: { opacity: 0, x: -30 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: "easeOut"
      }
    }
  };

  const pulseVariants = {
    pulse: {
      scale: [1, 1.05, 1],
      transition: {
        duration: 2,
        repeat: Infinity,
        ease: "easeInOut"
      }
    }
  };

  return (
    <motion.div
      className={`flex items-center gap-4 ${className}`}
      variants={logoVariants}
      initial="hidden"
      animate="visible"
      whileHover="pulse"
    >
      {/* Premium Medical Logo Icon */}
      <motion.div
        className={`${sizeClasses[size]} relative`}
        variants={iconVariants}
        whileHover={{
          scale: 1.1,
          rotate: 5,
          transition: { duration: 0.3 }
        }}
      >
        <svg
          viewBox="0 0 120 120"
          className="w-full h-full drop-shadow-lg"
          xmlns="http://www.w3.org/2000/svg"
        >
          <defs>
            {/* Premium Gradients */}
            <linearGradient id="premiumGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#0066cc" />
              <stop offset="30%" stopColor="#1a7ae8" />
              <stop offset="70%" stopColor="#00a693" />
              <stop offset="100%" stopColor="#1ab8a6" />
            </linearGradient>

            <linearGradient id="crossGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#ffffff" />
              <stop offset="100%" stopColor="#f0f9ff" />
            </linearGradient>

            <linearGradient id="accentGradient" x1="0%" y1="0%" x2="100%" y2="100%">
              <stop offset="0%" stopColor="#10b981" />
              <stop offset="100%" stopColor="#34d399" />
            </linearGradient>

            {/* Glow Filter */}
            <filter id="glow">
              <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
              <feMerge>
                <feMergeNode in="coloredBlur"/>
                <feMergeNode in="SourceGraphic"/>
              </feMerge>
            </filter>

            {/* Shadow Filter */}
            <filter id="dropshadow" x="-50%" y="-50%" width="200%" height="200%">
              <feDropShadow dx="0" dy="4" stdDeviation="4" floodColor="#0066cc" floodOpacity="0.3"/>
            </filter>
          </defs>

          {/* Outer Ring with Animation */}
          <motion.circle
            cx="60"
            cy="60"
            r="55"
            fill="none"
            stroke="url(#premiumGradient)"
            strokeWidth="2"
            strokeDasharray="10 5"
            initial={{ rotate: 0 }}
            animate={{ rotate: 360 }}
            transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
            style={{ transformOrigin: "60px 60px" }}
          />

          {/* Main Circle Background */}
          <circle
            cx="60"
            cy="60"
            r="48"
            fill="url(#premiumGradient)"
            filter="url(#dropshadow)"
          />

          {/* Inner Glow Circle */}
          <circle
            cx="60"
            cy="60"
            r="42"
            fill="none"
            stroke="rgba(255, 255, 255, 0.3)"
            strokeWidth="1"
          />

          {/* Medical Cross with 3D Effect */}
          <g transform="translate(60, 60)" filter="url(#glow)">
            {/* Cross Shadow */}
            <g transform="translate(2, 2)" opacity="0.3">
              <rect x="-4" y="-24" width="8" height="48" fill="#0066cc" rx="4" />
              <rect x="-24" y="-4" width="48" height="8" fill="#0066cc" rx="4" />
            </g>

            {/* Main Cross */}
            <rect x="-4" y="-24" width="8" height="48" fill="url(#crossGradient)" rx="4" />
            <rect x="-24" y="-4" width="48" height="8" fill="url(#crossGradient)" rx="4" />

            {/* Cross Highlights */}
            <rect x="-2" y="-24" width="4" height="48" fill="rgba(255, 255, 255, 0.4)" rx="2" />
            <rect x="-24" y="-2" width="48" height="4" fill="rgba(255, 255, 255, 0.4)" rx="2" />
          </g>

          {/* Decorative Elements */}
          <g transform="translate(60, 60)">
            {/* DNA Helix Accent */}
            <motion.path
              d="M -35 -20 Q -30 -15 -25 -20 Q -20 -25 -15 -20 Q -10 -15 -5 -20"
              stroke="url(#accentGradient)"
              strokeWidth="2"
              fill="none"
              opacity="0.6"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 2, delay: 0.5 }}
            />

            {/* Heartbeat Line */}
            <motion.path
              d="M -35 25 L -25 25 L -20 15 L -15 35 L -10 5 L -5 25 L 5 25"
              stroke="url(#accentGradient)"
              strokeWidth="2"
              fill="none"
              opacity="0.7"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: 1 }}
              transition={{ duration: 1.5, delay: 1 }}
            />

            {/* Floating Particles */}
            {[
              { cx: 35, cy: 0 },
              { cx: 17.5, cy: 30.31 },
              { cx: -17.5, cy: 30.31 },
              { cx: -35, cy: 0 },
              { cx: -17.5, cy: -30.31 },
              { cx: 17.5, cy: -30.31 }
            ].map((position, i) => (
              <motion.circle
                key={i}
                cx={position.cx}
                cy={position.cy}
                r="2"
                fill="rgba(255, 255, 255, 0.8)"
                initial={{ scale: 0, opacity: 0 }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 1, 0]
                }}
                transition={{
                  duration: 3,
                  delay: i * 0.2,
                  repeat: Infinity,
                  repeatDelay: 2
                }}
              />
            ))}
          </g>
        </svg>
      </motion.div>

      {/* Premium Text Logo */}
      {showText && (
        <motion.div
          className="flex flex-col"
          variants={textVariants}
        >
          <motion.h1
            className={`font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-teal-600 bg-clip-text text-transparent leading-tight ${textSizeClasses[size]}`}
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 }
            }}
          >
            Dr Satya's
          </motion.h1>
          <motion.p
            className={`font-semibold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent leading-tight ${
              size === 'sm' ? 'text-xs' :
              size === 'md' ? 'text-sm' :
              size === 'lg' ? 'text-lg' : 'text-xl'
            }`}
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 }
            }}
          >
            Liver & Gastro Care
          </motion.p>
          {(size === 'lg' || size === 'xl') && (
            <motion.p
              className="text-xs text-gray-500 mt-1 font-medium tracking-wide"
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8, duration: 0.5 }}
            >
              Excellence in Digestive Health
            </motion.p>
          )}
        </motion.div>
      )}
    </motion.div>
  );
};

export default Logo;
