{"version": 3, "sources": [], "sections": [{"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/store/index.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist } from 'zustand/middleware';\nimport { User, Patient, Comorbidity, DashboardStats } from '@/types';\n\n// Authentication Store\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  login: (credentials: { email: string; password: string }) => Promise<boolean>;\n  logout: () => void;\n  setUser: (user: User) => void;\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n      \n      login: async (credentials) => {\n        set({ isLoading: true });\n        try {\n          const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: { 'Content-Type': 'application/json' },\n            body: JSON.stringify(credentials),\n          });\n\n          const data = await response.json();\n\n          if (response.ok && data.success) {\n            set({ user: data.user, isAuthenticated: true, isLoading: false });\n            return true;\n          }\n\n          set({ isLoading: false });\n          return false;\n        } catch (error) {\n          console.error('Login error:', error);\n          set({ isLoading: false });\n          return false;\n        }\n      },\n      \n      logout: () => {\n        set({ user: null, isAuthenticated: false });\n      },\n      \n      setUser: (user) => {\n        set({ user, isAuthenticated: true });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({ \n        user: state.user, \n        isAuthenticated: state.isAuthenticated \n      }),\n    }\n  )\n);\n\n// Patients Store\ninterface PatientsState {\n  patients: Patient[];\n  currentPatient: Patient | null;\n  isLoading: boolean;\n  searchQuery: string;\n  filters: {\n    gender?: string;\n    bloodGroup?: string;\n    ageRange?: { min: number; max: number };\n  };\n  \n  // Actions\n  setPatients: (patients: Patient[]) => void;\n  addPatient: (patient: Patient) => void;\n  updatePatient: (id: string, updates: Partial<Patient>) => void;\n  deletePatient: (id: string) => void;\n  setCurrentPatient: (patient: Patient | null) => void;\n  setSearchQuery: (query: string) => void;\n  setFilters: (filters: any) => void;\n  setLoading: (loading: boolean) => void;\n  \n  // Computed\n  filteredPatients: () => Patient[];\n}\n\nexport const usePatientsStore = create<PatientsState>((set, get) => ({\n  patients: [],\n  currentPatient: null,\n  isLoading: false,\n  searchQuery: '',\n  filters: {},\n  \n  setPatients: (patients) => set({ patients }),\n  \n  addPatient: (patient) => \n    set((state) => ({ patients: [...state.patients, patient] })),\n  \n  updatePatient: (id, updates) =>\n    set((state) => ({\n      patients: state.patients.map((p) => \n        p.id === id ? { ...p, ...updates } : p\n      ),\n      currentPatient: state.currentPatient?.id === id \n        ? { ...state.currentPatient, ...updates } \n        : state.currentPatient,\n    })),\n  \n  deletePatient: (id) =>\n    set((state) => ({\n      patients: state.patients.filter((p) => p.id !== id),\n      currentPatient: state.currentPatient?.id === id ? null : state.currentPatient,\n    })),\n  \n  setCurrentPatient: (patient) => set({ currentPatient: patient }),\n  \n  setSearchQuery: (searchQuery) => set({ searchQuery }),\n  \n  setFilters: (filters) => set({ filters }),\n  \n  setLoading: (isLoading) => set({ isLoading }),\n  \n  filteredPatients: () => {\n    const { patients, searchQuery, filters } = get();\n    \n    return patients.filter((patient) => {\n      // Search query filter\n      if (searchQuery) {\n        const query = searchQuery.toLowerCase();\n        const matchesSearch = \n          patient.fullName.toLowerCase().includes(query) ||\n          patient.patientId.toLowerCase().includes(query) ||\n          patient.mobileNumber.includes(query) ||\n          patient.email?.toLowerCase().includes(query);\n        \n        if (!matchesSearch) return false;\n      }\n      \n      // Gender filter\n      if (filters.gender && patient.gender !== filters.gender) {\n        return false;\n      }\n      \n      // Blood group filter\n      if (filters.bloodGroup && patient.bloodGroup !== filters.bloodGroup) {\n        return false;\n      }\n      \n      // Age range filter\n      if (filters.ageRange) {\n        const age = new Date().getFullYear() - new Date(patient.dateOfBirth).getFullYear();\n        if (age < filters.ageRange.min || age > filters.ageRange.max) {\n          return false;\n        }\n      }\n      \n      return true;\n    });\n  },\n}));\n\n// Comorbidities Store\ninterface ComorbiditiesState {\n  comorbidities: Comorbidity[];\n  isLoading: boolean;\n  setComorbidities: (comorbidities: Comorbidity[]) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useComorbiditiesStore = create<ComorbiditiesState>((set) => ({\n  comorbidities: [],\n  isLoading: false,\n  \n  setComorbidities: (comorbidities) => set({ comorbidities }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// Dashboard Store\ninterface DashboardState {\n  stats: DashboardStats | null;\n  isLoading: boolean;\n  setStats: (stats: DashboardStats) => void;\n  setLoading: (loading: boolean) => void;\n}\n\nexport const useDashboardStore = create<DashboardState>((set) => ({\n  stats: null,\n  isLoading: false,\n  \n  setStats: (stats) => set({ stats }),\n  setLoading: (isLoading) => set({ isLoading }),\n}));\n\n// UI Store for global UI state\ninterface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  notifications: Array<{\n    id: string;\n    type: 'success' | 'error' | 'warning' | 'info';\n    message: string;\n    timestamp: number;\n  }>;\n  \n  toggleSidebar: () => void;\n  setTheme: (theme: 'light' | 'dark') => void;\n  addNotification: (notification: Omit<UIState['notifications'][0], 'id' | 'timestamp'>) => void;\n  removeNotification: (id: string) => void;\n}\n\nexport const useUIStore = create<UIState>()(\n  persist(\n    (set, get) => ({\n      sidebarOpen: true,\n      theme: 'light',\n      notifications: [],\n      \n      toggleSidebar: () => \n        set((state) => ({ sidebarOpen: !state.sidebarOpen })),\n      \n      setTheme: (theme) => set({ theme }),\n      \n      addNotification: (notification) => {\n        const id = Math.random().toString(36).substr(2, 9);\n        const timestamp = Date.now();\n        \n        set((state) => ({\n          notifications: [...state.notifications, { ...notification, id, timestamp }],\n        }));\n        \n        // Auto-remove after 5 seconds\n        setTimeout(() => {\n          set((state) => ({\n            notifications: state.notifications.filter((n) => n.id !== id),\n          }));\n        }, 5000);\n      },\n      \n      removeNotification: (id) =>\n        set((state) => ({\n          notifications: state.notifications.filter((n) => n.id !== id),\n        })),\n    }),\n    {\n      name: 'ui-storage',\n      partialize: (state) => ({ \n        sidebarOpen: state.sidebarOpen, \n        theme: state.theme \n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAaO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,OAAO;YACZ,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBAAE,gBAAgB;oBAAmB;oBAC9C,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,SAAS,EAAE,IAAI,KAAK,OAAO,EAAE;oBAC/B,IAAI;wBAAE,MAAM,KAAK,IAAI;wBAAE,iBAAiB;wBAAM,WAAW;oBAAM;oBAC/D,OAAO;gBACT;gBAEA,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gBAAgB;gBAC9B,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;YACT;QACF;QAEA,QAAQ;YACN,IAAI;gBAAE,MAAM;gBAAM,iBAAiB;YAAM;QAC3C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB;YAAK;QACpC;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AA8BG,MAAM,mBAAmB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAiB,CAAC,KAAK,MAAQ,CAAC;QACnE,UAAU,EAAE;QACZ,gBAAgB;QAChB,WAAW;QACX,aAAa;QACb,SAAS,CAAC;QAEV,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAE1C,YAAY,CAAC,UACX,IAAI,CAAC,QAAU,CAAC;oBAAE,UAAU;2BAAI,MAAM,QAAQ;wBAAE;qBAAQ;gBAAC,CAAC;QAE5D,eAAe,CAAC,IAAI,UAClB,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,IAC5B,EAAE,EAAE,KAAK,KAAK;4BAAE,GAAG,CAAC;4BAAE,GAAG,OAAO;wBAAC,IAAI;oBAEvC,gBAAgB,MAAM,cAAc,EAAE,OAAO,KACzC;wBAAE,GAAG,MAAM,cAAc;wBAAE,GAAG,OAAO;oBAAC,IACtC,MAAM,cAAc;gBAC1B,CAAC;QAEH,eAAe,CAAC,KACd,IAAI,CAAC,QAAU,CAAC;oBACd,UAAU,MAAM,QAAQ,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAChD,gBAAgB,MAAM,cAAc,EAAE,OAAO,KAAK,OAAO,MAAM,cAAc;gBAC/E,CAAC;QAEH,mBAAmB,CAAC,UAAY,IAAI;gBAAE,gBAAgB;YAAQ;QAE9D,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QAEnD,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QAEvC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;QAE3C,kBAAkB;YAChB,MAAM,EAAE,QAAQ,EAAE,WAAW,EAAE,OAAO,EAAE,GAAG;YAE3C,OAAO,SAAS,MAAM,CAAC,CAAC;gBACtB,sBAAsB;gBACtB,IAAI,aAAa;oBACf,MAAM,QAAQ,YAAY,WAAW;oBACrC,MAAM,gBACJ,QAAQ,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,UACxC,QAAQ,SAAS,CAAC,WAAW,GAAG,QAAQ,CAAC,UACzC,QAAQ,YAAY,CAAC,QAAQ,CAAC,UAC9B,QAAQ,KAAK,EAAE,cAAc,SAAS;oBAExC,IAAI,CAAC,eAAe,OAAO;gBAC7B;gBAEA,gBAAgB;gBAChB,IAAI,QAAQ,MAAM,IAAI,QAAQ,MAAM,KAAK,QAAQ,MAAM,EAAE;oBACvD,OAAO;gBACT;gBAEA,qBAAqB;gBACrB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,KAAK,QAAQ,UAAU,EAAE;oBACnE,OAAO;gBACT;gBAEA,mBAAmB;gBACnB,IAAI,QAAQ,QAAQ,EAAE;oBACpB,MAAM,MAAM,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,QAAQ,WAAW,EAAE,WAAW;oBAChF,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,IAAI,MAAM,QAAQ,QAAQ,CAAC,GAAG,EAAE;wBAC5D,OAAO;oBACT;gBACF;gBAEA,OAAO;YACT;QACF;IACF,CAAC;AAUM,MAAM,wBAAwB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,MAAQ,CAAC;QACxE,eAAe,EAAE;QACjB,WAAW;QAEX,kBAAkB,CAAC,gBAAkB,IAAI;gBAAE;YAAc;QACzD,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAUM,MAAM,oBAAoB,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,EAAkB,CAAC,MAAQ,CAAC;QAChE,OAAO;QACP,WAAW;QAEX,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QACjC,YAAY,CAAC,YAAc,IAAI;gBAAE;YAAU;IAC7C,CAAC;AAmBM,MAAM,aAAa,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,aAAa;QACb,OAAO;QACP,eAAe,EAAE;QAEjB,eAAe,IACb,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QAErD,UAAU,CAAC,QAAU,IAAI;gBAAE;YAAM;QAEjC,iBAAiB,CAAC;YAChB,MAAM,KAAK,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;YAChD,MAAM,YAAY,KAAK,GAAG;YAE1B,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;2BAAI,MAAM,aAAa;wBAAE;4BAAE,GAAG,YAAY;4BAAE;4BAAI;wBAAU;qBAAE;gBAC7E,CAAC;YAED,8BAA8B;YAC9B,WAAW;gBACT,IAAI,CAAC,QAAU,CAAC;wBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;oBAC5D,CAAC;YACH,GAAG;QACL;QAEA,oBAAoB,CAAC,KACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC5D,CAAC;IACL,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,OAAO,MAAM,KAAK;QACpB,CAAC;AACH", "debugId": null}}, {"offset": {"line": 229, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Logo.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LogoProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showText?: boolean;\n  className?: string;\n  variant?: 'default' | 'minimal' | 'premium';\n}\n\nconst Logo: React.FC<LogoProps> = ({\n  size = 'md',\n  showText = true,\n  className = '',\n  variant = 'premium'\n}) => {\n  const sizeClasses = {\n    sm: 'w-10 h-10',\n    md: 'w-14 h-14',\n    lg: 'w-20 h-20',\n    xl: 'w-28 h-28'\n  };\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-xl',\n    lg: 'text-2xl',\n    xl: 'text-4xl'\n  };\n\n  const logoVariants = {\n    hidden: {\n      opacity: 0,\n      scale: 0.5,\n      rotate: -180\n    },\n    visible: {\n      opacity: 1,\n      scale: 1,\n      rotate: 0,\n      transition: {\n        duration: 1.2,\n        ease: [0.6, -0.05, 0.01, 0.99],\n        staggerChildren: 0.1\n      }\n    }\n  };\n\n  const iconVariants = {\n    hidden: { scale: 0, rotate: -90 },\n    visible: {\n      scale: 1,\n      rotate: 0,\n      transition: {\n        type: \"spring\",\n        stiffness: 200,\n        damping: 15\n      }\n    }\n  };\n\n  const textVariants = {\n    hidden: { opacity: 0, x: -30 },\n    visible: {\n      opacity: 1,\n      x: 0,\n      transition: {\n        duration: 0.8,\n        ease: \"easeOut\"\n      }\n    }\n  };\n\n  const pulseVariants = {\n    pulse: {\n      scale: [1, 1.05, 1],\n      transition: {\n        duration: 2,\n        repeat: Infinity,\n        ease: \"easeInOut\"\n      }\n    }\n  };\n\n  return (\n    <motion.div\n      className={`flex items-center gap-4 ${className}`}\n      variants={logoVariants}\n      initial=\"hidden\"\n      animate=\"visible\"\n      whileHover=\"pulse\"\n    >\n      {/* Premium Medical Logo Icon */}\n      <motion.div\n        className={`${sizeClasses[size]} relative`}\n        variants={iconVariants}\n        whileHover={{\n          scale: 1.1,\n          rotate: 5,\n          transition: { duration: 0.3 }\n        }}\n      >\n        <svg\n          viewBox=\"0 0 120 120\"\n          className=\"w-full h-full drop-shadow-lg\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <defs>\n            {/* Premium Gradients */}\n            <linearGradient id=\"premiumGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#0066cc\" />\n              <stop offset=\"30%\" stopColor=\"#1a7ae8\" />\n              <stop offset=\"70%\" stopColor=\"#00a693\" />\n              <stop offset=\"100%\" stopColor=\"#1ab8a6\" />\n            </linearGradient>\n\n            <linearGradient id=\"crossGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#ffffff\" />\n              <stop offset=\"100%\" stopColor=\"#f0f9ff\" />\n            </linearGradient>\n\n            <linearGradient id=\"accentGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"100%\">\n              <stop offset=\"0%\" stopColor=\"#10b981\" />\n              <stop offset=\"100%\" stopColor=\"#34d399\" />\n            </linearGradient>\n\n            {/* Glow Filter */}\n            <filter id=\"glow\">\n              <feGaussianBlur stdDeviation=\"3\" result=\"coloredBlur\"/>\n              <feMerge>\n                <feMergeNode in=\"coloredBlur\"/>\n                <feMergeNode in=\"SourceGraphic\"/>\n              </feMerge>\n            </filter>\n\n            {/* Shadow Filter */}\n            <filter id=\"dropshadow\" x=\"-50%\" y=\"-50%\" width=\"200%\" height=\"200%\">\n              <feDropShadow dx=\"0\" dy=\"4\" stdDeviation=\"4\" floodColor=\"#0066cc\" floodOpacity=\"0.3\"/>\n            </filter>\n          </defs>\n\n          {/* Outer Ring with Animation */}\n          <motion.circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"55\"\n            fill=\"none\"\n            stroke=\"url(#premiumGradient)\"\n            strokeWidth=\"2\"\n            strokeDasharray=\"10 5\"\n            initial={{ rotate: 0 }}\n            animate={{ rotate: 360 }}\n            transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n            style={{ transformOrigin: \"60px 60px\" }}\n          />\n\n          {/* Main Circle Background */}\n          <circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"48\"\n            fill=\"url(#premiumGradient)\"\n            filter=\"url(#dropshadow)\"\n          />\n\n          {/* Inner Glow Circle */}\n          <circle\n            cx=\"60\"\n            cy=\"60\"\n            r=\"42\"\n            fill=\"none\"\n            stroke=\"rgba(255, 255, 255, 0.3)\"\n            strokeWidth=\"1\"\n          />\n\n          {/* Medical Cross with 3D Effect */}\n          <g transform=\"translate(60, 60)\" filter=\"url(#glow)\">\n            {/* Cross Shadow */}\n            <g transform=\"translate(2, 2)\" opacity=\"0.3\">\n              <rect x=\"-4\" y=\"-24\" width=\"8\" height=\"48\" fill=\"#0066cc\" rx=\"4\" />\n              <rect x=\"-24\" y=\"-4\" width=\"48\" height=\"8\" fill=\"#0066cc\" rx=\"4\" />\n            </g>\n\n            {/* Main Cross */}\n            <rect x=\"-4\" y=\"-24\" width=\"8\" height=\"48\" fill=\"url(#crossGradient)\" rx=\"4\" />\n            <rect x=\"-24\" y=\"-4\" width=\"48\" height=\"8\" fill=\"url(#crossGradient)\" rx=\"4\" />\n\n            {/* Cross Highlights */}\n            <rect x=\"-2\" y=\"-24\" width=\"4\" height=\"48\" fill=\"rgba(255, 255, 255, 0.4)\" rx=\"2\" />\n            <rect x=\"-24\" y=\"-2\" width=\"48\" height=\"4\" fill=\"rgba(255, 255, 255, 0.4)\" rx=\"2\" />\n          </g>\n\n          {/* Decorative Elements */}\n          <g transform=\"translate(60, 60)\">\n            {/* DNA Helix Accent */}\n            <motion.path\n              d=\"M -35 -20 Q -30 -15 -25 -20 Q -20 -25 -15 -20 Q -10 -15 -5 -20\"\n              stroke=\"url(#accentGradient)\"\n              strokeWidth=\"2\"\n              fill=\"none\"\n              opacity=\"0.6\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 2, delay: 0.5 }}\n            />\n\n            {/* Heartbeat Line */}\n            <motion.path\n              d=\"M -35 25 L -25 25 L -20 15 L -15 35 L -10 5 L -5 25 L 5 25\"\n              stroke=\"url(#accentGradient)\"\n              strokeWidth=\"2\"\n              fill=\"none\"\n              opacity=\"0.7\"\n              initial={{ pathLength: 0 }}\n              animate={{ pathLength: 1 }}\n              transition={{ duration: 1.5, delay: 1 }}\n            />\n\n            {/* Floating Particles */}\n            {[\n              { cx: 35, cy: 0 },\n              { cx: 17.5, cy: 30.31 },\n              { cx: -17.5, cy: 30.31 },\n              { cx: -35, cy: 0 },\n              { cx: -17.5, cy: -30.31 },\n              { cx: 17.5, cy: -30.31 }\n            ].map((position, i) => (\n              <motion.circle\n                key={i}\n                cx={position.cx}\n                cy={position.cy}\n                r=\"2\"\n                fill=\"rgba(255, 255, 255, 0.8)\"\n                initial={{ scale: 0, opacity: 0 }}\n                animate={{\n                  scale: [0, 1, 0],\n                  opacity: [0, 1, 0]\n                }}\n                transition={{\n                  duration: 3,\n                  delay: i * 0.2,\n                  repeat: Infinity,\n                  repeatDelay: 2\n                }}\n              />\n            ))}\n          </g>\n        </svg>\n      </motion.div>\n\n      {/* Premium Text Logo */}\n      {showText && (\n        <motion.div\n          className=\"flex flex-col\"\n          variants={textVariants}\n        >\n          <motion.h1\n            className={`font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-teal-600 bg-clip-text text-transparent leading-tight ${textSizeClasses[size]}`}\n            whileHover={{\n              scale: 1.02,\n              transition: { duration: 0.2 }\n            }}\n          >\n            Dr Satya's\n          </motion.h1>\n          <motion.p\n            className={`font-semibold bg-gradient-to-r from-teal-600 to-blue-600 bg-clip-text text-transparent leading-tight ${\n              size === 'sm' ? 'text-xs' :\n              size === 'md' ? 'text-sm' :\n              size === 'lg' ? 'text-lg' : 'text-xl'\n            }`}\n            whileHover={{\n              scale: 1.02,\n              transition: { duration: 0.2 }\n            }}\n          >\n            Liver & Gastro Care\n          </motion.p>\n          {(size === 'lg' || size === 'xl') && (\n            <motion.p\n              className=\"text-xs text-gray-500 mt-1 font-medium tracking-wide\"\n              initial={{ opacity: 0, y: 10 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8, duration: 0.5 }}\n            >\n              Excellence in Digestive Health\n            </motion.p>\n          )}\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport default Logo;\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAYA,MAAM,OAA4B,CAAC,EACjC,OAAO,IAAI,EACX,WAAW,IAAI,EACf,YAAY,EAAE,EACd,UAAU,SAAS,EACpB;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,QAAQ,CAAC;QACX;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,UAAU;gBACV,MAAM;oBAAC;oBAAK,CAAC;oBAAM;oBAAM;iBAAK;gBAC9B,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,OAAO;YAAG,QAAQ,CAAC;QAAG;QAChC,SAAS;YACP,OAAO;YACP,QAAQ;YACR,YAAY;gBACV,MAAM;gBACN,WAAW;gBACX,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC7B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,MAAM;YACR;QACF;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAO;YACL,OAAO;gBAAC;gBAAG;gBAAM;aAAE;YACnB,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;QACF;IACF;IAEA,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,wBAAwB,EAAE,WAAW;QACjD,UAAU;QACV,SAAQ;QACR,SAAQ;QACR,YAAW;;0BAGX,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,SAAS,CAAC;gBAC1C,UAAU;gBACV,YAAY;oBACV,OAAO;oBACP,QAAQ;oBACR,YAAY;wBAAE,UAAU;oBAAI;gBAC9B;0BAEA,cAAA,8OAAC;oBACC,SAAQ;oBACR,WAAU;oBACV,OAAM;;sCAEN,8OAAC;;8CAEC,8OAAC;oCAAe,IAAG;oCAAkB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAChE,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,8OAAC;4CAAK,QAAO;4CAAM,WAAU;;;;;;sDAC7B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAGhC,8OAAC;oCAAe,IAAG;oCAAgB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC9D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAGhC,8OAAC;oCAAe,IAAG;oCAAiB,IAAG;oCAAK,IAAG;oCAAK,IAAG;oCAAO,IAAG;;sDAC/D,8OAAC;4CAAK,QAAO;4CAAK,WAAU;;;;;;sDAC5B,8OAAC;4CAAK,QAAO;4CAAO,WAAU;;;;;;;;;;;;8CAIhC,8OAAC;oCAAO,IAAG;;sDACT,8OAAC;4CAAe,cAAa;4CAAI,QAAO;;;;;;sDACxC,8OAAC;;8DACC,8OAAC;oDAAY,IAAG;;;;;;8DAChB,8OAAC;oDAAY,IAAG;;;;;;;;;;;;;;;;;;8CAKpB,8OAAC;oCAAO,IAAG;oCAAa,GAAE;oCAAO,GAAE;oCAAO,OAAM;oCAAO,QAAO;8CAC5D,cAAA,8OAAC;wCAAa,IAAG;wCAAI,IAAG;wCAAI,cAAa;wCAAI,YAAW;wCAAU,cAAa;;;;;;;;;;;;;;;;;sCAKnF,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;4BACZ,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;4BACZ,iBAAgB;4BAChB,SAAS;gCAAE,QAAQ;4BAAE;4BACrB,SAAS;gCAAE,QAAQ;4BAAI;4BACvB,YAAY;gCAAE,UAAU;gCAAI,QAAQ;gCAAU,MAAM;4BAAS;4BAC7D,OAAO;gCAAE,iBAAiB;4BAAY;;;;;;sCAIxC,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;;;;;;sCAIT,8OAAC;4BACC,IAAG;4BACH,IAAG;4BACH,GAAE;4BACF,MAAK;4BACL,QAAO;4BACP,aAAY;;;;;;sCAId,8OAAC;4BAAE,WAAU;4BAAoB,QAAO;;8CAEtC,8OAAC;oCAAE,WAAU;oCAAkB,SAAQ;;sDACrC,8OAAC;4CAAK,GAAE;4CAAK,GAAE;4CAAM,OAAM;4CAAI,QAAO;4CAAK,MAAK;4CAAU,IAAG;;;;;;sDAC7D,8OAAC;4CAAK,GAAE;4CAAM,GAAE;4CAAK,OAAM;4CAAK,QAAO;4CAAI,MAAK;4CAAU,IAAG;;;;;;;;;;;;8CAI/D,8OAAC;oCAAK,GAAE;oCAAK,GAAE;oCAAM,OAAM;oCAAI,QAAO;oCAAK,MAAK;oCAAsB,IAAG;;;;;;8CACzE,8OAAC;oCAAK,GAAE;oCAAM,GAAE;oCAAK,OAAM;oCAAK,QAAO;oCAAI,MAAK;oCAAsB,IAAG;;;;;;8CAGzE,8OAAC;oCAAK,GAAE;oCAAK,GAAE;oCAAM,OAAM;oCAAI,QAAO;oCAAK,MAAK;oCAA2B,IAAG;;;;;;8CAC9E,8OAAC;oCAAK,GAAE;oCAAM,GAAE;oCAAK,OAAM;oCAAK,QAAO;oCAAI,MAAK;oCAA2B,IAAG;;;;;;;;;;;;sCAIhF,8OAAC;4BAAE,WAAU;;8CAEX,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,SAAQ;oCACR,SAAS;wCAAE,YAAY;oCAAE;oCACzB,SAAS;wCAAE,YAAY;oCAAE;oCACzB,YAAY;wCAAE,UAAU;wCAAG,OAAO;oCAAI;;;;;;8CAIxC,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;oCACV,GAAE;oCACF,QAAO;oCACP,aAAY;oCACZ,MAAK;oCACL,SAAQ;oCACR,SAAS;wCAAE,YAAY;oCAAE;oCACzB,SAAS;wCAAE,YAAY;oCAAE;oCACzB,YAAY;wCAAE,UAAU;wCAAK,OAAO;oCAAE;;;;;;gCAIvC;oCACC;wCAAE,IAAI;wCAAI,IAAI;oCAAE;oCAChB;wCAAE,IAAI;wCAAM,IAAI;oCAAM;oCACtB;wCAAE,IAAI,CAAC;wCAAM,IAAI;oCAAM;oCACvB;wCAAE,IAAI,CAAC;wCAAI,IAAI;oCAAE;oCACjB;wCAAE,IAAI,CAAC;wCAAM,IAAI,CAAC;oCAAM;oCACxB;wCAAE,IAAI;wCAAM,IAAI,CAAC;oCAAM;iCACxB,CAAC,GAAG,CAAC,CAAC,UAAU,kBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wCAEZ,IAAI,SAAS,EAAE;wCACf,IAAI,SAAS,EAAE;wCACf,GAAE;wCACF,MAAK;wCACL,SAAS;4CAAE,OAAO;4CAAG,SAAS;wCAAE;wCAChC,SAAS;4CACP,OAAO;gDAAC;gDAAG;gDAAG;6CAAE;4CAChB,SAAS;gDAAC;gDAAG;gDAAG;6CAAE;wCACpB;wCACA,YAAY;4CACV,UAAU;4CACV,OAAO,IAAI;4CACX,QAAQ;4CACR,aAAa;wCACf;uCAfK;;;;;;;;;;;;;;;;;;;;;;YAuBd,0BACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU;;kCAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAW,CAAC,8GAA8G,EAAE,eAAe,CAAC,KAAK,EAAE;wBACnJ,YAAY;4BACV,OAAO;4BACP,YAAY;gCAAE,UAAU;4BAAI;wBAC9B;kCACD;;;;;;kCAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAW,CAAC,qGAAqG,EAC/G,SAAS,OAAO,YAChB,SAAS,OAAO,YAChB,SAAS,OAAO,YAAY,WAC5B;wBACF,YAAY;4BACV,OAAO;4BACP,YAAY;gCAAE,UAAU;4BAAI;wBAC9B;kCACD;;;;;;oBAGA,CAAC,SAAS,QAAQ,SAAS,IAAI,mBAC9B,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;4BAAK,UAAU;wBAAI;kCACzC;;;;;;;;;;;;;;;;;;AAQb;uCAEe", "debugId": null}}, {"offset": {"line": 840, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { motion } from 'framer-motion';\n\ninterface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  text?: string;\n  fullScreen?: boolean;\n  className?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ \n  size = 'md', \n  text = 'Loading...', \n  fullScreen = false,\n  className = '' \n}) => {\n  const sizeClasses = {\n    sm: 'w-6 h-6',\n    md: 'w-10 h-10',\n    lg: 'w-16 h-16'\n  };\n\n  const LoadingSpinner = () => (\n    <div className=\"relative\">\n      {/* Outer ring */}\n      <motion.div\n        className={`${sizeClasses[size]} border-4 border-blue-100 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Inner spinning ring */}\n      <motion.div\n        className={`absolute inset-0 ${sizeClasses[size]} border-4 border-transparent border-t-blue-600 rounded-full`}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n      \n      {/* Medical cross in center */}\n      <div className=\"absolute inset-0 flex items-center justify-center\">\n        <div className=\"w-3 h-3 relative\">\n          <div className=\"absolute inset-x-1/2 inset-y-0 w-0.5 bg-blue-600 transform -translate-x-1/2\" />\n          <div className=\"absolute inset-y-1/2 inset-x-0 h-0.5 bg-blue-600 transform -translate-y-1/2\" />\n        </div>\n      </div>\n    </div>\n  );\n\n  const LoadingDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((index) => (\n        <motion.div\n          key={index}\n          className=\"w-2 h-2 bg-blue-600 rounded-full\"\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7],\n          }}\n          transition={{\n            duration: 1,\n            repeat: Infinity,\n            delay: index * 0.2,\n          }}\n        />\n      ))}\n    </div>\n  );\n\n  const content = (\n    <motion.div\n      className={`flex flex-col items-center justify-center gap-4 ${className}`}\n      initial={{ opacity: 0, scale: 0.9 }}\n      animate={{ opacity: 1, scale: 1 }}\n      transition={{ duration: 0.3 }}\n    >\n      <LoadingSpinner />\n      \n      {text && (\n        <motion.div\n          className=\"text-center\"\n          initial={{ opacity: 0, y: 10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.2 }}\n        >\n          <p className=\"text-gray-600 font-medium\">{text}</p>\n          <LoadingDots />\n        </motion.div>\n      )}\n    </motion.div>\n  );\n\n  if (fullScreen) {\n    return (\n      <motion.div\n        className=\"fixed inset-0 bg-white bg-opacity-90 backdrop-blur-sm z-50 flex items-center justify-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        exit={{ opacity: 0 }}\n      >\n        {content}\n      </motion.div>\n    );\n  }\n\n  return content;\n};\n\n// Skeleton Loading Component\ninterface SkeletonProps {\n  className?: string;\n  lines?: number;\n  avatar?: boolean;\n}\n\nexport const Skeleton: React.FC<SkeletonProps> = ({ \n  className = '', \n  lines = 3, \n  avatar = false \n}) => {\n  return (\n    <div className={`animate-pulse ${className}`}>\n      <div className=\"flex items-start space-x-4\">\n        {avatar && (\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n        )}\n        <div className=\"flex-1 space-y-2\">\n          {Array.from({ length: lines }).map((_, index) => (\n            <div\n              key={index}\n              className={`h-4 bg-gray-200 rounded ${\n                index === lines - 1 ? 'w-3/4' : 'w-full'\n              }`}\n            />\n          ))}\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Card Skeleton\nexport const CardSkeleton: React.FC<{ className?: string }> = ({ className = '' }) => {\n  return (\n    <div className={`medical-card p-6 ${className}`}>\n      <div className=\"animate-pulse\">\n        <div className=\"flex items-center space-x-4 mb-4\">\n          <div className=\"w-12 h-12 bg-gray-200 rounded-full\" />\n          <div className=\"flex-1\">\n            <div className=\"h-4 bg-gray-200 rounded w-3/4 mb-2\" />\n            <div className=\"h-3 bg-gray-200 rounded w-1/2\" />\n          </div>\n        </div>\n        <div className=\"space-y-3\">\n          <div className=\"h-3 bg-gray-200 rounded\" />\n          <div className=\"h-3 bg-gray-200 rounded w-5/6\" />\n          <div className=\"h-3 bg-gray-200 rounded w-4/6\" />\n        </div>\n      </div>\n    </div>\n  );\n};\n\n// Button Loading State\ninterface ButtonLoadingProps {\n  loading?: boolean;\n  children: React.ReactNode;\n  className?: string;\n  onClick?: () => void;\n  disabled?: boolean;\n}\n\nexport const ButtonLoading: React.FC<ButtonLoadingProps> = ({\n  loading = false,\n  children,\n  className = '',\n  onClick,\n  disabled = false\n}) => {\n  return (\n    <button\n      className={`btn-primary relative ${className} ${\n        loading || disabled ? 'opacity-70 cursor-not-allowed' : ''\n      }`}\n      onClick={onClick}\n      disabled={loading || disabled}\n    >\n      {loading && (\n        <div className=\"absolute inset-0 flex items-center justify-center\">\n          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n        </div>\n      )}\n      <span className={loading ? 'opacity-0' : 'opacity-100'}>\n        {children}\n      </span>\n    </button>\n  );\n};\n\nexport default Loading;\n"], "names": [], "mappings": ";;;;;;;AAGA;AAHA;;;AAYA,MAAM,UAAkC,CAAC,EACvC,OAAO,IAAI,EACX,OAAO,YAAY,EACnB,aAAa,KAAK,EAClB,YAAY,EAAE,EACf;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,GAAG,WAAW,CAAC,KAAK,CAAC,sCAAsC,CAAC;oBACvE,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAW,CAAC,iBAAiB,EAAE,WAAW,CAAC,KAAK,CAAC,2DAA2D,CAAC;oBAC7G,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;;;;;;8BAI9D,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAMvB,MAAM,cAAc,kBAClB,8OAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,sBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,QAAQ;oBACjB;mBAVK;;;;;;;;;;IAgBb,MAAM,wBACJ,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,gDAAgD,EAAE,WAAW;QACzE,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,UAAU;QAAI;;0BAE5B,8OAAC;;;;;YAEA,sBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;;kCAEzB,8OAAC;wBAAE,WAAU;kCAA6B;;;;;;kCAC1C,8OAAC;;;;;;;;;;;;;;;;;IAMT,IAAI,YAAY;QACd,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;sBAElB;;;;;;IAGP;IAEA,OAAO;AACT;AASO,MAAM,WAAoC,CAAC,EAChD,YAAY,EAAE,EACd,QAAQ,CAAC,EACT,SAAS,KAAK,EACf;IACC,qBACE,8OAAC;QAAI,WAAW,CAAC,cAAc,EAAE,WAAW;kBAC1C,cAAA,8OAAC;YAAI,WAAU;;gBACZ,wBACC,8OAAC;oBAAI,WAAU;;;;;;8BAEjB,8OAAC;oBAAI,WAAU;8BACZ,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAM,GAAG,GAAG,CAAC,CAAC,GAAG,sBACrC,8OAAC;4BAEC,WAAW,CAAC,wBAAwB,EAClC,UAAU,QAAQ,IAAI,UAAU,UAChC;2BAHG;;;;;;;;;;;;;;;;;;;;;AAUnB;AAGO,MAAM,eAAiD,CAAC,EAAE,YAAY,EAAE,EAAE;IAC/E,qBACE,8OAAC;QAAI,WAAW,CAAC,iBAAiB,EAAE,WAAW;kBAC7C,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;8BAGnB,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB;AAWO,MAAM,gBAA8C,CAAC,EAC1D,UAAU,KAAK,EACf,QAAQ,EACR,YAAY,EAAE,EACd,OAAO,EACP,WAAW,KAAK,EACjB;IACC,qBACE,8OAAC;QACC,WAAW,CAAC,qBAAqB,EAAE,UAAU,CAAC,EAC5C,WAAW,WAAW,kCAAkC,IACxD;QACF,SAAS;QACT,UAAU,WAAW;;YAEpB,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;0BAGnB,8OAAC;gBAAK,WAAW,UAAU,cAAc;0BACtC;;;;;;;;;;;;AAIT;uCAEe", "debugId": null}}, {"offset": {"line": 1208, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/components/auth/LoginPage.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport { motion } from 'framer-motion';\nimport { useForm } from 'react-hook-form';\nimport { zodResolver } from '@hookform/resolvers/zod';\nimport { z } from 'zod';\nimport { EyeIcon, EyeSlashIcon, LockClosedIcon, UserIcon } from '@heroicons/react/24/outline';\nimport { toast } from 'react-hot-toast';\nimport Logo from '../ui/Logo';\nimport { ButtonLoading } from '../ui/Loading';\nimport { useAuthStore } from '@/store';\n\n// Validation schema\nconst loginSchema = z.object({\n  email: z.string().email('Please enter a valid email address'),\n  password: z.string().min(6, 'Password must be at least 6 characters'),\n});\n\ntype LoginFormData = z.infer<typeof loginSchema>;\n\nconst LoginPage: React.FC = () => {\n  const [showPassword, setShowPassword] = useState(false);\n  const [isLoading, setIsLoading] = useState(false);\n  const { login } = useAuthStore();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<LoginFormData>({\n    resolver: zodResolver(loginSchema),\n  });\n\n  const onSubmit = async (data: LoginFormData) => {\n    setIsLoading(true);\n    try {\n      // Simulate API call - replace with actual authentication\n      await new Promise(resolve => setTimeout(resolve, 1500));\n      \n      const success = await login(data);\n      if (success) {\n        toast.success('Welcome back!');\n        // Navigation will be handled by the parent component\n      } else {\n        toast.error('Invalid credentials. Please try again.');\n      }\n    } catch (error) {\n      toast.error('Login failed. Please try again.');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const containerVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: {\n      opacity: 1,\n      y: 0,\n      transition: {\n        duration: 0.6,\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const itemVariants = {\n    hidden: { opacity: 0, y: 20 },\n    visible: { opacity: 1, y: 0 },\n  };\n\n  return (\n    <div className=\"min-h-screen relative overflow-hidden\">\n      {/* Premium Animated Background */}\n      <div className=\"absolute inset-0 gradient-animate\"></div>\n\n      {/* Floating Medical Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {/* Animated DNA Helixes */}\n        {[...Array(5)].map((_, i) => (\n          <motion.div\n            key={i}\n            className=\"absolute w-2 h-32 opacity-10\"\n            style={{\n              left: `${20 + i * 20}%`,\n              top: `${10 + i * 15}%`,\n            }}\n            animate={{\n              y: [-20, 20, -20],\n              rotate: [0, 360],\n            }}\n            transition={{\n              duration: 8 + i * 2,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n            }}\n          >\n            <div className=\"w-full h-full bg-gradient-to-b from-blue-400 to-teal-400 rounded-full blur-sm\"></div>\n          </motion.div>\n        ))}\n\n        {/* Floating Medical Icons */}\n        {[\n          { left: '10%', top: '20%', delay: 0 },\n          { left: '85%', top: '15%', delay: 0.5 },\n          { left: '15%', top: '80%', delay: 1 },\n          { left: '90%', top: '70%', delay: 1.5 },\n          { left: '5%', top: '50%', delay: 2 },\n          { left: '95%', top: '40%', delay: 2.5 },\n          { left: '25%', top: '10%', delay: 3 },\n          { left: '75%', top: '90%', delay: 3.5 }\n        ].map((position, i) => (\n          <motion.div\n            key={`icon-${i}`}\n            className=\"absolute w-8 h-8 opacity-5\"\n            style={{\n              left: position.left,\n              top: position.top,\n            }}\n            animate={{\n              y: [-10, 10, -10],\n              x: [-5, 5, -5],\n              rotate: [0, 180, 360],\n            }}\n            transition={{\n              duration: 6 + i * 0.5,\n              repeat: Infinity,\n              ease: \"easeInOut\",\n              delay: position.delay,\n            }}\n          >\n            <svg viewBox=\"0 0 24 24\" fill=\"currentColor\" className=\"w-full h-full text-white\">\n              <path d=\"M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z\"/>\n            </svg>\n          </motion.div>\n        ))}\n\n        {/* Geometric Shapes */}\n        <motion.div\n          className=\"absolute top-20 right-20 w-32 h-32 border-2 border-white/20 rounded-full\"\n          animate={{ rotate: 360 }}\n          transition={{ duration: 20, repeat: Infinity, ease: \"linear\" }}\n        />\n        <motion.div\n          className=\"absolute bottom-20 left-20 w-24 h-24 border-2 border-white/20 rounded-lg\"\n          animate={{ rotate: -360 }}\n          transition={{ duration: 15, repeat: Infinity, ease: \"linear\" }}\n        />\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 min-h-screen flex items-center justify-center p-4\">\n        <motion.div\n          className=\"w-full max-w-lg\"\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n        >\n          {/* Premium Login Card */}\n          <motion.div\n            className=\"medical-card-glass p-10 relative overflow-hidden\"\n            variants={itemVariants}\n            whileHover={{\n              y: -8,\n              transition: { duration: 0.3 }\n            }}\n          >\n            {/* Card Glow Effect */}\n            <div className=\"absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-teal-500/10 rounded-2xl blur-xl\"></div>\n\n            {/* Content */}\n            <div className=\"relative z-10\">\n            {/* Logo and Header */}\n            <motion.div className=\"text-center mb-10\" variants={itemVariants}>\n              <div className=\"flex justify-center mb-8\">\n                <Logo size=\"xl\" showText variant=\"premium\" />\n              </div>\n              <motion.h1\n                className=\"text-3xl font-bold bg-gradient-to-r from-gray-800 via-blue-800 to-teal-800 bg-clip-text text-transparent mb-3\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.3, duration: 0.6 }}\n              >\n                Welcome Back\n              </motion.h1>\n              <motion.p\n                className=\"text-gray-600 text-lg font-medium\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4, duration: 0.6 }}\n              >\n                Sign in to access your medical dashboard\n              </motion.p>\n\n              {/* Decorative Line */}\n              <motion.div\n                className=\"w-24 h-1 bg-gradient-to-r from-blue-500 to-teal-500 mx-auto mt-4 rounded-full\"\n                initial={{ width: 0 }}\n                animate={{ width: 96 }}\n                transition={{ delay: 0.6, duration: 0.8 }}\n              />\n            </motion.div>\n\n            {/* Premium Login Form */}\n            <motion.form onSubmit={handleSubmit(onSubmit)} variants={itemVariants}>\n              <div className=\"space-y-8\">\n                {/* Email Field with Floating Label */}\n                <motion.div\n                  className=\"form-group\"\n                  whileHover={{ scale: 1.02 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\n                      <UserIcon className=\"h-5 w-5 text-gray-400 transition-colors duration-300\" />\n                    </div>\n                    <input\n                      {...register('email')}\n                      type=\"email\"\n                      className={`form-input-floating pl-12 ${errors.email ? 'error' : ''}`}\n                      placeholder=\" \"\n                      autoComplete=\"email\"\n                    />\n                    <label className=\"form-label-floating\">Email Address</label>\n                  </div>\n                  {errors.email && (\n                    <motion.p\n                      className=\"mt-2 text-sm text-red-500 flex items-center gap-1\"\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                      </svg>\n                      {errors.email.message}\n                    </motion.p>\n                  )}\n                </motion.div>\n\n                {/* Password Field with Floating Label */}\n                <motion.div\n                  className=\"form-group\"\n                  whileHover={{ scale: 1.02 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  <div className=\"relative\">\n                    <div className=\"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none z-10\">\n                      <LockClosedIcon className=\"h-5 w-5 text-gray-400 transition-colors duration-300\" />\n                    </div>\n                    <input\n                      {...register('password')}\n                      type={showPassword ? 'text' : 'password'}\n                      className={`form-input-floating pl-12 pr-12 ${errors.password ? 'error' : ''}`}\n                      placeholder=\" \"\n                      autoComplete=\"current-password\"\n                    />\n                    <label className=\"form-label-floating\">Password</label>\n                    <motion.button\n                      type=\"button\"\n                      className=\"absolute inset-y-0 right-0 pr-4 flex items-center z-10\"\n                      onClick={() => setShowPassword(!showPassword)}\n                      whileHover={{ scale: 1.1 }}\n                      whileTap={{ scale: 0.9 }}\n                    >\n                      {showPassword ? (\n                        <EyeSlashIcon className=\"h-5 w-5 text-gray-400 hover:text-blue-500 transition-colors duration-200\" />\n                      ) : (\n                        <EyeIcon className=\"h-5 w-5 text-gray-400 hover:text-blue-500 transition-colors duration-200\" />\n                      )}\n                    </motion.button>\n                  </div>\n                  {errors.password && (\n                    <motion.p\n                      className=\"mt-2 text-sm text-red-500 flex items-center gap-1\"\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                    >\n                      <svg className=\"w-4 h-4\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                      </svg>\n                      {errors.password.message}\n                    </motion.p>\n                  )}\n                </motion.div>\n\n                {/* Remember Me & Forgot Password */}\n                <div className=\"flex items-center justify-between\">\n                  <motion.label\n                    className=\"flex items-center cursor-pointer\"\n                    whileHover={{ scale: 1.02 }}\n                  >\n                    <input\n                      type=\"checkbox\"\n                      className=\"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-all duration-200\"\n                    />\n                    <span className=\"ml-3 text-sm text-gray-600 font-medium\">Remember me</span>\n                  </motion.label>\n                  <motion.a\n                    href=\"#\"\n                    className=\"text-sm text-blue-600 hover:text-blue-700 font-semibold transition-colors duration-200\"\n                    whileHover={{ scale: 1.05 }}\n                    whileTap={{ scale: 0.95 }}\n                  >\n                    Forgot password?\n                  </motion.a>\n                </div>\n\n                {/* Premium Submit Button */}\n                <motion.div\n                  whileHover={{ scale: 1.02 }}\n                  whileTap={{ scale: 0.98 }}\n                >\n                  <ButtonLoading\n                    loading={isLoading}\n                    className=\"w-full btn-primary text-lg py-4 relative overflow-hidden\"\n                    disabled={isLoading}\n                  >\n                    <motion.span\n                      className=\"relative z-10\"\n                      initial={{ opacity: 0 }}\n                      animate={{ opacity: 1 }}\n                      transition={{ delay: 0.2 }}\n                    >\n                      {isLoading ? (\n                        <div className=\"flex items-center justify-center gap-3\">\n                          <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n                          Signing in...\n                        </div>\n                      ) : (\n                        <div className=\"flex items-center justify-center gap-2\">\n                          <svg className=\"w-5 h-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1\" />\n                          </svg>\n                          Sign In to Dashboard\n                        </div>\n                      )}\n                    </motion.span>\n                  </ButtonLoading>\n                </motion.div>\n              </div>\n            </motion.form>\n\n            {/* Premium Demo Credentials */}\n            <motion.div\n              className=\"mt-10 p-6 bg-gradient-to-r from-blue-50 via-indigo-50 to-teal-50 rounded-2xl border border-blue-100 relative overflow-hidden\"\n              variants={itemVariants}\n              whileHover={{ scale: 1.02 }}\n            >\n              {/* Background Pattern */}\n              <div className=\"absolute inset-0 opacity-5\">\n                <svg className=\"w-full h-full\" viewBox=\"0 0 100 100\">\n                  <pattern id=\"demo-pattern\" x=\"0\" y=\"0\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\">\n                    <circle cx=\"10\" cy=\"10\" r=\"2\" fill=\"currentColor\" />\n                  </pattern>\n                  <rect width=\"100\" height=\"100\" fill=\"url(#demo-pattern)\" />\n                </svg>\n              </div>\n\n              <div className=\"relative z-10\">\n                <div className=\"flex items-center gap-3 mb-4\">\n                  <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-teal-500 rounded-lg flex items-center justify-center\">\n                    <svg className=\"w-4 h-4 text-white\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                      <path fillRule=\"evenodd\" d=\"M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                  </div>\n                  <h3 className=\"text-lg font-bold bg-gradient-to-r from-blue-700 to-teal-700 bg-clip-text text-transparent\">\n                    Demo Access\n                  </h3>\n                </div>\n\n                <div className=\"grid grid-cols-1 gap-3\">\n                  <div className=\"flex items-center justify-between p-3 bg-white/60 rounded-lg backdrop-blur-sm\">\n                    <span className=\"text-sm font-medium text-gray-600\">Email:</span>\n                    <code className=\"text-sm font-bold text-blue-700 bg-blue-100 px-2 py-1 rounded\">\n                      <EMAIL>\n                    </code>\n                  </div>\n                  <div className=\"flex items-center justify-between p-3 bg-white/60 rounded-lg backdrop-blur-sm\">\n                    <span className=\"text-sm font-medium text-gray-600\">Password:</span>\n                    <code className=\"text-sm font-bold text-teal-700 bg-teal-100 px-2 py-1 rounded\">\n                      demo123\n                    </code>\n                  </div>\n                </div>\n\n                <motion.p\n                  className=\"text-xs text-gray-500 mt-3 text-center\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: 1 }}\n                >\n                  Use these credentials to explore the medical dashboard\n                </motion.p>\n              </div>\n            </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Premium Footer */}\n          <motion.div\n            className=\"text-center mt-8\"\n            variants={itemVariants}\n          >\n            <motion.p\n              className=\"text-sm text-white/80 font-medium\"\n              whileHover={{ scale: 1.05 }}\n            >\n              © 2024 Dr Satya&apos;s Liver & Gastro Care. All rights reserved.\n            </motion.p>\n            <motion.div\n              className=\"flex items-center justify-center gap-4 mt-3\"\n              initial={{ opacity: 0 }}\n              animate={{ opacity: 1 }}\n              transition={{ delay: 1.2 }}\n            >\n              <span className=\"text-xs text-white/60\">Powered by</span>\n              <div className=\"flex items-center gap-1\">\n                <div className=\"w-4 h-4 bg-gradient-to-r from-blue-400 to-teal-400 rounded-full\"></div>\n                <span className=\"text-xs font-semibold text-white/80\">Medical AI</span>\n              </div>\n            </motion.div>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AAXA;;;;;;;;;;;;AAaA,oBAAoB;AACpB,MAAM,cAAc,6KAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3B,OAAO,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,6KAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC9B;AAIA,MAAM,YAAsB;IAC1B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAE7B,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,aAAa;QACb,IAAI;YACF,yDAAyD;YACzD,MAAM,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;YAEjD,MAAM,UAAU,MAAM,MAAM;YAC5B,IAAI,SAAS;gBACX,uJAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,qDAAqD;YACvD,OAAO;gBACL,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,uJAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,oBAAoB;QACxB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YACP,SAAS;YACT,GAAG;YACH,YAAY;gBACV,UAAU;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,eAAe;QACnB,QAAQ;YAAE,SAAS;YAAG,GAAG;QAAG;QAC5B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;;;;;0BAGf,8OAAC;gBAAI,WAAU;;oBAEZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;gCACvB,KAAK,GAAG,KAAK,IAAI,GAAG,CAAC,CAAC;4BACxB;4BACA,SAAS;gCACP,GAAG;oCAAC,CAAC;oCAAI;oCAAI,CAAC;iCAAG;gCACjB,QAAQ;oCAAC;oCAAG;iCAAI;4BAClB;4BACA,YAAY;gCACV,UAAU,IAAI,IAAI;gCAClB,QAAQ;gCACR,MAAM;4BACR;sCAEA,cAAA,8OAAC;gCAAI,WAAU;;;;;;2BAhBV;;;;;oBAqBR;wBACC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAE;wBACpC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAI;wBACtC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAE;wBACpC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAI;wBACtC;4BAAE,MAAM;4BAAM,KAAK;4BAAO,OAAO;wBAAE;wBACnC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAI;wBACtC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAE;wBACpC;4BAAE,MAAM;4BAAO,KAAK;4BAAO,OAAO;wBAAI;qBACvC,CAAC,GAAG,CAAC,CAAC,UAAU,kBACf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,OAAO;gCACL,MAAM,SAAS,IAAI;gCACnB,KAAK,SAAS,GAAG;4BACnB;4BACA,SAAS;gCACP,GAAG;oCAAC,CAAC;oCAAI;oCAAI,CAAC;iCAAG;gCACjB,GAAG;oCAAC,CAAC;oCAAG;oCAAG,CAAC;iCAAE;gCACd,QAAQ;oCAAC;oCAAG;oCAAK;iCAAI;4BACvB;4BACA,YAAY;gCACV,UAAU,IAAI,IAAI;gCAClB,QAAQ;gCACR,MAAM;gCACN,OAAO,SAAS,KAAK;4BACvB;sCAEA,cAAA,8OAAC;gCAAI,SAAQ;gCAAY,MAAK;gCAAe,WAAU;0CACrD,cAAA,8OAAC;oCAAK,GAAE;;;;;;;;;;;2BAnBL,CAAC,KAAK,EAAE,GAAG;;;;;kCAyBpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;wBAAI;wBACvB,YAAY;4BAAE,UAAU;4BAAI,QAAQ;4BAAU,MAAM;wBAAS;;;;;;kCAE/D,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ,CAAC;wBAAI;wBACxB,YAAY;4BAAE,UAAU;4BAAI,QAAQ;4BAAU,MAAM;wBAAS;;;;;;;;;;;;0BAKjE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU;oBACV,SAAQ;oBACR,SAAQ;;sCAGR,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;4BACV,YAAY;gCACV,GAAG,CAAC;gCACJ,YAAY;oCAAE,UAAU;gCAAI;4BAC9B;;8CAGA,8OAAC;oCAAI,WAAU;;;;;;8CAGf,8OAAC;oCAAI,WAAU;;sDAEf,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CAAC,WAAU;4CAAoB,UAAU;;8DAClD,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC,gIAAA,CAAA,UAAI;wDAAC,MAAK;wDAAK,QAAQ;wDAAC,SAAQ;;;;;;;;;;;8DAEnC,8OAAC,0LAAA,CAAA,SAAM,CAAC,EAAE;oDACR,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO;wDAAK,UAAU;oDAAI;8DACzC;;;;;;8DAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oDACP,WAAU;oDACV,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAG;oDAC7B,SAAS;wDAAE,SAAS;wDAAG,GAAG;oDAAE;oDAC5B,YAAY;wDAAE,OAAO;wDAAK,UAAU;oDAAI;8DACzC;;;;;;8DAKD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAG;oDACrB,YAAY;wDAAE,OAAO;wDAAK,UAAU;oDAAI;;;;;;;;;;;;sDAK5C,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;4CAAC,UAAU,aAAa;4CAAW,UAAU;sDACvD,cAAA,8OAAC;gDAAI,WAAU;;kEAEb,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,YAAY;4DAAE,UAAU;wDAAI;;0EAE5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,+MAAA,CAAA,WAAQ;4EAAC,WAAU;;;;;;;;;;;kFAEtB,8OAAC;wEACE,GAAG,SAAS,QAAQ;wEACrB,MAAK;wEACL,WAAW,CAAC,0BAA0B,EAAE,OAAO,KAAK,GAAG,UAAU,IAAI;wEACrE,aAAY;wEACZ,cAAa;;;;;;kFAEf,8OAAC;wEAAM,WAAU;kFAAsB;;;;;;;;;;;;4DAExC,OAAO,KAAK,kBACX,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gEACP,WAAU;gEACV,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;;kFAE5B,8OAAC;wEAAI,WAAU;wEAAU,MAAK;wEAAe,SAAQ;kFACnD,cAAA,8OAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAoH,UAAS;;;;;;;;;;;oEAEzJ,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;;kEAM3B,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,WAAU;wDACV,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,YAAY;4DAAE,UAAU;wDAAI;;0EAE5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC,2NAAA,CAAA,iBAAc;4EAAC,WAAU;;;;;;;;;;;kFAE5B,8OAAC;wEACE,GAAG,SAAS,WAAW;wEACxB,MAAM,eAAe,SAAS;wEAC9B,WAAW,CAAC,gCAAgC,EAAE,OAAO,QAAQ,GAAG,UAAU,IAAI;wEAC9E,aAAY;wEACZ,cAAa;;;;;;kFAEf,8OAAC;wEAAM,WAAU;kFAAsB;;;;;;kFACvC,8OAAC,0LAAA,CAAA,SAAM,CAAC,MAAM;wEACZ,MAAK;wEACL,WAAU;wEACV,SAAS,IAAM,gBAAgB,CAAC;wEAChC,YAAY;4EAAE,OAAO;wEAAI;wEACzB,UAAU;4EAAE,OAAO;wEAAI;kFAEtB,6BACC,8OAAC,uNAAA,CAAA,eAAY;4EAAC,WAAU;;;;;qIAExB,8OAAC,6MAAA,CAAA,UAAO;4EAAC,WAAU;;;;;;;;;;;;;;;;;4DAIxB,OAAO,QAAQ,kBACd,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gEACP,WAAU;gEACV,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;;kFAE5B,8OAAC;wEAAI,WAAU;wEAAU,MAAK;wEAAe,SAAQ;kFACnD,cAAA,8OAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAoH,UAAS;;;;;;;;;;;oEAEzJ,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;;kEAM9B,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,0LAAA,CAAA,SAAM,CAAC,KAAK;gEACX,WAAU;gEACV,YAAY;oEAAE,OAAO;gEAAK;;kFAE1B,8OAAC;wEACC,MAAK;wEACL,WAAU;;;;;;kFAEZ,8OAAC;wEAAK,WAAU;kFAAyC;;;;;;;;;;;;0EAE3D,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;gEACP,MAAK;gEACL,WAAU;gEACV,YAAY;oEAAE,OAAO;gEAAK;gEAC1B,UAAU;oEAAE,OAAO;gEAAK;0EACzB;;;;;;;;;;;;kEAMH,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wDACT,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEAExB,cAAA,8OAAC,mIAAA,CAAA,gBAAa;4DACZ,SAAS;4DACT,WAAU;4DACV,UAAU;sEAEV,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,IAAI;gEACV,WAAU;gEACV,SAAS;oEAAE,SAAS;gEAAE;gEACtB,SAAS;oEAAE,SAAS;gEAAE;gEACtB,YAAY;oEAAE,OAAO;gEAAI;0EAExB,0BACC,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;;;;;;wEAAiF;;;;;;6HAIlG,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAU,MAAK;4EAAO,QAAO;4EAAe,SAAQ;sFACjE,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;wEACjE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAWpB,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,UAAU;4CACV,YAAY;gDAAE,OAAO;4CAAK;;8DAG1B,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;wDAAgB,SAAQ;;0EACrC,8OAAC;gEAAQ,IAAG;gEAAe,GAAE;gEAAI,GAAE;gEAAI,OAAM;gEAAK,QAAO;gEAAK,cAAa;0EACzE,cAAA,8OAAC;oEAAO,IAAG;oEAAK,IAAG;oEAAK,GAAE;oEAAI,MAAK;;;;;;;;;;;0EAErC,8OAAC;gEAAK,OAAM;gEAAM,QAAO;gEAAM,MAAK;;;;;;;;;;;;;;;;;8DAIxC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;8EACb,cAAA,8OAAC;wEAAI,WAAU;wEAAqB,MAAK;wEAAe,SAAQ;kFAC9D,cAAA,8OAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAsI,UAAS;;;;;;;;;;;;;;;;8EAG9K,8OAAC;oEAAG,WAAU;8EAA6F;;;;;;;;;;;;sEAK7G,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAK,WAAU;sFAAgE;;;;;;;;;;;;8EAIlF,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAK,WAAU;sFAAoC;;;;;;sFACpD,8OAAC;4EAAK,WAAU;sFAAgE;;;;;;;;;;;;;;;;;;sEAMpF,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;4DACP,WAAU;4DACV,SAAS;gEAAE,SAAS;4DAAE;4DACtB,SAAS;gEAAE,SAAS;4DAAE;4DACtB,YAAY;gEAAE,OAAO;4DAAE;sEACxB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCASP,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,UAAU;;8CAEV,8OAAC,0LAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,YAAY;wCAAE,OAAO;oCAAK;8CAC3B;;;;;;8CAGD,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;oCAAE;oCACtB,SAAS;wCAAE,SAAS;oCAAE;oCACtB,YAAY;wCAAE,OAAO;oCAAI;;sDAEzB,8OAAC;4CAAK,WAAU;sDAAwB;;;;;;sDACxC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;;;;;8DACf,8OAAC;oDAAK,WAAU;8DAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtE;uCAEe", "debugId": null}}, {"offset": {"line": 2238, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect } from 'react';\nimport { useRouter } from 'next/navigation';\nimport { useAuthStore } from '@/store';\nimport LoginPage from '@/components/auth/LoginPage';\n\nexport default function Home() {\n  const router = useRouter();\n  const { isAuthenticated } = useAuthStore();\n\n  useEffect(() => {\n    if (isAuthenticated) {\n      router.push('/dashboard');\n    }\n  }, [isAuthenticated, router]);\n\n  if (isAuthenticated) {\n    return null; // Will redirect to dashboard\n  }\n\n  return <LoginPage />;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,qHAAA,CAAA,eAAY,AAAD;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,iBAAiB;YACnB,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;KAAO;IAE5B,IAAI,iBAAiB;QACnB,OAAO,MAAM,6BAA6B;IAC5C;IAEA,qBAAO,8OAAC,uIAAA,CAAA,UAAS;;;;;AACnB", "debugId": null}}]}