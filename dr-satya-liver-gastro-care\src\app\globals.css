@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import "tailwindcss";

:root {
  /* Premium Medical Theme Colors */
  --primary-medical: #0066cc;
  --primary-medical-light: #1a7ae8;
  --primary-medical-dark: #004499;
  --primary-medical-50: #e6f3ff;
  --primary-medical-100: #b3d9ff;
  --primary-medical-200: #80bfff;
  --primary-medical-300: #4da6ff;
  --primary-medical-400: #1a8cff;
  --primary-medical-500: #0066cc;
  --primary-medical-600: #0052a3;
  --primary-medical-700: #003d7a;
  --primary-medical-800: #002952;
  --primary-medical-900: #001429;

  /* Secondary Colors */
  --secondary-teal: #00a693;
  --secondary-teal-light: #1ab8a6;
  --secondary-teal-dark: #008a7a;
  --accent-purple: #6366f1;
  --accent-purple-light: #818cf8;
  --accent-purple-dark: #4f46e5;

  /* Status Colors */
  --success-emerald: #10b981;
  --success-emerald-light: #34d399;
  --success-emerald-dark: #059669;
  --warning-amber: #f59e0b;
  --warning-amber-light: #fbbf24;
  --warning-amber-dark: #d97706;
  --error-red: #ef4444;
  --error-red-light: #f87171;
  --error-red-dark: #dc2626;
  --info-blue: #3b82f6;
  --info-blue-light: #60a5fa;
  --info-blue-dark: #2563eb;

  /* Neutral Palette */
  --neutral-50: #fafafa;
  --neutral-100: #f5f5f5;
  --neutral-200: #e5e5e5;
  --neutral-300: #d4d4d4;
  --neutral-400: #a3a3a3;
  --neutral-500: #737373;
  --neutral-600: #525252;
  --neutral-700: #404040;
  --neutral-800: #262626;
  --neutral-900: #171717;

  /* Medical Specific */
  --medical-white: #ffffff;
  --medical-cream: #fefdfb;
  --medical-light: #f8fafc;
  --medical-border: #e2e8f0;
  --medical-border-light: #f1f5f9;

  /* Advanced Shadows */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-medical: 0 4px 20px rgba(0, 102, 204, 0.15);
  --shadow-medical-lg: 0 8px 30px rgba(0, 102, 204, 0.2);

  /* Gradients */
  --gradient-medical: linear-gradient(135deg, var(--primary-medical) 0%, var(--secondary-teal) 100%);
  --gradient-medical-light: linear-gradient(135deg, var(--primary-medical-100) 0%, var(--secondary-teal-light) 100%);
  --gradient-success: linear-gradient(135deg, var(--success-emerald) 0%, var(--success-emerald-light) 100%);
  --gradient-warning: linear-gradient(135deg, var(--warning-amber) 0%, var(--warning-amber-light) 100%);
  --gradient-error: linear-gradient(135deg, var(--error-red) 0%, var(--error-red-light) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 100%);

  --background: var(--medical-light);
  --foreground: var(--neutral-900);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html,
body {
  max-width: 100vw;
  overflow-x: hidden;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  background: var(--background);
  color: var(--foreground);
  line-height: 1.6;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
  background: var(--gray-300);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--gray-400);
}

/* Premium Medical Card Styles */
.medical-card {
  background: var(--medical-white);
  border: 1px solid var(--medical-border-light);
  border-radius: 16px;
  box-shadow: var(--shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.medical-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--gradient-medical);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.medical-card:hover {
  box-shadow: var(--shadow-medical-lg);
  transform: translateY(-4px) scale(1.01);
  border-color: var(--primary-medical-200);
}

.medical-card:hover::before {
  opacity: 1;
}

/* Glass Card Effect */
.medical-card-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.medical-card-glass:hover {
  background: rgba(255, 255, 255, 0.98);
  transform: translateY(-6px);
  box-shadow: var(--shadow-2xl);
}

/* Premium Medical Button Styles */
.btn-primary {
  background: var(--gradient-medical);
  color: white;
  border: none;
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-medical);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medical-lg);
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:active {
  transform: translateY(0) scale(0.98);
}

.btn-secondary {
  background: var(--medical-white);
  color: var(--primary-medical);
  border: 2px solid var(--primary-medical-200);
  padding: 0.875rem 2rem;
  border-radius: 12px;
  font-weight: 600;
  font-size: 0.875rem;
  letter-spacing: 0.025em;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover {
  background: var(--primary-medical);
  color: white;
  border-color: var(--primary-medical);
  transform: translateY(-2px) scale(1.02);
  box-shadow: var(--shadow-medical);
}

.btn-secondary:active {
  transform: translateY(0) scale(0.98);
}

/* Floating Action Button */
.btn-fab {
  background: var(--gradient-medical);
  color: white;
  border: none;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--shadow-lg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  z-index: 50;
}

.btn-fab:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--shadow-2xl);
}

/* Premium Medical Form Styles */
.form-input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid var(--medical-border);
  border-radius: 12px;
  font-size: 0.875rem;
  font-weight: 400;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--medical-white);
  color: var(--neutral-800);
  position: relative;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-medical);
  box-shadow: 0 0 0 4px var(--primary-medical-50);
  transform: translateY(-1px);
}

.form-input:hover {
  border-color: var(--primary-medical-200);
}

.form-input.error {
  border-color: var(--error-red);
  box-shadow: 0 0 0 4px rgba(239, 68, 68, 0.1);
}

.form-input.success {
  border-color: var(--success-emerald);
  box-shadow: 0 0 0 4px rgba(16, 185, 129, 0.1);
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--neutral-700);
  margin-bottom: 0.5rem;
  letter-spacing: 0.025em;
}

.form-label.required::after {
  content: ' *';
  color: var(--error-red);
  font-weight: 500;
}

/* Floating Label Effect */
.form-group {
  position: relative;
  margin-bottom: 1.5rem;
}

.form-input-floating {
  width: 100%;
  padding: 1.25rem 1.25rem 0.75rem 1.25rem;
  border: 2px solid var(--medical-border);
  border-radius: 12px;
  font-size: 0.875rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--medical-white);
  color: var(--neutral-800);
}

.form-label-floating {
  position: absolute;
  left: 1.25rem;
  top: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--neutral-500);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  background: var(--medical-white);
  padding: 0 0.25rem;
}

.form-input-floating:focus + .form-label-floating,
.form-input-floating:not(:placeholder-shown) + .form-label-floating {
  top: -0.5rem;
  left: 1rem;
  font-size: 0.75rem;
  color: var(--primary-medical);
  font-weight: 600;
}

.form-input-floating:focus {
  outline: none;
  border-color: var(--primary-medical);
  box-shadow: 0 0 0 4px var(--primary-medical-50);
}

/* Medical Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.status-active {
  background: rgba(5, 150, 105, 0.1);
  color: var(--success-green);
}

.status-inactive {
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-500);
}

.status-warning {
  background: rgba(217, 119, 6, 0.1);
  color: var(--warning-amber);
}

.status-error {
  background: rgba(220, 38, 38, 0.1);
  color: var(--error-red);
}

/* Premium Animation Classes */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-down {
  animation: slideDown 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-left {
  animation: slideLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-right {
  animation: slideRight 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.scale-in {
  animation: scaleIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.float {
  animation: float 3s ease-in-out infinite;
}

.pulse-medical {
  animation: pulseMedical 2s ease-in-out infinite;
}

.shimmer {
  animation: shimmer 2s linear infinite;
}

/* Keyframe Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulseMedical {
  0%, 100% {
    box-shadow: 0 0 0 0 rgba(0, 102, 204, 0.4);
  }
  50% {
    box-shadow: 0 0 0 20px rgba(0, 102, 204, 0);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Gradient Animation */
@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

.gradient-animate {
  background: linear-gradient(-45deg, var(--primary-medical), var(--secondary-teal), var(--accent-purple), var(--primary-medical-light));
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
}

/* Loading Spinner */
.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--gray-200);
  border-top: 2px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Medical Grid Layout */
.medical-grid {
  display: grid;
  gap: 1.5rem;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

/* Responsive Design */
@media (max-width: 768px) {
  .medical-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .btn-primary,
  .btn-secondary {
    padding: 0.625rem 1.25rem;
    font-size: 0.875rem;
  }
}
