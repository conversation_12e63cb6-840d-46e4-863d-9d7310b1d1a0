{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 78, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/lib/database.ts"], "sourcesContent": ["// Database connection using direct PostgreSQL connection\nimport { Pool } from 'pg';\n\nconst pool = new Pool({\n  connectionString: process.env.DATABASE_URL,\n  ssl: {\n    rejectUnauthorized: false\n  }\n});\n\nexport { pool };\n\n// Database helper functions\nexport const db = {\n  query: async (text: string, params?: any[]) => {\n    const client = await pool.connect();\n    try {\n      const result = await client.query(text, params);\n      return result;\n    } finally {\n      client.release();\n    }\n  },\n  \n  getClient: async () => {\n    return await pool.connect();\n  }\n};\n\n// Test database connection\nexport const testConnection = async () => {\n  try {\n    const result = await db.query('SELECT NOW()');\n    console.log('Database connected successfully:', result.rows[0]);\n    return true;\n  } catch (error) {\n    console.error('Database connection failed:', error);\n    return false;\n  }\n};\n"], "names": [], "mappings": "AAAA,yDAAyD;;;;;;AACzD;;;;;;AAEA,MAAM,OAAO,IAAI,oGAAA,CAAA,OAAI,CAAC;IACpB,kBAAkB,QAAQ,GAAG,CAAC,YAAY;IAC1C,KAAK;QACH,oBAAoB;IACtB;AACF;;AAKO,MAAM,KAAK;IAChB,OAAO,OAAO,MAAc;QAC1B,MAAM,SAAS,MAAM,KAAK,OAAO;QACjC,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,MAAM;YACxC,OAAO;QACT,SAAU;YACR,OAAO,OAAO;QAChB;IACF;IAEA,WAAW;QACT,OAAO,MAAM,KAAK,OAAO;IAC3B;AACF;AAGO,MAAM,iBAAiB;IAC5B,IAAI;QACF,MAAM,SAAS,MAAM,GAAG,KAAK,CAAC;QAC9B,QAAQ,GAAG,CAAC,oCAAoC,OAAO,IAAI,CAAC,EAAE;QAC9D,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,+BAA+B;QAC7C,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 128, "column": 0}, "map": {"version": 3, "sources": ["file:///G:/project_V/dr-satya-liver-gastro-care/src/app/api/patients/%5Bid%5D/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { pool } from '@/lib/database';\n\n// GET - Fetch single patient by ID\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = params;\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Patient ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const client = await pool.connect();\n    \n    try {\n      const query = `\n        SELECT \n          id,\n          first_name,\n          middle_name,\n          last_name,\n          date_of_birth,\n          gender,\n          aadhar_number,\n          mobile_number,\n          alternate_phone,\n          email,\n          instagram_id,\n          facebook_id,\n          referred_by,\n          house_number,\n          village,\n          post_office,\n          address,\n          city,\n          state,\n          pincode,\n          country,\n          marital_status,\n          occupation,\n          education,\n          religion,\n          nationality,\n          height,\n          weight,\n          bmi,\n          height_unit,\n          weight_unit,\n          blood_group,\n          charlson_index,\n          asa_grade,\n          ecog_grade,\n          dm_status,\n          htn_status,\n          hyperlipidemia_status,\n          hypothyroid_status,\n          cardiac_disease_status,\n          pulmonary_disease_status,\n          neurological_disease_status,\n          rheumatological_disease_status,\n          other_diseases,\n          date_of_visit,\n          primary_disease,\n          symptoms,\n          clinical_examination,\n          investigations,\n          final_diagnosis,\n          ultrasonography_date,\n          ultrasonography_findings,\n          cect_date,\n          cect_findings,\n          endoscopy_date,\n          endoscopy_findings,\n          biopsy_date,\n          biopsy_findings,\n          colonoscopy_date,\n          colonoscopy_findings,\n          colonoscopic_biopsy_date,\n          colonoscopic_biopsy_findings,\n          pet_ct_findings,\n          other_biopsy_date,\n          other_biopsy_findings,\n          medications,\n          primary_treatment_plan,\n          admission_date,\n          surgery_plan_date,\n          surgery_name,\n          surgery_risks,\n          consent_obtained,\n          surgery_date,\n          surgeon,\n          assistant_surgeon,\n          ot_findings,\n          ot_procedure,\n          hospital_course,\n          complications,\n          clavien_dindo_grade,\n          discharge_date,\n          discharge_medications,\n          discharge_advice,\n          next_follow_up_date,\n          conservative_treatment,\n          icu_stay,\n          hospital_stay,\n          plan_for_surgery,\n          surgery_plan_date_conservative,\n          final_biopsy,\n          disease_stage,\n          chemotherapy_radiotherapy,\n          further_management_plan,\n          medication_1,\n          medication_2,\n          medication_3,\n          medication_4,\n          medication_5,\n          comorbidities,\n          allergies,\n          habits,\n          current_medications,\n          family_history,\n          social_history,\n          notes,\n          is_pregnant,\n          is_breastfeeding,\n          has_insurance,\n          insurance_provider,\n          policy_number,\n          profile_image_url,\n          created_by,\n          created_at,\n          updated_at\n        FROM patients \n        WHERE id = $1\n      `;\n\n      const result = await client.query(query, [id]);\n      \n      if (result.rows.length === 0) {\n        return NextResponse.json(\n          { error: 'Patient not found' },\n          { status: 404 }\n        );\n      }\n\n      const row = result.rows[0];\n      \n      // Transform data to match frontend expectations\n      const patient = {\n        id: row.id,\n        fullName: `${row.first_name} ${row.middle_name ? row.middle_name + ' ' : ''}${row.last_name}`.trim(),\n        patientId: row.id.substring(0, 8).toUpperCase(),\n        firstName: row.first_name,\n        middleName: row.middle_name,\n        lastName: row.last_name,\n        dateOfBirth: row.date_of_birth,\n        age: row.date_of_birth ? new Date().getFullYear() - new Date(row.date_of_birth).getFullYear() : null,\n        gender: row.gender,\n        aadharNumber: row.aadhar_number,\n        mobileNumber: row.mobile_number,\n        alternatePhone: row.alternate_phone,\n        email: row.email,\n        instagramId: row.instagram_id,\n        facebookId: row.facebook_id,\n        referredBy: row.referred_by,\n        houseNumber: row.house_number,\n        village: row.village,\n        postOffice: row.post_office,\n        address: row.address,\n        city: row.city,\n        state: row.state,\n        pincode: row.pincode,\n        country: row.country,\n        maritalStatus: row.marital_status,\n        occupation: row.occupation,\n        education: row.education,\n        religion: row.religion,\n        nationality: row.nationality,\n        height: row.height,\n        weight: row.weight,\n        bmi: row.bmi,\n        heightUnit: row.height_unit,\n        weightUnit: row.weight_unit,\n        bloodGroup: row.blood_group,\n        charlsonIndex: row.charlson_index,\n        asaGrade: row.asa_grade,\n        ecogGrade: row.ecog_grade,\n        dmStatus: row.dm_status,\n        htnStatus: row.htn_status,\n        hyperlipidemiaStatus: row.hyperlipidemia_status,\n        hypothyroidStatus: row.hypothyroid_status,\n        cardiacDiseaseStatus: row.cardiac_disease_status,\n        pulmonaryDiseaseStatus: row.pulmonary_disease_status,\n        neurologicalDiseaseStatus: row.neurological_disease_status,\n        rheumatologicalDiseaseStatus: row.rheumatological_disease_status,\n        otherDiseases: row.other_diseases,\n        dateOfVisit: row.date_of_visit,\n        primaryDisease: row.primary_disease,\n        symptoms: row.symptoms,\n        clinicalExamination: row.clinical_examination,\n        investigations: row.investigations,\n        finalDiagnosis: row.final_diagnosis,\n        medications: row.medications,\n        primaryTreatmentPlan: row.primary_treatment_plan,\n        comorbidities: row.comorbidities,\n        allergies: row.allergies,\n        habits: row.habits,\n        currentMedications: row.current_medications,\n        familyHistory: row.family_history,\n        socialHistory: row.social_history,\n        notes: row.notes,\n        isPregnant: row.is_pregnant,\n        isBreastfeeding: row.is_breastfeeding,\n        hasInsurance: row.has_insurance,\n        insuranceProvider: row.insurance_provider,\n        policyNumber: row.policy_number,\n        profileImageUrl: row.profile_image_url,\n        createdBy: row.created_by,\n        createdAt: row.created_at,\n        updatedAt: row.updated_at\n      };\n\n      return NextResponse.json({\n        success: true,\n        data: patient\n      });\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Error fetching patient:', error);\n    return NextResponse.json(\n      { error: 'Failed to fetch patient' },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT - Update patient\nexport async function PUT(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = params;\n    const patientData = await request.json();\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Patient ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const client = await pool.connect();\n    \n    try {\n      // Build dynamic update query based on provided fields\n      const updateFields = [];\n      const values = [];\n      let paramCount = 1;\n\n      // Add fields that can be updated\n      const updatableFields = [\n        'first_name', 'middle_name', 'last_name', 'date_of_birth', 'gender',\n        'mobile_number', 'email', 'address', 'height', 'weight', 'blood_group',\n        'charlson_index', 'asa_grade', 'ecog_grade', 'occupation', 'marital_status',\n        'city', 'state', 'pincode', 'notes'\n      ];\n\n      for (const field of updatableFields) {\n        const camelCaseField = field.replace(/_([a-z])/g, (g) => g[1].toUpperCase());\n        if (patientData[camelCaseField] !== undefined) {\n          updateFields.push(`${field} = $${paramCount}`);\n          values.push(patientData[camelCaseField]);\n          paramCount++;\n        }\n      }\n\n      if (updateFields.length === 0) {\n        return NextResponse.json(\n          { error: 'No valid fields to update' },\n          { status: 400 }\n        );\n      }\n\n      // Add updated_at\n      updateFields.push(`updated_at = $${paramCount}`);\n      values.push(new Date().toISOString());\n      paramCount++;\n\n      // Add ID for WHERE clause\n      values.push(id);\n\n      const query = `\n        UPDATE patients \n        SET ${updateFields.join(', ')}\n        WHERE id = $${paramCount}\n        RETURNING *\n      `;\n\n      const result = await client.query(query, values);\n      \n      if (result.rows.length === 0) {\n        return NextResponse.json(\n          { error: 'Patient not found' },\n          { status: 404 }\n        );\n      }\n\n      return NextResponse.json({\n        success: true,\n        data: result.rows[0],\n        message: 'Patient updated successfully'\n      });\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Error updating patient:', error);\n    return NextResponse.json(\n      { error: 'Failed to update patient' },\n      { status: 500 }\n    );\n  }\n}\n\n// DELETE - Delete patient\nexport async function DELETE(\n  request: NextRequest,\n  { params }: { params: { id: string } }\n) {\n  try {\n    const { id } = params;\n\n    if (!id) {\n      return NextResponse.json(\n        { error: 'Patient ID is required' },\n        { status: 400 }\n      );\n    }\n\n    const client = await pool.connect();\n    \n    try {\n      const result = await client.query(\n        'DELETE FROM patients WHERE id = $1 RETURNING id',\n        [id]\n      );\n      \n      if (result.rows.length === 0) {\n        return NextResponse.json(\n          { error: 'Patient not found' },\n          { status: 404 }\n        );\n      }\n\n      return NextResponse.json({\n        success: true,\n        message: 'Patient deleted successfully'\n      });\n\n    } finally {\n      client.release();\n    }\n\n  } catch (error) {\n    console.error('Error deleting patient:', error);\n    return NextResponse.json(\n      { error: 'Failed to delete patient' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;;;;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,MAAM,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;MAsHf,CAAC;YAED,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,OAAO;gBAAC;aAAG;YAE7C,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;gBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,MAAM,MAAM,OAAO,IAAI,CAAC,EAAE;YAE1B,gDAAgD;YAChD,MAAM,UAAU;gBACd,IAAI,IAAI,EAAE;gBACV,UAAU,GAAG,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI,WAAW,GAAG,IAAI,WAAW,GAAG,MAAM,KAAK,IAAI,SAAS,EAAE,CAAC,IAAI;gBAClG,WAAW,IAAI,EAAE,CAAC,SAAS,CAAC,GAAG,GAAG,WAAW;gBAC7C,WAAW,IAAI,UAAU;gBACzB,YAAY,IAAI,WAAW;gBAC3B,UAAU,IAAI,SAAS;gBACvB,aAAa,IAAI,aAAa;gBAC9B,KAAK,IAAI,aAAa,GAAG,IAAI,OAAO,WAAW,KAAK,IAAI,KAAK,IAAI,aAAa,EAAE,WAAW,KAAK;gBAChG,QAAQ,IAAI,MAAM;gBAClB,cAAc,IAAI,aAAa;gBAC/B,cAAc,IAAI,aAAa;gBAC/B,gBAAgB,IAAI,eAAe;gBACnC,OAAO,IAAI,KAAK;gBAChB,aAAa,IAAI,YAAY;gBAC7B,YAAY,IAAI,WAAW;gBAC3B,YAAY,IAAI,WAAW;gBAC3B,aAAa,IAAI,YAAY;gBAC7B,SAAS,IAAI,OAAO;gBACpB,YAAY,IAAI,WAAW;gBAC3B,SAAS,IAAI,OAAO;gBACpB,MAAM,IAAI,IAAI;gBACd,OAAO,IAAI,KAAK;gBAChB,SAAS,IAAI,OAAO;gBACpB,SAAS,IAAI,OAAO;gBACpB,eAAe,IAAI,cAAc;gBACjC,YAAY,IAAI,UAAU;gBAC1B,WAAW,IAAI,SAAS;gBACxB,UAAU,IAAI,QAAQ;gBACtB,aAAa,IAAI,WAAW;gBAC5B,QAAQ,IAAI,MAAM;gBAClB,QAAQ,IAAI,MAAM;gBAClB,KAAK,IAAI,GAAG;gBACZ,YAAY,IAAI,WAAW;gBAC3B,YAAY,IAAI,WAAW;gBAC3B,YAAY,IAAI,WAAW;gBAC3B,eAAe,IAAI,cAAc;gBACjC,UAAU,IAAI,SAAS;gBACvB,WAAW,IAAI,UAAU;gBACzB,UAAU,IAAI,SAAS;gBACvB,WAAW,IAAI,UAAU;gBACzB,sBAAsB,IAAI,qBAAqB;gBAC/C,mBAAmB,IAAI,kBAAkB;gBACzC,sBAAsB,IAAI,sBAAsB;gBAChD,wBAAwB,IAAI,wBAAwB;gBACpD,2BAA2B,IAAI,2BAA2B;gBAC1D,8BAA8B,IAAI,8BAA8B;gBAChE,eAAe,IAAI,cAAc;gBACjC,aAAa,IAAI,aAAa;gBAC9B,gBAAgB,IAAI,eAAe;gBACnC,UAAU,IAAI,QAAQ;gBACtB,qBAAqB,IAAI,oBAAoB;gBAC7C,gBAAgB,IAAI,cAAc;gBAClC,gBAAgB,IAAI,eAAe;gBACnC,aAAa,IAAI,WAAW;gBAC5B,sBAAsB,IAAI,sBAAsB;gBAChD,eAAe,IAAI,aAAa;gBAChC,WAAW,IAAI,SAAS;gBACxB,QAAQ,IAAI,MAAM;gBAClB,oBAAoB,IAAI,mBAAmB;gBAC3C,eAAe,IAAI,cAAc;gBACjC,eAAe,IAAI,cAAc;gBACjC,OAAO,IAAI,KAAK;gBAChB,YAAY,IAAI,WAAW;gBAC3B,iBAAiB,IAAI,gBAAgB;gBACrC,cAAc,IAAI,aAAa;gBAC/B,mBAAmB,IAAI,kBAAkB;gBACzC,cAAc,IAAI,aAAa;gBAC/B,iBAAiB,IAAI,iBAAiB;gBACtC,WAAW,IAAI,UAAU;gBACzB,WAAW,IAAI,UAAU;gBACzB,WAAW,IAAI,UAAU;YAC3B;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM;YACR;QAEF,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA0B,GACnC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG;QACf,MAAM,cAAc,MAAM,QAAQ,IAAI;QAEtC,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,sDAAsD;YACtD,MAAM,eAAe,EAAE;YACvB,MAAM,SAAS,EAAE;YACjB,IAAI,aAAa;YAEjB,iCAAiC;YACjC,MAAM,kBAAkB;gBACtB;gBAAc;gBAAe;gBAAa;gBAAiB;gBAC3D;gBAAiB;gBAAS;gBAAW;gBAAU;gBAAU;gBACzD;gBAAkB;gBAAa;gBAAc;gBAAc;gBAC3D;gBAAQ;gBAAS;gBAAW;aAC7B;YAED,KAAK,MAAM,SAAS,gBAAiB;gBACnC,MAAM,iBAAiB,MAAM,OAAO,CAAC,aAAa,CAAC,IAAM,CAAC,CAAC,EAAE,CAAC,WAAW;gBACzE,IAAI,WAAW,CAAC,eAAe,KAAK,WAAW;oBAC7C,aAAa,IAAI,CAAC,GAAG,MAAM,IAAI,EAAE,YAAY;oBAC7C,OAAO,IAAI,CAAC,WAAW,CAAC,eAAe;oBACvC;gBACF;YACF;YAEA,IAAI,aAAa,MAAM,KAAK,GAAG;gBAC7B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAA4B,GACrC;oBAAE,QAAQ;gBAAI;YAElB;YAEA,iBAAiB;YACjB,aAAa,IAAI,CAAC,CAAC,cAAc,EAAE,YAAY;YAC/C,OAAO,IAAI,CAAC,IAAI,OAAO,WAAW;YAClC;YAEA,0BAA0B;YAC1B,OAAO,IAAI,CAAC;YAEZ,MAAM,QAAQ,CAAC;;YAET,EAAE,aAAa,IAAI,CAAC,MAAM;oBAClB,EAAE,WAAW;;MAE3B,CAAC;YAED,MAAM,SAAS,MAAM,OAAO,KAAK,CAAC,OAAO;YAEzC,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;gBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,MAAM,OAAO,IAAI,CAAC,EAAE;gBACpB,SAAS;YACX;QAEF,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,OACpB,OAAoB,EACpB,EAAE,MAAM,EAA8B;IAEtC,IAAI;QACF,MAAM,EAAE,EAAE,EAAE,GAAG;QAEf,IAAI,CAAC,IAAI;YACP,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,SAAS,MAAM,wHAAA,CAAA,OAAI,CAAC,OAAO;QAEjC,IAAI;YACF,MAAM,SAAS,MAAM,OAAO,KAAK,CAC/B,mDACA;gBAAC;aAAG;YAGN,IAAI,OAAO,IAAI,CAAC,MAAM,KAAK,GAAG;gBAC5B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAoB,GAC7B;oBAAE,QAAQ;gBAAI;YAElB;YAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QAEF,SAAU;YACR,OAAO,OAAO;QAChB;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA2B,GACpC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}