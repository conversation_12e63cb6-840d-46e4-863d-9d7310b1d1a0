const { Pool } = require('pg');
const { v4: uuidv4 } = require('uuid');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: {
    rejectUnauthorized: false
  }
});

async function seedDatabase() {
  const client = await pool.connect();
  
  try {
    console.log('🔗 Connected to NeonDB');
    
    // Create test user
    const userId = uuidv4();
    const userResult = await client.query(`
      INSERT INTO users (id, email, first_name, last_name, role, is_active, created_at, updated_at) 
      VALUES ($1, $2, $3, $4, $5, $6, $7, $8) 
      ON CONFLICT (email) DO UPDATE SET
        first_name = EXCLUDED.first_name,
        last_name = EXCLUDED.last_name,
        updated_at = EXCLUDED.updated_at
      RETURNING id, email, first_name, last_name;
    `, [
      userId,
      '<EMAIL>',
      'Dr. <PERSON>',
      '<PERSON>',
      'doctor',
      true,
      new Date().toISOString(),
      new Date().toISOString()
    ]);
    
    console.log('👤 Test user created/updated:', userResult.rows[0]);
    const doctorId = userResult.rows[0].id;
    
    // Check if patients already exist
    const existingPatients = await client.query('SELECT COUNT(*) FROM patients;');
    const patientCount = parseInt(existingPatients.rows[0].count);
    
    if (patientCount > 0) {
      console.log(`👥 ${patientCount} patients already exist in database`);
      return;
    }
    
    console.log('📝 Creating sample patients...');
    
    const samplePatients = [
      {
        firstName: 'John',
        middleName: 'Kumar',
        lastName: 'Doe',
        dateOfBirth: '1978-05-15',
        gender: 'Male',
        aadharNumber: '123456789012',
        mobileNumber: '**********',
        email: '<EMAIL>',
        address: '123 Main Street, Andheri West, Mumbai, Maharashtra 400058',
        height: 175,
        weight: 75,
        bloodGroup: 'O+',
        charlsonIndex: 2,
        asaGrade: '2',
        ecogGrade: '0',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400058',
        occupation: 'Software Engineer',
        maritalStatus: 'Married'
      },
      {
        firstName: 'Sarah',
        middleName: null,
        lastName: 'Smith',
        dateOfBirth: '1992-08-22',
        gender: 'Female',
        aadharNumber: '123456789013',
        mobileNumber: '**********',
        email: '<EMAIL>',
        address: '456 Park Avenue, Connaught Place, New Delhi, Delhi 110001',
        height: 165,
        weight: 60,
        bloodGroup: 'A+',
        charlsonIndex: 0,
        asaGrade: '1',
        ecogGrade: '0',
        city: 'New Delhi',
        state: 'Delhi',
        pincode: '110001',
        occupation: 'Teacher',
        maritalStatus: 'Single'
      },
      {
        firstName: 'Mike',
        middleName: 'Raj',
        lastName: 'Johnson',
        dateOfBirth: '1965-12-10',
        gender: 'Male',
        aadharNumber: '123456789014',
        mobileNumber: '9876543212',
        email: '<EMAIL>',
        address: '789 Oak Street, Koramangala, Bangalore, Karnataka 560034',
        height: 180,
        weight: 85,
        bloodGroup: 'B+',
        charlsonIndex: 4,
        asaGrade: '3',
        ecogGrade: '1',
        city: 'Bangalore',
        state: 'Karnataka',
        pincode: '560034',
        occupation: 'Business Owner',
        maritalStatus: 'Married'
      },
      {
        firstName: 'Emily',
        middleName: 'Priya',
        lastName: 'Davis',
        dateOfBirth: '1996-03-18',
        gender: 'Female',
        aadharNumber: '123456789015',
        mobileNumber: '9876543213',
        email: '<EMAIL>',
        address: '321 Rose Garden, Banjara Hills, Hyderabad, Telangana 500034',
        height: 160,
        weight: 55,
        bloodGroup: 'AB+',
        charlsonIndex: 0,
        asaGrade: '1',
        ecogGrade: '0',
        city: 'Hyderabad',
        state: 'Telangana',
        pincode: '500034',
        occupation: 'Graphic Designer',
        maritalStatus: 'Single'
      },
      {
        firstName: 'Robert',
        middleName: 'Singh',
        lastName: 'Wilson',
        dateOfBirth: '1957-09-25',
        gender: 'Male',
        aadharNumber: '123456789016',
        mobileNumber: '9876543214',
        email: '<EMAIL>',
        address: '654 Palm Street, Sector 17, Chandigarh, Punjab 160017',
        height: 170,
        weight: 80,
        bloodGroup: 'O-',
        charlsonIndex: 6,
        asaGrade: '4',
        ecogGrade: '2',
        city: 'Chandigarh',
        state: 'Punjab',
        pincode: '160017',
        occupation: 'Retired',
        maritalStatus: 'Married'
      },
      {
        firstName: 'Lisa',
        middleName: 'Devi',
        lastName: 'Anderson',
        dateOfBirth: '1983-11-07',
        gender: 'Female',
        aadharNumber: '123456789017',
        mobileNumber: '**********',
        email: '<EMAIL>',
        address: '987 Lotus Lane, Anna Nagar, Chennai, Tamil Nadu 600040',
        height: 168,
        weight: 65,
        bloodGroup: 'A-',
        charlsonIndex: 1,
        asaGrade: '2',
        ecogGrade: '0',
        city: 'Chennai',
        state: 'Tamil Nadu',
        pincode: '600040',
        occupation: 'Marketing Manager',
        maritalStatus: 'Married'
      }
    ];
    
    for (const patient of samplePatients) {
      const patientId = uuidv4();
      
      // Calculate BMI
      const heightInMeters = patient.height / 100;
      const bmi = patient.weight / (heightInMeters * heightInMeters);
      
      const result = await client.query(`
        INSERT INTO patients (
          id, first_name, middle_name, last_name, date_of_birth, gender,
          aadhar_number, mobile_number, email, address, height, weight, bmi,
          blood_group, charlson_index, asa_grade, ecog_grade, city, state, pincode,
          occupation, marital_status, created_by, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21, $22, $23, $24, $25
        ) RETURNING id, first_name, last_name;
      `, [
        patientId,
        patient.firstName,
        patient.middleName,
        patient.lastName,
        patient.dateOfBirth,
        patient.gender,
        patient.aadharNumber,
        patient.mobileNumber,
        patient.email,
        patient.address,
        patient.height,
        patient.weight,
        bmi.toFixed(2),
        patient.bloodGroup,
        patient.charlsonIndex,
        patient.asaGrade,
        patient.ecogGrade,
        patient.city,
        patient.state,
        patient.pincode,
        patient.occupation,
        patient.maritalStatus,
        doctorId,
        new Date().toISOString(),
        new Date().toISOString()
      ]);
      
      console.log(`  ✅ Created patient: ${result.rows[0].first_name} ${result.rows[0].last_name} (${result.rows[0].id.substring(0, 8)})`);
    }
    
    // Final counts
    const finalPatients = await client.query('SELECT COUNT(*) FROM patients;');
    const finalUsers = await client.query('SELECT COUNT(*) FROM users;');
    
    console.log('\n🎉 Database Seeding Complete:');
    console.log(`  👥 Total Patients: ${finalPatients.rows[0].count}`);
    console.log(`  👤 Total Users: ${finalUsers.rows[0].count}`);
    
  } catch (error) {
    console.error('❌ Error seeding database:', error);
    throw error;
  } finally {
    client.release();
    await pool.end();
  }
}

// Run the seeding
seedDatabase().catch(console.error);
